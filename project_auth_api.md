# 🔐 External Project Integration Guide

This guide explains how external projects can securely connect to the NWA Alliance API for user authentication and permissions management.

## 🚀 Quick Start

### 1. Obtain Credentials
Contact your NWA Alliance administrator to obtain:
- `API Key` - Unique identifier for your project
- `JWT Public Key` - For verifying JWT tokens
- `Allowed Origins` - Domains permitted to access the API

### 2. Environment Configuration
Store these values in your project's `.env` file:

```env
# External Project Environment Variables
ADMIN_API_BASE_URL=https://api.nwa.org
ADMIN_API_KEY=nwa_prj_abc123xyz
ADMIN_JWT_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----...
```

## 🔌 Connection Flow

### Step 1: API Key Authentication
Authenticate your project with the API key:

```javascript
// JavaScript/Node.js example
const axios = require('axios');

// Configuration from environment
const API_BASE_URL = process.env.ADMIN_API_BASE_URL;
const API_KEY = process.env.ADMIN_API_KEY;

// Authenticate with API key
async function authenticateProject() {
  try {
    const response = await axios.post(`${API_BASE_URL}/api/auth/external/token`, {
      // Optional: Specify user context
      // userId: 'user-123'
    }, {
      headers: {
        'x-api-key': API_KEY,
        'Content-Type': 'application/json'
      }
    });
    
    const { token, expiresIn } = response.data;
    console.log('JWT token generated successfully');
    return token;
  } catch (error) {
    console.error('Authentication failed:', error.response?.data || error.message);
    throw error;
  }
}
```

### Step 2: JWT Token Usage
Use the JWT token for authenticated requests:

```javascript
// Make authenticated API requests
async function fetchUserPermissions(userId, projectId, jwtToken) {
  try {
    const response = await axios.get(
      `${API_BASE_URL}/api/admin/users/${userId}/projects/${projectId}`,
      {
        headers: {
          'x-api-key': API_KEY,
          'Authorization': `Bearer ${jwtToken}`,
          'Content-Type': 'application/json'
        }
      }
    );
    
    const { user, project, scopes } = response.data;
    console.log('User permissions:', scopes);
    return { user, project, scopes };
  } catch (error) {
    console.error('Failed to fetch user permissions:', error.response?.data || error.message);
    throw error;
  }
}
```

### Step 3: Scope Validation
Check if a user has required permissions:

```javascript
// Validate user scopes
function hasRequiredScopes(userScopes, requiredScopes) {
  // Check if user has all required scopes
  return requiredScopes.every(scope => 
    userScopes.some(userScope => 
      userScope.name === scope || 
      userScope.name === 'admin:*' || // Admin has all scopes
      (scope.endsWith(':read') && userScope.name === 'admin:read') || // Specific admin read scope
      (scope.endsWith(':write') && userScope.name === 'admin:write') // Specific admin write scope
    )
  );
}

// Example usage
async function checkUserAccess(userId, projectId, requiredScopes = ['users:read']) {
  try {
    // Step 1: Authenticate project
    const jwtToken = await authenticateProject();
    
    // Step 2: Fetch user permissions
    const { user, project, scopes } = await fetchUserPermissions(userId, projectId, jwtToken);
    
    // Step 3: Validate scopes
    const hasAccess = hasRequiredScopes(scopes, requiredScopes);
    
    if (hasAccess) {
      console.log(`User ${user.name} has access to ${project.name}`);
      return { authorized: true, user, project, scopes };
    } else {
      console.log(`User ${user.name} lacks required permissions`);
      return { authorized: false, requiredScopes, grantedScopes: scopes };
    }
  } catch (error) {
    console.error('Access check failed:', error.message);
    return { authorized: false, error: error.message };
  }
}
```

## 🛠️ Complete Integration Example

### Python Implementation

```python
import os
import requests
import jwt
from typing import Dict, List, Optional

class NWAExternalClient:
    def __init__(self):
        self.base_url = os.getenv('ADMIN_API_BASE_URL')
        self.api_key = os.getenv('ADMIN_API_KEY')
        self.jwt_public_key = os.getenv('ADMIN_JWT_PUBLIC_KEY')
        
        # Session for connection reuse
        self.session = requests.Session()
        self.session.headers.update({
            'x-api-key': self.api_key,
            'Content-Type': 'application/json'
        })
    
    def authenticate(self, user_id: Optional[str] = None) -> str:
        """Generate JWT token for project authentication"""
        url = f"{self.base_url}/api/auth/external/token"
        
        payload = {}
        if user_id:
            payload['userId'] = user_id
            
        try:
            response = self.session.post(url, json=payload)
            response.raise_for_status()
            
            data = response.json()
            return data['token']
        except requests.exceptions.RequestException as e:
            raise Exception(f"Authentication failed: {str(e)}")
    
    def get_user_permissions(self, user_id: str, project_id: str, token: str) -> Dict:
        """Fetch user permissions for a specific project"""
        url = f"{self.base_url}/api/admin/users/{user_id}/projects/{project_id}"
        
        headers = {
            'Authorization': f'Bearer {token}',
            **self.session.headers
        }
        
        try:
            response = requests.get(url, headers=headers)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            raise Exception(f"Failed to fetch user permissions: {str(e)}")
    
    def validate_token(self, token: str) -> Dict:
        """Validate JWT token signature and claims"""
        try {
            // Decode and verify JWT token
            payload = jwt.decode(
                token, 
                self.jwt_public_key, 
                algorithms=['RS256'],
                audience='nwa-external'  // Adjust based on your setup
            );
            return {'valid': True, 'payload': payload};
        } catch (jwt.InvalidTokenError $e) {
            return {'valid': False, 'error': str($e)};
        }
    }

// Usage example
function main() {
    $client = new NWAExternalClient();
    
    try {
        // Authenticate project
        $jwtToken = $client->authenticate();
        echo "✅ Project authenticated successfully\n";
        
        // Get user permissions
        $userInfo = $client->get_user_permissions(
            userId: 'user-123',
            projectId: 'project-456',
            token: $jwtToken
        );
        
        echo "👤 User: " . $userInfo['user']['name'] . "\n";
        echo "🏢 Project: " . $userInfo['project']['name'] . "\n";
        echo "🔓 Scopes: ";
        foreach ($userInfo['scopes'] as $scope) {
            echo $scope['name'] . " ";
        }
        echo "\n";
        
        // Validate token
        $tokenValidation = $client->validateToken($jwtToken);
        if ($tokenValidation['valid']) {
            echo "✅ Token validation successful\n";
        } else {
            echo "❌ Token validation failed: " . $tokenValidation['error'] . "\n";
        }
            
    } catch (Exception $e) {
        echo "❌ Integration failed: " . $e->getMessage() . "\n";
    }
}

if (basename(__FILE__) == basename($_SERVER['SCRIPT_FILENAME'])) {
    main();
}
```

### PHP Implementation

```php
<?php

class NWAExternalClient {
    private $baseUrl;
    private $apiKey;
    private $jwtPublicKey;
    
    public function __construct() {
        $this->baseUrl = getenv('ADMIN_API_BASE_URL');
        $this->apiKey = getenv('ADMIN_API_KEY');
        $this->jwtPublicKey = getenv('ADMIN_JWT_PUBLIC_KEY');
    }
    
    /**
     * Authenticate project and generate JWT token
     */
    public function authenticate($userId = null) {
        $url = $this->baseUrl . '/api/auth/external/token';
        
        $payload = [];
        if ($userId) {
            $payload['userId'] = $userId;
        }
        
        $headers = [
            'x-api-key: ' . $this->apiKey,
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($payload));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if ($httpCode !== 200) {
            throw new Exception("Authentication failed: HTTP $httpCode");
        }
        
        $data = json_decode($response, true);
        curl_close($ch);
        
        return $data['token'];
    }
    
    /**
     * Fetch user permissions for a specific project
     */
    public function getUserPermissions($userId, $projectId, $token) {
        $url = $this->baseUrl . "/api/admin/users/{$userId}/projects/{$projectId}";
        
        $headers = [
            'Authorization: Bearer ' . $token,
            'x-api-key: ' . $this->apiKey,
            'Content-Type: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        
        if ($httpCode !== 200) {
            throw new Exception("Failed to fetch user permissions: HTTP $httpCode");
        }
        
        $data = json_decode($response, true);
        curl_close($ch);
        
        return $data;
    }
    
    /**
     * Validate JWT token
     */
    public function validateToken($token) {
        // In a real implementation, you would validate the JWT token
        // using a JWT library like firebase/php-jwt
        // This is a simplified example
        
        try {
            // Split the token
            $parts = explode('.', $token);
            if (count($parts) !== 3) {
                return ['valid' => false, 'error' => 'Invalid token format'];
            }
            
            // In a real implementation, you would verify the signature
            // and validate the claims
            return ['valid' => true, 'message' => 'Token appears valid'];
        } catch (Exception $e) {
            return ['valid' => false, 'error' => $e->getMessage()];
        }
    }
}

// Usage example
try {
    $client = new NWAExternalClient();
    
    // Authenticate project
    $jwtToken = $client->authenticate();
    echo "✅ Project authenticated successfully\n";
    
    // Get user permissions
    $userInfo = $client->getUserPermissions('user-123', 'project-456', $jwtToken);
    
    echo "👤 User: " . $userInfo['user']['name'] . "\n";
    echo "🏢 Project: " . $userInfo['project']['name'] . "\n";
    echo "🔓 Scopes: ";
    foreach ($userInfo['scopes'] as $scope) {
        echo $scope['name'] . " ";
    }
    echo "\n";
    
    // Validate token
    $tokenValidation = $client->validateToken($jwtToken);
    if ($tokenValidation['valid']) {
        echo "✅ Token validation successful\n";
    } else {
        echo "❌ Token validation failed: " . $tokenValidation['error'] . "\n";
    }
    
} catch (Exception $e) {
    echo "❌ Integration failed: " . $e->getMessage() . "\n";
}

?>
```

## 🔒 Security Best Practices

### 1. Environment Variables
Never hardcode credentials in your source code:

```bash
# ✅ Good - Use environment variables
ADMIN_API_KEY=nwa_prj_abc123xyz

# ❌ Bad - Never hardcode credentials
const API_KEY = 'nwa_prj_abc123xyz';
```

### 2. Token Management
Handle JWT tokens securely:

```javascript
// Store tokens securely (not in localStorage for web apps)
const token = await authenticateProject();

// Set appropriate expiration handling
setTimeout(() => {
  console.log('Token expired, re-authenticating...');
  // Re-authenticate to get new token
}, expiresIn * 1000); // Convert seconds to milliseconds
```

### 3. Error Handling
Never expose sensitive information in error messages:

```javascript
// ✅ Good - Generic error messages
try {
  await authenticateProject();
} catch (error) {
  console.error('Authentication failed. Please check your credentials.');
  // Log detailed errors server-side only
}

// ❌ Bad - Exposing internal details
console.error('Database connection failed:', error.stack);
```

## 📋 Required Scopes

Common permission scopes you may encounter:

| Scope | Description |
|-------|-------------|
| `users:read` | Read user information |
| `users:write` | Modify user information |
| `projects:read` | Read project information |
| `projects:write` | Modify project information |
| `admin:*` | Full administrative access |
| `service:access` | Service-to-service communication |

## 🆘 Troubleshooting

### Common Issues

1. **401 Unauthorized**
   - Check API key validity
   - Verify JWT token hasn't expired
   - Confirm allowed origins configuration

2. **403 Forbidden**
   - Verify user has required scopes
   - Check project permissions
   - Ensure user-project relationship exists

3. **404 Not Found**
   - Verify user/project IDs are correct
   - Check if resources exist in the system

### Debugging Steps

```bash
# 1. Test API key validity
curl -X POST https://api.nwa.org/api/auth/external/validate \
  -H "x-api-key: YOUR_API_KEY" \
  -H "Content-Type: application/json"

# 2. Test JWT token generation
curl -X POST https://api.nwa.org/api/auth/external/token \
  -H "x-api-key: YOUR_API_KEY" \
  -H "Content-Type: application/json"

# 3. Test user permissions
curl -X GET https://api.nwa.org/api/admin/users/USER_ID/projects/PROJECT_ID \
  -H "x-api-key: YOUR_API_KEY" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN"
```

## 📞 Support

For integration issues or to request new API keys:
- Contact: **<EMAIL>**
- Hours: Monday-Friday, 9AM-5PM EST
- Emergency: **+1-800-NWA-HELP**

---
*Last Updated: August 2025*