--
-- PostgreSQL database dump
--

\restrict scNJxveAjrakLrKUV5ABpiUaKYhzUyCZ0UHgdap4ZjtfN1WiDIaIAdBhmzEMOyh

-- Dumped from database version 15.14
-- Dumped by pg_dump version 15.14

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: BulkUploadStatus; Type: TYPE; Schema: public; Owner: nwa_user
--

CREATE TYPE public."BulkUploadStatus" AS ENUM (
    'PENDING',
    'APPROVED',
    'REJECTED'
);


ALTER TYPE public."BulkUploadStatus" OWNER TO nwa_user;

--
-- Name: OrdinanceStatus; Type: TYPE; Schema: public; Owner: nwa_user
--

CREATE TYPE public."OrdinanceStatus" AS ENUM (
    'PENDING',
    'IN_PROGRESS',
    'COMPLETED',
    'EXPIRED',
    'CANCELLED'
);


ALTER TYPE public."OrdinanceStatus" OWNER TO nwa_user;

--
-- Name: TreatyStatus; Type: TYPE; Schema: public; Owner: nwa_user
--

CREATE TYPE public."TreatyStatus" AS ENUM (
    'ACTIVE',
    'EXPIRED',
    'TERMINATED',
    'PENDING_RENEWAL'
);


ALTER TYPE public."TreatyStatus" OWNER TO nwa_user;

--
-- Name: UserStatus; Type: TYPE; Schema: public; Owner: nwa_user
--

CREATE TYPE public."UserStatus" AS ENUM (
    'ACTIVE',
    'INACTIVE',
    'SUSPENDED',
    'PENDING_VERIFICATION'
);


ALTER TYPE public."UserStatus" OWNER TO nwa_user;

SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: _prisma_migrations; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public._prisma_migrations (
    id character varying(36) NOT NULL,
    checksum character varying(64) NOT NULL,
    finished_at timestamp with time zone,
    migration_name character varying(255) NOT NULL,
    logs text,
    rolled_back_at timestamp with time zone,
    started_at timestamp with time zone DEFAULT now() NOT NULL,
    applied_steps_count integer DEFAULT 0 NOT NULL
);


ALTER TABLE public._prisma_migrations OWNER TO nwa_user;

--
-- Name: accounts; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.accounts (
    id text NOT NULL,
    user_id text NOT NULL,
    type text NOT NULL,
    provider text NOT NULL,
    provider_account_id text NOT NULL,
    refresh_token text,
    access_token text,
    expires_at integer,
    token_type text,
    scope text,
    id_token text,
    session_state text
);


ALTER TABLE public.accounts OWNER TO nwa_user;

--
-- Name: audit_logs; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.audit_logs (
    id text NOT NULL,
    user_id text,
    action text NOT NULL,
    resource text NOT NULL,
    resource_id text,
    old_values jsonb,
    new_values jsonb,
    ip_address text,
    user_agent text,
    "timestamp" timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    success boolean NOT NULL,
    status_code integer,
    error_message text,
    request_id text,
    duration integer,
    request_size integer,
    response_size integer,
    metadata jsonb,
    project_id text,
    api_endpoint text,
    request_method text,
    remote_server_id text
);


ALTER TABLE public.audit_logs OWNER TO nwa_user;

--
-- Name: authorization_codes; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.authorization_codes (
    id text NOT NULL,
    remote_server_id text NOT NULL,
    user_id text NOT NULL,
    code text NOT NULL,
    redirect_uri text,
    scope text,
    expires_at timestamp(3) without time zone NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    used_at timestamp(3) without time zone
);


ALTER TABLE public.authorization_codes OWNER TO nwa_user;

--
-- Name: file_attachments; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.file_attachments (
    id text NOT NULL,
    treaty_id text NOT NULL,
    file_name text NOT NULL,
    file_path text NOT NULL,
    mime_type text NOT NULL,
    file_size integer NOT NULL,
    uploaded_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.file_attachments OWNER TO nwa_user;

--
-- Name: oauth_tokens; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.oauth_tokens (
    id text NOT NULL,
    remote_server_id text NOT NULL,
    user_id text,
    access_token text NOT NULL,
    refresh_token text,
    token_type text DEFAULT 'Bearer'::text NOT NULL,
    scope text,
    expires_at timestamp(3) without time zone NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.oauth_tokens OWNER TO nwa_user;

--
-- Name: ordinance_types; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.ordinance_types (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    category text NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.ordinance_types OWNER TO nwa_user;

--
-- Name: ordinances; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.ordinances (
    id text NOT NULL,
    user_id text NOT NULL,
    ordinance_type_id text NOT NULL,
    status text DEFAULT 'PENDING'::text NOT NULL,
    completed_date timestamp(3) without time zone,
    expiration_date timestamp(3) without time zone,
    notes text,
    document_path text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.ordinances OWNER TO nwa_user;

--
-- Name: pending_bulk_uploads; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.pending_bulk_uploads (
    id text NOT NULL,
    uploader_id text NOT NULL,
    file_name text NOT NULL,
    record_count integer NOT NULL,
    data text NOT NULL,
    status text DEFAULT 'PENDING'::text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.pending_bulk_uploads OWNER TO nwa_user;

--
-- Name: permissions; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.permissions (
    id text NOT NULL,
    name text NOT NULL,
    resource text NOT NULL,
    action text NOT NULL,
    description text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.permissions OWNER TO nwa_user;

--
-- Name: positions; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.positions (
    id text NOT NULL,
    title text NOT NULL,
    description text,
    level integer DEFAULT 0 NOT NULL,
    parent_id text,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.positions OWNER TO nwa_user;

--
-- Name: projects; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.projects (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    api_key_hash text NOT NULL,
    allowed_origins text[],
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.projects OWNER TO nwa_user;

--
-- Name: remote_servers; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.remote_servers (
    id text NOT NULL,
    name text NOT NULL,
    url text NOT NULL,
    "apiKey" text NOT NULL,
    description text,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL,
    client_id text,
    client_secret text,
    redirect_uris text[],
    grant_types text[] DEFAULT ARRAY['authorization_code'::text, 'refresh_token'::text],
    default_scopes text[] DEFAULT ARRAY['read:profile'::text]
);


ALTER TABLE public.remote_servers OWNER TO nwa_user;

--
-- Name: role_permissions; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.role_permissions (
    id text NOT NULL,
    role_id text NOT NULL,
    permission_id text NOT NULL
);


ALTER TABLE public.role_permissions OWNER TO nwa_user;

--
-- Name: roles; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.roles (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    is_system boolean DEFAULT false NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.roles OWNER TO nwa_user;

--
-- Name: scopes; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.scopes (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    category text DEFAULT 'general'::text NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.scopes OWNER TO nwa_user;

--
-- Name: sessions; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.sessions (
    id text NOT NULL,
    session_token text NOT NULL,
    user_id text NOT NULL,
    expires timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.sessions OWNER TO nwa_user;

--
-- Name: system_settings; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.system_settings (
    id text NOT NULL,
    "maxLoginAttempts" integer DEFAULT 5 NOT NULL,
    lockout_duration integer DEFAULT 30 NOT NULL,
    session_timeout integer DEFAULT 60 NOT NULL,
    password_min_length integer DEFAULT 8 NOT NULL,
    password_require_uppercase boolean DEFAULT true NOT NULL,
    password_require_lowercase boolean DEFAULT true NOT NULL,
    password_require_numbers boolean DEFAULT true NOT NULL,
    password_require_special_chars boolean DEFAULT true NOT NULL,
    two_factor_auth_required boolean DEFAULT false NOT NULL,
    ip_whitelist_enabled boolean DEFAULT false NOT NULL,
    ip_whitelist text,
    audit_log_retention integer DEFAULT 90 NOT NULL,
    api_rate_limit integer DEFAULT 100 NOT NULL,
    cors_origins text,
    email_verification_required boolean DEFAULT true NOT NULL,
    password_reset_token_expiry integer DEFAULT 24 NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.system_settings OWNER TO nwa_user;

--
-- Name: title_positions; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.title_positions (
    id text NOT NULL,
    title_id text NOT NULL,
    position_id text NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


ALTER TABLE public.title_positions OWNER TO nwa_user;

--
-- Name: titles; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.titles (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.titles OWNER TO nwa_user;

--
-- Name: treaties; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.treaties (
    id text NOT NULL,
    user_id text NOT NULL,
    treaty_type_id text NOT NULL,
    status text DEFAULT 'ACTIVE'::text NOT NULL,
    signed_date timestamp(3) without time zone,
    expiration_date timestamp(3) without time zone,
    renewal_date timestamp(3) without time zone,
    notes text,
    document_path text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.treaties OWNER TO nwa_user;

--
-- Name: treaty_types; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.treaty_types (
    id text NOT NULL,
    name text NOT NULL,
    description text,
    category text NOT NULL,
    is_active boolean DEFAULT true NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.treaty_types OWNER TO nwa_user;

--
-- Name: user_positions; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.user_positions (
    id text NOT NULL,
    user_id text NOT NULL,
    position_id text NOT NULL,
    start_date timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    end_date timestamp(3) without time zone,
    is_active boolean DEFAULT true NOT NULL,
    notes text
);


ALTER TABLE public.user_positions OWNER TO nwa_user;

--
-- Name: user_profiles; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.user_profiles (
    id text NOT NULL,
    user_id text NOT NULL,
    nwa_email text,
    country text,
    city text,
    mobile text,
    bio text,
    date_of_birth timestamp(3) without time zone,
    license text,
    passport text,
    title_id text,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.user_profiles OWNER TO nwa_user;

--
-- Name: user_project_scopes; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.user_project_scopes (
    id text NOT NULL,
    user_id text NOT NULL,
    project_id text NOT NULL,
    scope_id text NOT NULL,
    granted_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    granted_by text
);


ALTER TABLE public.user_project_scopes OWNER TO nwa_user;

--
-- Name: user_roles; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.user_roles (
    id text NOT NULL,
    user_id text NOT NULL,
    role_id text NOT NULL,
    assigned_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    assigned_by text
);


ALTER TABLE public.user_roles OWNER TO nwa_user;

--
-- Name: users; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.users (
    id text NOT NULL,
    name text,
    email text,
    email_verified timestamp(3) without time zone,
    image text,
    password_hash text,
    two_factor_secret text,
    two_factor_enabled boolean DEFAULT false NOT NULL,
    created_at timestamp(3) without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    updated_at timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.users OWNER TO nwa_user;

--
-- Name: verification_tokens; Type: TABLE; Schema: public; Owner: nwa_user
--

CREATE TABLE public.verification_tokens (
    identifier text NOT NULL,
    token text NOT NULL,
    expires timestamp(3) without time zone NOT NULL
);


ALTER TABLE public.verification_tokens OWNER TO nwa_user;

--
-- Name: _prisma_migrations _prisma_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public._prisma_migrations
    ADD CONSTRAINT _prisma_migrations_pkey PRIMARY KEY (id);


--
-- Name: accounts accounts_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_pkey PRIMARY KEY (id);


--
-- Name: audit_logs audit_logs_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT audit_logs_pkey PRIMARY KEY (id);


--
-- Name: authorization_codes authorization_codes_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.authorization_codes
    ADD CONSTRAINT authorization_codes_pkey PRIMARY KEY (id);


--
-- Name: file_attachments file_attachments_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.file_attachments
    ADD CONSTRAINT file_attachments_pkey PRIMARY KEY (id);


--
-- Name: oauth_tokens oauth_tokens_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.oauth_tokens
    ADD CONSTRAINT oauth_tokens_pkey PRIMARY KEY (id);


--
-- Name: ordinance_types ordinance_types_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.ordinance_types
    ADD CONSTRAINT ordinance_types_pkey PRIMARY KEY (id);


--
-- Name: ordinances ordinances_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.ordinances
    ADD CONSTRAINT ordinances_pkey PRIMARY KEY (id);


--
-- Name: pending_bulk_uploads pending_bulk_uploads_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.pending_bulk_uploads
    ADD CONSTRAINT pending_bulk_uploads_pkey PRIMARY KEY (id);


--
-- Name: permissions permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.permissions
    ADD CONSTRAINT permissions_pkey PRIMARY KEY (id);


--
-- Name: positions positions_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.positions
    ADD CONSTRAINT positions_pkey PRIMARY KEY (id);


--
-- Name: projects projects_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.projects
    ADD CONSTRAINT projects_pkey PRIMARY KEY (id);


--
-- Name: remote_servers remote_servers_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.remote_servers
    ADD CONSTRAINT remote_servers_pkey PRIMARY KEY (id);


--
-- Name: role_permissions role_permissions_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT role_permissions_pkey PRIMARY KEY (id);


--
-- Name: roles roles_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.roles
    ADD CONSTRAINT roles_pkey PRIMARY KEY (id);


--
-- Name: scopes scopes_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.scopes
    ADD CONSTRAINT scopes_pkey PRIMARY KEY (id);


--
-- Name: sessions sessions_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT sessions_pkey PRIMARY KEY (id);


--
-- Name: system_settings system_settings_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.system_settings
    ADD CONSTRAINT system_settings_pkey PRIMARY KEY (id);


--
-- Name: title_positions title_positions_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.title_positions
    ADD CONSTRAINT title_positions_pkey PRIMARY KEY (id);


--
-- Name: titles titles_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.titles
    ADD CONSTRAINT titles_pkey PRIMARY KEY (id);


--
-- Name: treaties treaties_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaties
    ADD CONSTRAINT treaties_pkey PRIMARY KEY (id);


--
-- Name: treaty_types treaty_types_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaty_types
    ADD CONSTRAINT treaty_types_pkey PRIMARY KEY (id);


--
-- Name: user_positions user_positions_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_positions
    ADD CONSTRAINT user_positions_pkey PRIMARY KEY (id);


--
-- Name: user_profiles user_profiles_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT user_profiles_pkey PRIMARY KEY (id);


--
-- Name: user_project_scopes user_project_scopes_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_project_scopes
    ADD CONSTRAINT user_project_scopes_pkey PRIMARY KEY (id);


--
-- Name: user_roles user_roles_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_pkey PRIMARY KEY (id);


--
-- Name: users users_pkey; Type: CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.users
    ADD CONSTRAINT users_pkey PRIMARY KEY (id);


--
-- Name: accounts_provider_provider_account_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX accounts_provider_provider_account_id_key ON public.accounts USING btree (provider, provider_account_id);


--
-- Name: audit_logs_project_id_api_endpoint_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX audit_logs_project_id_api_endpoint_idx ON public.audit_logs USING btree (project_id, api_endpoint);


--
-- Name: audit_logs_remote_server_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX audit_logs_remote_server_id_idx ON public.audit_logs USING btree (remote_server_id);


--
-- Name: audit_logs_resource_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX audit_logs_resource_idx ON public.audit_logs USING btree (resource);


--
-- Name: audit_logs_timestamp_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX audit_logs_timestamp_idx ON public.audit_logs USING btree ("timestamp");


--
-- Name: audit_logs_user_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX audit_logs_user_id_idx ON public.audit_logs USING btree (user_id);


--
-- Name: authorization_codes_code_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX authorization_codes_code_idx ON public.authorization_codes USING btree (code);


--
-- Name: authorization_codes_code_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX authorization_codes_code_key ON public.authorization_codes USING btree (code);


--
-- Name: authorization_codes_expires_at_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX authorization_codes_expires_at_idx ON public.authorization_codes USING btree (expires_at);


--
-- Name: file_attachments_treaty_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX file_attachments_treaty_id_idx ON public.file_attachments USING btree (treaty_id);


--
-- Name: oauth_tokens_expires_at_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX oauth_tokens_expires_at_idx ON public.oauth_tokens USING btree (expires_at);


--
-- Name: oauth_tokens_remote_server_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX oauth_tokens_remote_server_id_idx ON public.oauth_tokens USING btree (remote_server_id);


--
-- Name: oauth_tokens_user_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX oauth_tokens_user_id_idx ON public.oauth_tokens USING btree (user_id);


--
-- Name: ordinance_types_name_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX ordinance_types_name_key ON public.ordinance_types USING btree (name);


--
-- Name: permissions_name_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX permissions_name_key ON public.permissions USING btree (name);


--
-- Name: permissions_resource_action_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX permissions_resource_action_key ON public.permissions USING btree (resource, action);


--
-- Name: positions_title_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX positions_title_key ON public.positions USING btree (title);


--
-- Name: projects_api_key_hash_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX projects_api_key_hash_idx ON public.projects USING btree (api_key_hash);


--
-- Name: projects_api_key_hash_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX projects_api_key_hash_key ON public.projects USING btree (api_key_hash);


--
-- Name: projects_is_active_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX projects_is_active_idx ON public.projects USING btree (is_active);


--
-- Name: remote_servers_client_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX remote_servers_client_id_key ON public.remote_servers USING btree (client_id);


--
-- Name: remote_servers_url_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX remote_servers_url_key ON public.remote_servers USING btree (url);


--
-- Name: role_permissions_role_id_permission_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX role_permissions_role_id_permission_id_key ON public.role_permissions USING btree (role_id, permission_id);


--
-- Name: roles_name_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX roles_name_key ON public.roles USING btree (name);


--
-- Name: scopes_is_active_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX scopes_is_active_idx ON public.scopes USING btree (is_active);


--
-- Name: scopes_name_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX scopes_name_idx ON public.scopes USING btree (name);


--
-- Name: scopes_name_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX scopes_name_key ON public.scopes USING btree (name);


--
-- Name: sessions_session_token_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX sessions_session_token_key ON public.sessions USING btree (session_token);


--
-- Name: title_positions_title_id_position_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX title_positions_title_id_position_id_key ON public.title_positions USING btree (title_id, position_id);


--
-- Name: titles_name_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX titles_name_key ON public.titles USING btree (name);


--
-- Name: treaty_types_name_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX treaty_types_name_key ON public.treaty_types USING btree (name);


--
-- Name: user_profiles_nwa_email_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX user_profiles_nwa_email_key ON public.user_profiles USING btree (nwa_email);


--
-- Name: user_profiles_user_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX user_profiles_user_id_key ON public.user_profiles USING btree (user_id);


--
-- Name: user_project_scopes_user_id_project_id_idx; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE INDEX user_project_scopes_user_id_project_id_idx ON public.user_project_scopes USING btree (user_id, project_id);


--
-- Name: user_project_scopes_user_id_project_id_scope_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX user_project_scopes_user_id_project_id_scope_id_key ON public.user_project_scopes USING btree (user_id, project_id, scope_id);


--
-- Name: user_roles_user_id_role_id_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX user_roles_user_id_role_id_key ON public.user_roles USING btree (user_id, role_id);


--
-- Name: users_email_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX users_email_key ON public.users USING btree (email);


--
-- Name: verification_tokens_identifier_token_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX verification_tokens_identifier_token_key ON public.verification_tokens USING btree (identifier, token);


--
-- Name: verification_tokens_token_key; Type: INDEX; Schema: public; Owner: nwa_user
--

CREATE UNIQUE INDEX verification_tokens_token_key ON public.verification_tokens USING btree (token);


--
-- Name: accounts accounts_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.accounts
    ADD CONSTRAINT accounts_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: audit_logs audit_logs_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT audit_logs_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: audit_logs audit_logs_remote_server_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT audit_logs_remote_server_id_fkey FOREIGN KEY (remote_server_id) REFERENCES public.remote_servers(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: audit_logs audit_logs_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.audit_logs
    ADD CONSTRAINT audit_logs_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: authorization_codes authorization_codes_remote_server_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.authorization_codes
    ADD CONSTRAINT authorization_codes_remote_server_id_fkey FOREIGN KEY (remote_server_id) REFERENCES public.remote_servers(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: authorization_codes authorization_codes_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.authorization_codes
    ADD CONSTRAINT authorization_codes_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: file_attachments file_attachments_treaty_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.file_attachments
    ADD CONSTRAINT file_attachments_treaty_id_fkey FOREIGN KEY (treaty_id) REFERENCES public.treaties(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: oauth_tokens oauth_tokens_remote_server_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.oauth_tokens
    ADD CONSTRAINT oauth_tokens_remote_server_id_fkey FOREIGN KEY (remote_server_id) REFERENCES public.remote_servers(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: oauth_tokens oauth_tokens_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.oauth_tokens
    ADD CONSTRAINT oauth_tokens_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ordinances ordinances_ordinance_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.ordinances
    ADD CONSTRAINT ordinances_ordinance_type_id_fkey FOREIGN KEY (ordinance_type_id) REFERENCES public.ordinance_types(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: ordinances ordinances_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.ordinances
    ADD CONSTRAINT ordinances_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: pending_bulk_uploads pending_bulk_uploads_uploader_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.pending_bulk_uploads
    ADD CONSTRAINT pending_bulk_uploads_uploader_id_fkey FOREIGN KEY (uploader_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: positions positions_parent_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.positions
    ADD CONSTRAINT positions_parent_id_fkey FOREIGN KEY (parent_id) REFERENCES public.positions(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: role_permissions role_permissions_permission_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT role_permissions_permission_id_fkey FOREIGN KEY (permission_id) REFERENCES public.permissions(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: role_permissions role_permissions_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.role_permissions
    ADD CONSTRAINT role_permissions_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: sessions sessions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.sessions
    ADD CONSTRAINT sessions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: title_positions title_positions_position_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.title_positions
    ADD CONSTRAINT title_positions_position_id_fkey FOREIGN KEY (position_id) REFERENCES public.positions(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: title_positions title_positions_title_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.title_positions
    ADD CONSTRAINT title_positions_title_id_fkey FOREIGN KEY (title_id) REFERENCES public.titles(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: treaties treaties_treaty_type_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaties
    ADD CONSTRAINT treaties_treaty_type_id_fkey FOREIGN KEY (treaty_type_id) REFERENCES public.treaty_types(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: treaties treaties_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.treaties
    ADD CONSTRAINT treaties_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_positions user_positions_position_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_positions
    ADD CONSTRAINT user_positions_position_id_fkey FOREIGN KEY (position_id) REFERENCES public.positions(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_positions user_positions_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_positions
    ADD CONSTRAINT user_positions_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_profiles user_profiles_title_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT user_profiles_title_id_fkey FOREIGN KEY (title_id) REFERENCES public.titles(id) ON UPDATE CASCADE ON DELETE SET NULL;


--
-- Name: user_profiles user_profiles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_profiles
    ADD CONSTRAINT user_profiles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_project_scopes user_project_scopes_project_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_project_scopes
    ADD CONSTRAINT user_project_scopes_project_id_fkey FOREIGN KEY (project_id) REFERENCES public.projects(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_project_scopes user_project_scopes_scope_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_project_scopes
    ADD CONSTRAINT user_project_scopes_scope_id_fkey FOREIGN KEY (scope_id) REFERENCES public.scopes(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_project_scopes user_project_scopes_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_project_scopes
    ADD CONSTRAINT user_project_scopes_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_roles user_roles_role_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_role_id_fkey FOREIGN KEY (role_id) REFERENCES public.roles(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- Name: user_roles user_roles_user_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: nwa_user
--

ALTER TABLE ONLY public.user_roles
    ADD CONSTRAINT user_roles_user_id_fkey FOREIGN KEY (user_id) REFERENCES public.users(id) ON UPDATE CASCADE ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--

\unrestrict scNJxveAjrakLrKUV5ABpiUaKYhzUyCZ0UHgdap4ZjtfN1WiDIaIAdBhmzEMOyh

