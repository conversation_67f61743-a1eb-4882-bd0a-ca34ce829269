import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function createUser() {
  try {
    console.log('Creating test user...');
    
    // Create a test user
    const testUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Test User',
      },
    });
    
    console.log('Test user created:', testUser);
    
    // Create user profile
    const userProfile = await prisma.userProfile.upsert({
      where: { userId: testUser.id },
      update: {},
      create: {
        userId: testUser.id,
        nwaEmail: '<EMAIL>',
        country: 'USA',
        city: 'New York',
        mobile: '+1234567890',
        bio: 'Test user for development purposes',
      },
    });
    
    console.log('User profile created:', userProfile);
    
    console.log('✅ Test user setup complete');
  } catch (error) {
    console.error('Error creating test user:', error);
  } finally {
    await prisma.$disconnect();
  }
}

createUser();