"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __generator = (this && this.__generator) || function (thisArg, body) {
    var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === "function" ? Iterator : Object).prototype);
    return g.next = verb(0), g["throw"] = verb(1), g["return"] = verb(2), typeof Symbol === "function" && (g[Symbol.iterator] = function() { return this; }), g;
    function verb(n) { return function (v) { return step([n, v]); }; }
    function step(op) {
        if (f) throw new TypeError("Generator is already executing.");
        while (g && (g = 0, op[0] && (_ = 0)), _) try {
            if (f = 1, y && (t = op[0] & 2 ? y["return"] : op[0] ? y["throw"] || ((t = y["return"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;
            if (y = 0, t) op = [op[0] & 2, t.value];
            switch (op[0]) {
                case 0: case 1: t = op; break;
                case 4: _.label++; return { value: op[1], done: false };
                case 5: _.label++; y = op[1]; op = [0]; continue;
                case 7: op = _.ops.pop(); _.trys.pop(); continue;
                default:
                    if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }
                    if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }
                    if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }
                    if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }
                    if (t[2]) _.ops.pop();
                    _.trys.pop(); continue;
            }
            op = body.call(thisArg, _);
        } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }
        if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };
    }
};
Object.defineProperty(exports, "__esModule", { value: true });
var client_1 = require("@prisma/client");
var prisma = new client_1.PrismaClient();
function main() {
    return __awaiter(this, void 0, void 0, function () {
        var memberRole, adminRole, moderatorRole, permissions, _i, permissions_1, perm, adminPermissions, memberPermissions, _a, adminPermissions_1, permission, _b, memberPermissions_1, permission, ordinanceTypes, _c, ordinanceTypes_1, type, treatyTypes, _d, treatyTypes_1, type, positions, _e, positions_1, position, scopes, _f, scopes_1, scope, testProject, testUser, userProfile;
        return __generator(this, function (_g) {
            switch (_g.label) {
                case 0:
                    console.log('🌱 Starting database seeding...');
                    return [4 /*yield*/, prisma.role.upsert({
                            where: { name: 'member' },
                            update: {},
                            create: {
                                name: 'member',
                                description: 'Standard NWA member',
                                isSystem: true,
                            },
                        })];
                case 1:
                    memberRole = _g.sent();
                    return [4 /*yield*/, prisma.role.upsert({
                            where: { name: 'admin' },
                            update: {},
                            create: {
                                name: 'admin',
                                description: 'System administrator',
                                isSystem: true,
                            },
                        })];
                case 2:
                    adminRole = _g.sent();
                    return [4 /*yield*/, prisma.role.upsert({
                            where: { name: 'moderator' },
                            update: {},
                            create: {
                                name: 'moderator',
                                description: 'Community moderator',
                                isSystem: true,
                            },
                        })];
                case 3:
                    moderatorRole = _g.sent();
                    console.log('✅ Created initial roles');
                    permissions = [
                        { name: 'user:read', resource: 'user', action: 'read', description: 'View user profiles' },
                        { name: 'user:write', resource: 'user', action: 'write', description: 'Edit user profiles' },
                        { name: 'user:delete', resource: 'user', action: 'delete', description: 'Delete user accounts' },
                        { name: 'ordinance:read', resource: 'ordinance', action: 'read', description: 'View ordinances' },
                        { name: 'ordinance:write', resource: 'ordinance', action: 'write', description: 'Create/edit ordinances' },
                        { name: 'ordinance:delete', resource: 'ordinance', action: 'delete', description: 'Delete ordinances' },
                        { name: 'treaty:read', resource: 'treaty', action: 'read', description: 'View treaties' },
                        { name: 'treaty:write', resource: 'treaty', action: 'write', description: 'Create/edit treaties' },
                        { name: 'treaty:delete', resource: 'treaty', action: 'delete', description: 'Delete treaties' },
                        { name: 'admin:access', resource: 'admin', action: 'access', description: 'Access admin panel' },
                    ];
                    _i = 0, permissions_1 = permissions;
                    _g.label = 4;
                case 4:
                    if (!(_i < permissions_1.length)) return [3 /*break*/, 7];
                    perm = permissions_1[_i];
                    return [4 /*yield*/, prisma.permission.upsert({
                            where: { name: perm.name },
                            update: {},
                            create: perm,
                        })];
                case 5:
                    _g.sent();
                    _g.label = 6;
                case 6:
                    _i++;
                    return [3 /*break*/, 4];
                case 7:
                    console.log('✅ Created initial permissions');
                    return [4 /*yield*/, prisma.permission.findMany()];
                case 8:
                    adminPermissions = _g.sent();
                    return [4 /*yield*/, prisma.permission.findMany({
                            where: {
                                OR: [
                                    { resource: 'user', action: 'read' },
                                    { resource: 'ordinance', action: 'read' },
                                    { resource: 'treaty', action: 'read' },
                                ]
                            }
                        })
                        // Admin gets all permissions
                    ];
                case 9:
                    memberPermissions = _g.sent();
                    _a = 0, adminPermissions_1 = adminPermissions;
                    _g.label = 10;
                case 10:
                    if (!(_a < adminPermissions_1.length)) return [3 /*break*/, 13];
                    permission = adminPermissions_1[_a];
                    return [4 /*yield*/, prisma.rolePermission.upsert({
                            where: {
                                roleId_permissionId: {
                                    roleId: adminRole.id,
                                    permissionId: permission.id,
                                }
                            },
                            update: {},
                            create: {
                                roleId: adminRole.id,
                                permissionId: permission.id,
                            },
                        })];
                case 11:
                    _g.sent();
                    _g.label = 12;
                case 12:
                    _a++;
                    return [3 /*break*/, 10];
                case 13:
                    _b = 0, memberPermissions_1 = memberPermissions;
                    _g.label = 14;
                case 14:
                    if (!(_b < memberPermissions_1.length)) return [3 /*break*/, 17];
                    permission = memberPermissions_1[_b];
                    return [4 /*yield*/, prisma.rolePermission.upsert({
                            where: {
                                roleId_permissionId: {
                                    roleId: memberRole.id,
                                    permissionId: permission.id,
                                }
                            },
                            update: {},
                            create: {
                                roleId: memberRole.id,
                                permissionId: permission.id,
                            },
                        })];
                case 15:
                    _g.sent();
                    _g.label = 16;
                case 16:
                    _b++;
                    return [3 /*break*/, 14];
                case 17:
                    console.log('✅ Assigned permissions to roles');
                    ordinanceTypes = [
                        { name: 'Baptism', description: 'Water baptism ceremony', category: 'sacrament' },
                        { name: 'Confirmation', description: 'Confirmation of faith', category: 'sacrament' },
                        { name: 'Blessing', description: 'Spiritual blessing ceremony', category: 'blessing' },
                        { name: 'Dedication', description: 'Child dedication ceremony', category: 'dedication' },
                    ];
                    _c = 0, ordinanceTypes_1 = ordinanceTypes;
                    _g.label = 18;
                case 18:
                    if (!(_c < ordinanceTypes_1.length)) return [3 /*break*/, 21];
                    type = ordinanceTypes_1[_c];
                    return [4 /*yield*/, prisma.ordinanceType.upsert({
                            where: { name: type.name },
                            update: {},
                            create: type,
                        })];
                case 19:
                    _g.sent();
                    _g.label = 20;
                case 20:
                    _c++;
                    return [3 /*break*/, 18];
                case 21:
                    console.log('✅ Created ordinance types');
                    treatyTypes = [
                        { name: 'Membership Covenant', description: 'Standard membership agreement', category: 'membership' },
                        { name: 'Leadership Covenant', description: 'Leadership commitment agreement', category: 'leadership' },
                        { name: 'Service Agreement', description: 'Volunteer service commitment', category: 'service' },
                    ];
                    _d = 0, treatyTypes_1 = treatyTypes;
                    _g.label = 22;
                case 22:
                    if (!(_d < treatyTypes_1.length)) return [3 /*break*/, 25];
                    type = treatyTypes_1[_d];
                    return [4 /*yield*/, prisma.treatyType.upsert({
                            where: { name: type.name },
                            update: {},
                            create: type,
                        })];
                case 23:
                    _g.sent();
                    _g.label = 24;
                case 24:
                    _d++;
                    return [3 /*break*/, 22];
                case 25:
                    console.log('✅ Created treaty types');
                    positions = [
                        { title: 'Elder', description: 'Church elder position', level: 1 },
                        { title: 'Deacon', description: 'Church deacon position', level: 2 },
                        { title: 'Minister', description: 'Licensed minister', level: 1 },
                        { title: 'Youth Leader', description: 'Youth ministry leader', level: 3, parentId: null },
                    ];
                    _e = 0, positions_1 = positions;
                    _g.label = 26;
                case 26:
                    if (!(_e < positions_1.length)) return [3 /*break*/, 29];
                    position = positions_1[_e];
                    return [4 /*yield*/, prisma.position.upsert({
                            where: { title: position.title },
                            update: {},
                            create: {
                                title: position.title,
                                description: position.description,
                                level: position.level,
                            },
                        })];
                case 27:
                    _g.sent();
                    _g.label = 28;
                case 28:
                    _e++;
                    return [3 /*break*/, 26];
                case 29:
                    console.log('✅ Created sample positions');
                    scopes = [
                        { name: 'read:users', description: 'Read user profile information', category: 'users' },
                        { name: 'write:users', description: 'Create and update user profiles', category: 'users' },
                        { name: 'read:profiles', description: 'Read detailed user profile data', category: 'profiles' },
                        { name: 'write:profiles', description: 'Update user profile information', category: 'profiles' },
                        { name: 'read:roles', description: 'Read user roles and permissions', category: 'roles' },
                        { name: 'write:roles', description: 'Assign and modify user roles', category: 'roles' },
                        { name: 'read:positions', description: 'Read user positions and hierarchy', category: 'positions' },
                        { name: 'write:positions', description: 'Assign and modify user positions', category: 'positions' },
                        { name: 'read:ordinances', description: 'Read user ordinance information', category: 'ordinances' },
                        { name: 'write:ordinances', description: 'Create and update ordinances', category: 'ordinances' },
                        { name: 'read:treaties', description: 'Read user treaty information', category: 'treaties' },
                        { name: 'write:treaties', description: 'Create and update treaties', category: 'treaties' },
                        { name: 'read:audit', description: 'Read audit log information', category: 'audit' },
                        { name: 'admin:projects', description: 'Manage external projects', category: 'admin' },
                        { name: 'admin:scopes', description: 'Manage permission scopes', category: 'admin' },
                    ];
                    _f = 0, scopes_1 = scopes;
                    _g.label = 30;
                case 30:
                    if (!(_f < scopes_1.length)) return [3 /*break*/, 33];
                    scope = scopes_1[_f];
                    return [4 /*yield*/, prisma.scope.upsert({
                            where: { name: scope.name },
                            update: {},
                            create: scope,
                        })];
                case 31:
                    _g.sent();
                    _g.label = 32;
                case 32:
                    _f++;
                    return [3 /*break*/, 30];
                case 33:
                    console.log('✅ Created initial API scopes');
                    return [4 /*yield*/, prisma.project.upsert({
                            where: { apiKeyHash: 'test_project_hash_dev_only' },
                            update: {},
                            create: {
                                name: 'Development Test Project',
                                description: 'Test project for development and testing purposes',
                                apiKeyHash: 'test_project_hash_dev_only',
                                allowedOrigins: ['http://localhost:3000', 'http://localhost:3005', 'https://localhost:3000'],
                                isActive: true,
                            },
                        })];
                case 34:
                    testProject = _g.sent();
                    console.log('✅ Created test project for development');
                    return [4 /*yield*/, prisma.user.upsert({
                            where: { email: '<EMAIL>' },
                            update: {},
                            create: {
                                email: '<EMAIL>',
                                name: 'Test User',
                            },
                        })
                        // Create user profile
                    ];
                case 35:
                    testUser = _g.sent();
                    return [4 /*yield*/, prisma.userProfile.upsert({
                            where: { userId: testUser.id },
                            update: {},
                            create: {
                                userId: testUser.id,
                                nwaEmail: '<EMAIL>',
                                country: 'USA',
                                city: 'New York',
                                mobile: '+1234567890',
                                bio: 'Test user for development purposes',
                            },
                        })
                        // Assign member role to test user
                    ];
                case 36:
                    userProfile = _g.sent();
                    // Assign member role to test user
                    return [4 /*yield*/, prisma.userRole.upsert({
                            where: {
                                userId_roleId: {
                                    userId: testUser.id,
                                    roleId: memberRole.id,
                                }
                            },
                            update: {},
                            create: {
                                userId: testUser.id,
                                roleId: memberRole.id,
                            },
                        })];
                case 37:
                    // Assign member role to test user
                    _g.sent();
                    console.log('✅ Created test user for development');
                    console.log('🎉 Database seeding completed successfully!');
                    return [2 /*return*/];
            }
        });
    });
}
main()
    .catch(function (e) {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
})
    .finally(function () { return __awaiter(void 0, void 0, void 0, function () {
    return __generator(this, function (_a) {
        switch (_a.label) {
            case 0: return [4 /*yield*/, prisma.$disconnect()];
            case 1:
                _a.sent();
                return [2 /*return*/];
        }
    });
}); });
