// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique @map("session_token")
  userId       String   @map("user_id")
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Core User Model (Extended for NWA)
model User {
  id               String    @id @default(cuid())
  name             String?
  email            String?   @unique
  emailVerified    DateTime? @map("email_verified")
  image            String?
  passwordHash     String?   @map("password_hash") @db.Text
  twoFactorSecret  String?   @map("two_factor_secret") @db.Text
  twoFactorEnabled Boolean   @default(false) @map("two_factor_enabled")
  createdAt        DateTime  @default(now()) @map("created_at")
  updatedAt        DateTime  @updatedAt @map("updated_at")

  // NextAuth.js relations
  sessions Session[]

  // NWA-specific relations
  profile            UserProfile?
  userPositions      UserPosition[]
  userRoles          UserRole[]
  ordinances         Ordinance[]
  treaties           Treaty[]
  auditLogs          AuditLog[]
  userProjectScopes  UserProjectScope[]
  pendingBulkUploads PendingBulkUpload[]
  AuthorizationCode  AuthorizationCode[]
  OAuthToken         OAuthToken[]

  @@map("users")
}

// Extended User Profile
model UserProfile {
  id          String    @id @default(cuid())
  userId      String    @unique @map("user_id")
  nwaEmail    String?   @unique @map("nwa_email")
  firstName   String?   @map("first_name")
  lastName    String?   @map("last_name")
  country     String?
  city        String?
  mobile      String?
  bio         String?   @db.Text
  dateOfBirth DateTime? @map("date_of_birth")
  license     String?   @map("license")
  passport    String?   @map("passport")
  titleId     String?   @map("title_id")
  createdAt   DateTime  @default(now()) @map("created_at")
  updatedAt   DateTime  @updatedAt @map("updated_at")

  user  User   @relation(fields: [userId], references: [id], onDelete: Cascade)
  title Title? @relation(fields: [titleId], references: [id])

  @@map("user_profiles")
}

// Ambassadorial Titles
model Title {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  // Relations
  userProfiles   UserProfile[]
  titlePositions TitlePosition[]

  @@map("titles")
}

// Title-Position Relationship (Many-to-Many)
model TitlePosition {
  id         String   @id @default(cuid())
  titleId    String   @map("title_id")
  positionId String   @map("position_id")
  createdAt  DateTime @default(now()) @map("created_at")

  title    Title    @relation(fields: [titleId], references: [id], onDelete: Cascade)
  position Position @relation(fields: [positionId], references: [id], onDelete: Cascade)

  @@unique([titleId, positionId])
  @@map("title_positions")
}

// Role-Based Access Control
model Role {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  isSystem    Boolean  @default(false) @map("is_system")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  userRoles       UserRole[]
  rolePermissions RolePermission[]

  @@map("roles")
}

model Permission {
  id          String   @id @default(cuid())
  name        String   @unique
  resource    String
  action      String
  description String?
  createdAt   DateTime @default(now()) @map("created_at")

  rolePermissions RolePermission[]

  @@unique([resource, action])
  @@map("permissions")
}

model RolePermission {
  id           String @id @default(cuid())
  roleId       String @map("role_id")
  permissionId String @map("permission_id")

  role       Role       @relation(fields: [roleId], references: [id], onDelete: Cascade)
  permission Permission @relation(fields: [permissionId], references: [id], onDelete: Cascade)

  @@unique([roleId, permissionId])
  @@map("role_permissions")
}

model UserRole {
  id         String   @id @default(cuid())
  userId     String   @map("user_id")
  roleId     String   @map("role_id")
  assignedAt DateTime @default(now()) @map("assigned_at")
  assignedBy String?  @map("assigned_by")

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)
  role Role @relation(fields: [roleId], references: [id], onDelete: Cascade)

  @@unique([userId, roleId])
  @@map("user_roles")
}

// Position Management
model Position {
  id          String   @id @default(cuid())
  title       String   @unique
  description String?
  level       Int      @default(0)
  parentId    String?  @map("parent_id")
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  parent         Position?       @relation("PositionHierarchy", fields: [parentId], references: [id])
  children       Position[]      @relation("PositionHierarchy")
  userPositions  UserPosition[]
  titlePositions TitlePosition[]

  @@map("positions")
}

model UserPosition {
  id         String    @id @default(cuid())
  userId     String    @map("user_id")
  positionId String    @map("position_id")
  startDate  DateTime  @default(now()) @map("start_date")
  endDate    DateTime? @map("end_date")
  isActive   Boolean   @default(true) @map("is_active")
  notes      String?

  user     User     @relation(fields: [userId], references: [id], onDelete: Cascade)
  position Position @relation(fields: [positionId], references: [id], onDelete: Cascade)

  @@map("user_positions")
}

// Ordinances System
model OrdinanceType {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  category    String
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  ordinances Ordinance[]

  @@map("ordinance_types")
}

model Ordinance {
  id              String    @id @default(cuid())
  userId          String    @map("user_id")
  ordinanceTypeId String    @map("ordinance_type_id")
  status          String    @default("PENDING")
  completedDate   DateTime? @map("completed_date")
  expirationDate  DateTime? @map("expiration_date")
  notes           String?   @db.Text
  documentPath    String?   @map("document_path")
  createdAt       DateTime  @default(now()) @map("created_at")
  updatedAt       DateTime  @updatedAt @map("updated_at")

  user          User          @relation(fields: [userId], references: [id], onDelete: Cascade)
  ordinanceType OrdinanceType @relation(fields: [ordinanceTypeId], references: [id], onDelete: Cascade)

  @@map("ordinances")
}

// Treaties System
model TreatyType {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  category    String
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  treaties Treaty[]

  @@map("treaty_types")
}

model Treaty {
  id             String    @id @default(cuid())
  userId         String    @map("user_id")
  treatyTypeId   String    @map("treaty_type_id")
  status         String    @default("ACTIVE")
  signedDate     DateTime? @map("signed_date")
  expirationDate DateTime? @map("expiration_date")
  renewalDate    DateTime? @map("renewal_date")
  notes          String?   @db.Text
  documentPath   String?   @map("document_path")
  createdAt      DateTime  @default(now()) @map("created_at")
  updatedAt      DateTime  @updatedAt @map("updated_at")

  user            User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  treatyType      TreatyType       @relation(fields: [treatyTypeId], references: [id], onDelete: Cascade)
  fileAttachments FileAttachment[]

  @@map("treaties")
}

// File Attachments for Treaties
model FileAttachment {
  id         String   @id @default(cuid())
  treatyId   String   @map("treaty_id")
  fileName   String   @map("file_name")
  filePath   String   @map("file_path")
  mimeType   String   @map("mime_type")
  fileSize   Int      @map("file_size")
  uploadedAt DateTime @default(now()) @map("uploaded_at")

  treaty Treaty @relation(fields: [treatyId], references: [id], onDelete: Cascade)

  @@index([treatyId])
  @@map("file_attachments")
}

// Audit Logging
model AuditLog {
  id           String   @id @default(cuid())
  userId       String?  @map("user_id")
  action       String
  resource     String
  resourceId   String?  @map("resource_id")
  oldValues    Json?    @map("old_values")
  newValues    Json?    @map("new_values")
  ipAddress    String?  @map("ip_address")
  userAgent    String?  @map("user_agent")
  timestamp    DateTime @default(now())
  success      Boolean  @map("success")
  statusCode   Int?     @map("status_code")
  errorMessage String?  @map("error_message")
  requestId    String?  @map("request_id")
  duration     Int?     @map("duration")
  requestSize  Int?     @map("request_size")
  responseSize Int?     @map("response_size")
  metadata     Json?    @map("metadata")

  // External API tracking fields
  projectId     String? @map("project_id")
  apiEndpoint   String? @map("api_endpoint")
  requestMethod String? @map("request_method")

  user    User?    @relation(fields: [userId], references: [id], onDelete: SetNull)
  project Project? @relation(fields: [projectId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([resource])
  @@index([timestamp])
  @@index([projectId, apiEndpoint])
  @@map("audit_logs")
}

// External Project Management
model Project {
  id             String   @id @default(cuid())
  name           String
  description    String?
  apiKeyHash     String   @unique @map("api_key_hash")
  allowedOrigins String[] @map("allowed_origins")
  isActive       Boolean  @default(true) @map("is_active")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  // Relations
  userProjectScopes UserProjectScope[]
  auditLogs         AuditLog[]

  @@index([apiKeyHash])
  @@index([isActive])
  @@map("projects")
}

model Scope {
  id          String   @id @default(cuid())
  name        String   @unique
  description String?
  category    String   @default("general")
  isActive    Boolean  @default(true) @map("is_active")
  createdAt   DateTime @default(now()) @map("created_at")

  // Relations
  userProjectScopes UserProjectScope[]

  @@index([name])
  @@index([isActive])
  @@map("scopes")
}

model UserProjectScope {
  id        String   @id @default(cuid())
  userId    String   @map("user_id")
  projectId String   @map("project_id")
  scopeId   String   @map("scope_id")
  grantedAt DateTime @default(now()) @map("granted_at")
  grantedBy String?  @map("granted_by")

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  project Project @relation(fields: [projectId], references: [id], onDelete: Cascade)
  scope   Scope   @relation(fields: [scopeId], references: [id], onDelete: Cascade)

  @@unique([userId, projectId, scopeId])
  @@index([userId, projectId])
  @@map("user_project_scopes")
}

// Pending Bulk Uploads (for admin approval)
model PendingBulkUpload {
  id          String   @id @default(cuid())
  uploaderId  String   @map("uploader_id")
  fileName    String   @map("file_name")
  recordCount Int      @map("record_count")
  data        String   @db.Text
  status      String   @default("PENDING") @map("status")
  createdAt   DateTime @default(now()) @map("created_at")
  updatedAt   DateTime @updatedAt @map("updated_at")

  uploader User @relation(fields: [uploaderId], references: [id], onDelete: Cascade)

  @@map("pending_bulk_uploads")
}

// Enums
enum UserStatus {
  ACTIVE
  INACTIVE
  SUSPENDED
  PENDING_VERIFICATION
}

enum OrdinanceStatus {
  PENDING
  IN_PROGRESS
  COMPLETED
  EXPIRED
  CANCELLED
}

enum TreatyStatus {
  ACTIVE
  EXPIRED
  TERMINATED
  PENDING_RENEWAL
}

enum BulkUploadStatus {
  PENDING
  APPROVED
  REJECTED
}

model RemoteServer {
  id                      String              @id @default(cuid())
  name                    String
  url                     String
  apiKey                  String              @map("apiKey")
  description             String?
  isActive                Boolean             @default(true)
  createdAt               DateTime            @default(now())
  updatedAt               DateTime            @updatedAt
  clientId                String?             @unique @map("client_id")
  clientSecret            String?             @map("client_secret")
  redirectUris            String[]            @map("redirect_uris")
  grantTypes              String[]            @default(["authorization_code", "refresh_token"]) @map("grant_types")
  defaultScopes           String[]            @default(["read:profile"]) @map("default_scopes")
  callbackUrl             String?             @map("callback_url")
  allowedOrigins          String[]            @map("allowed_origins")
  tokenEndpointAuthMethod String              @default("client_secret_basic") @map("token_endpoint_auth_method")
  AuthorizationCode       AuthorizationCode[]
  OAuthToken              OAuthToken[]

  @@map("remote_servers")
}

model AuthorizationCode {
  id             String    @id @default(cuid())
  remoteServerId String    @map("remote_server_id")
  userId         String    @map("user_id")
  code           String    @unique
  redirectUri    String?   @map("redirect_uri")
  scope          String?
  expiresAt      DateTime  @map("expires_at")
  createdAt      DateTime  @default(now()) @map("created_at")
  usedAt         DateTime? @map("used_at")

  remoteServer RemoteServer @relation(fields: [remoteServerId], references: [id])
  user         User         @relation(fields: [userId], references: [id])

  @@map("authorization_codes")
}

model OAuthToken {
  id             String   @id @default(cuid())
  remoteServerId String   @map("remote_server_id")
  userId         String?  @map("user_id")
  accessToken    String   @map("access_token")
  refreshToken   String?  @map("refresh_token")
  tokenType      String   @default("Bearer") @map("token_type")
  scope          String?
  expiresAt      DateTime @map("expires_at")
  createdAt      DateTime @default(now()) @map("created_at")
  updatedAt      DateTime @updatedAt @map("updated_at")

  remoteServer RemoteServer @relation(fields: [remoteServerId], references: [id])
  user         User?        @relation(fields: [userId], references: [id])

  @@map("oauth_tokens")
}
