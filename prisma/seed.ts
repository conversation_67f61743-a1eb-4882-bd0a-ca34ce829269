import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

async function main() {
  console.log('🌱 Starting database seeding...')

  // Create initial roles
  const memberRole = await prisma.role.upsert({
    where: { name: 'member' },
    update: {},
    create: {
      name: 'member',
      description: 'Standard NWA member',
      isSystem: true,
    },
  })

  const adminRole = await prisma.role.upsert({
    where: { name: 'admin' },
    update: {},
    create: {
      name: 'admin',
      description: 'System administrator',
      isSystem: true,
    },
  })

  const moderatorRole = await prisma.role.upsert({
    where: { name: 'moderator' },
    update: {},
    create: {
      name: 'moderator',
      description: 'Community moderator',
      isSystem: true,
    },
  })

  console.log('✅ Created initial roles')

  // Create initial permissions
  const permissions = [
    { name: 'user:read', resource: 'user', action: 'read', description: 'View user profiles' },
    { name: 'user:write', resource: 'user', action: 'write', description: 'Edit user profiles' },
    { name: 'user:delete', resource: 'user', action: 'delete', description: 'Delete user accounts' },
    { name: 'ordinance:read', resource: 'ordinance', action: 'read', description: 'View ordinances' },
    { name: 'ordinance:write', resource: 'ordinance', action: 'write', description: 'Create/edit ordinances' },
    { name: 'ordinance:delete', resource: 'ordinance', action: 'delete', description: 'Delete ordinances' },
    { name: 'treaty:read', resource: 'treaty', action: 'read', description: 'View treaties' },
    { name: 'treaty:write', resource: 'treaty', action: 'write', description: 'Create/edit treaties' },
    { name: 'treaty:delete', resource: 'treaty', action: 'delete', description: 'Delete treaties' },
    { name: 'admin:access', resource: 'admin', action: 'access', description: 'Access admin panel' },
  ]

  for (const perm of permissions) {
    await prisma.permission.upsert({
      where: { name: perm.name },
      update: {},
      create: perm,
    })
  }

  console.log('✅ Created initial permissions')

  // Assign permissions to roles
  const adminPermissions = await prisma.permission.findMany()
  const memberPermissions = await prisma.permission.findMany({
    where: {
      OR: [
        { resource: 'user', action: 'read' },
        { resource: 'ordinance', action: 'read' },
        { resource: 'treaty', action: 'read' },
      ]
    }
  })

  // Admin gets all permissions
  for (const permission of adminPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: adminRole.id,
          permissionId: permission.id,
        }
      },
      update: {},
      create: {
        roleId: adminRole.id,
        permissionId: permission.id,
      },
    })
  }

  // Members get read permissions
  for (const permission of memberPermissions) {
    await prisma.rolePermission.upsert({
      where: {
        roleId_permissionId: {
          roleId: memberRole.id,
          permissionId: permission.id,
        }
      },
      update: {},
      create: {
        roleId: memberRole.id,
        permissionId: permission.id,
      },
    })
  }

  console.log('✅ Assigned permissions to roles')

  // Create ordinance types
  const ordinanceTypes = [
    { name: 'Baptism', description: 'Water baptism ceremony', category: 'sacrament' },
    { name: 'Confirmation', description: 'Confirmation of faith', category: 'sacrament' },
    { name: 'Blessing', description: 'Spiritual blessing ceremony', category: 'blessing' },
    { name: 'Dedication', description: 'Child dedication ceremony', category: 'dedication' },
  ]

  for (const type of ordinanceTypes) {
    await prisma.ordinanceType.upsert({
      where: { name: type.name },
      update: {},
      create: type,
    })
  }

  console.log('✅ Created ordinance types')

  // Create treaty types
  const treatyTypes = [
    { name: 'Membership Covenant', description: 'Standard membership agreement', category: 'membership' },
    { name: 'Leadership Covenant', description: 'Leadership commitment agreement', category: 'leadership' },
    { name: 'Service Agreement', description: 'Volunteer service commitment', category: 'service' },
  ]

  for (const type of treatyTypes) {
    await prisma.treatyType.upsert({
      where: { name: type.name },
      update: {},
      create: type,
    })
  }

  console.log('✅ Created treaty types')

  // Create sample positions
  const positions = [
    { title: 'Elder', description: 'Church elder position', level: 1 },
    { title: 'Deacon', description: 'Church deacon position', level: 2 },
    { title: 'Minister', description: 'Licensed minister', level: 1 },
    { title: 'Youth Leader', description: 'Youth ministry leader', level: 3, parentId: null },
  ]

  for (const position of positions) {
    await prisma.position.upsert({
      where: { title: position.title },
      update: {},
      create: {
        title: position.title,
        description: position.description,
        level: position.level,
      },
    })
  }

  console.log('✅ Created sample positions')

  // Create initial scopes for external API authentication
  const scopes = [
    { name: 'read:users', description: 'Read user profile information', category: 'users' },
    { name: 'write:users', description: 'Create and update user profiles', category: 'users' },
    { name: 'read:profiles', description: 'Read detailed user profile data', category: 'profiles' },
    { name: 'write:profiles', description: 'Update user profile information', category: 'profiles' },
    { name: 'read:roles', description: 'Read user roles and permissions', category: 'roles' },
    { name: 'write:roles', description: 'Assign and modify user roles', category: 'roles' },
    { name: 'read:positions', description: 'Read user positions and hierarchy', category: 'positions' },
    { name: 'write:positions', description: 'Assign and modify user positions', category: 'positions' },
    { name: 'read:ordinances', description: 'Read user ordinance information', category: 'ordinances' },
    { name: 'write:ordinances', description: 'Create and update ordinances', category: 'ordinances' },
    { name: 'read:treaties', description: 'Read user treaty information', category: 'treaties' },
    { name: 'write:treaties', description: 'Create and update treaties', category: 'treaties' },
    { name: 'read:audit', description: 'Read audit log information', category: 'audit' },
    { name: 'admin:projects', description: 'Manage external projects', category: 'admin' },
    { name: 'admin:scopes', description: 'Manage permission scopes', category: 'admin' },
  ]

  for (const scope of scopes) {
    await prisma.scope.upsert({
      where: { name: scope.name },
      update: {},
      create: scope,
    })
  }

  console.log('✅ Created initial API scopes')

  // Create test project for development
  const testProject = await prisma.project.upsert({
    where: { apiKeyHash: 'test_project_hash_dev_only' },
    update: {},
    create: {
      name: 'Development Test Project',
      description: 'Test project for development and testing purposes',
      apiKeyHash: 'test_project_hash_dev_only',
      allowedOrigins: ['http://localhost:3000', 'http://localhost:3005', 'https://localhost:3000'],
      isActive: true,
    },
  })

  console.log('✅ Created test project for development')

  // Create a test user for development
  const testUser = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Test User',
    },
  })

  // Create user profile
  const userProfile = await prisma.userProfile.upsert({
    where: { userId: testUser.id },
    update: {},
    create: {
      userId: testUser.id,
      nwaEmail: '<EMAIL>',
      country: 'USA',
      city: 'New York',
      mobile: '+1234567890',
      bio: 'Test user for development purposes',
    },
  })

  // Assign member role to test user
  await prisma.userRole.upsert({
    where: {
      userId_roleId: {
        userId: testUser.id,
        roleId: memberRole.id,
      }
    },
    update: {},
    create: {
      userId: testUser.id,
      roleId: memberRole.id,
    },
  })

  console.log('✅ Created test user for development')

  // Create OAuth client configuration for NWAPromote
  const nwaPromoteClient = await prisma.remoteServer.upsert({
    where: { clientId: 'nwapromote-client-local' },
    update: {},
    create: {
      name: 'NWA Promote Local',
      url: 'http://localhost:3002',
      apiKey: 'nwa_promote_api_key_dev_only',
      description: 'Local development client for NWA Promote',
      isActive: true,
      clientId: 'nwapromote-client-local',
      clientSecret: 'da69150be2ae984ea232f144387add211844500d9d94de131ba78f9e2936fbff',
      redirectUris: ['http://localhost:3002/api/auth/callback/member-portal-custom'],
      grantTypes: ['authorization_code', 'refresh_token'],
      defaultScopes: ['read:profile'],
      callbackUrl: 'http://localhost:3002/api/auth/callback/member-portal-custom',
      allowedOrigins: ['http://localhost:3002'],
      tokenEndpointAuthMethod: 'client_secret_basic',
    },
  })

  console.log('✅ Created OAuth client configuration for NWAPromote')

  console.log('🎉 Database seeding completed successfully!')
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })