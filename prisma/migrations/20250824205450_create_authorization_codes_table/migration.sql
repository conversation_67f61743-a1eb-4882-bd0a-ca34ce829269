-- CreateTable
CREATE TABLE "public"."authorization_codes" (
    "id" TEXT NOT NULL,
    "remote_server_id" TEXT NOT NULL,
    "user_id" TEXT NOT NULL,
    "code" TEXT NOT NULL,
    "redirect_uri" TEXT,
    "scope" TEXT,
    "expires_at" TIMESTAMP(3) NOT NULL,
    "created_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "used_at" TIMESTAMP(3),

    CONSTRAINT "authorization_codes_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE UNIQUE INDEX "authorization_codes_code_key" ON "public"."authorization_codes"("code");

-- AddForeignKey
ALTER TABLE "public"."authorization_codes" ADD CONSTRAINT "authorization_codes_remote_server_id_fkey" FOREIGN KEY ("remote_server_id") REFERENCES "public"."remote_servers"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "public"."authorization_codes" ADD CONSTRAINT "authorization_codes_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "public"."users"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
