-- CreateTable
CREATE TABLE "public"."file_attachments" (
    "id" TEXT NOT NULL,
    "treaty_id" TEXT NOT NULL,
    "file_name" TEXT NOT NULL,
    "file_path" TEXT NOT NULL,
    "mime_type" TEXT NOT NULL,
    "file_size" INTEGER NOT NULL,
    "uploaded_at" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,

    CONSTRAINT "file_attachments_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "file_attachments_treaty_id_idx" ON "public"."file_attachments"("treaty_id");

-- AddForeignKey
ALTER TABLE "public"."file_attachments" ADD CONSTRAINT "file_attachments_treaty_id_fkey" FOREIGN KEY ("treaty_id") REFERENCES "public"."treaties"("id") ON DELETE CASCADE ON UPDATE CASCADE;
