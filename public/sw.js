// Service Worker for NWA Member Portal
// This is a basic service worker to handle the /sw.js request

self.addEventListener('install', (event) => {
  console.log('Service Worker installing...');
  self.skipWaiting();
});

self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...');
  event.waitUntil(self.clients.claim());
});

self.addEventListener('fetch', (event) => {
  // For now, just pass through all requests
  // Future: Add caching strategies here
  event.respondWith(fetch(event.request));
});