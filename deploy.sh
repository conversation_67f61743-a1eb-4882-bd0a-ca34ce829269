#!/bin/bash

# NWA Alliance Deployment Script
# Local development: ./deploy.sh
# Remote deployment: ./deploy.sh remote
# Build individual containers: ./deploy.sh [app|redis|postgres|minio|all]
# Examples:
#   ./deploy.sh app      # Build only the app container
#   ./deploy.sh redis    # Build only the redis container
#   ./deploy.sh postgres # Build only the postgres container
#   ./deploy.sh minio    # Build only the minio container
#   ./deploy.sh all      # Build all containers (default)
#   ./deploy.sh remote   # Deploy all containers to remote server

set -e  # Exit on any error

# Configuration
NWAALLIANCE_DEPLOY_WEBHOOK="http://***************:3000/api/deploy/M6PVfFYNKJEcLC-pY6d9H"
MINIO_DEPLOY_WEBHOOK="http://***************:3000/api/deploy/r8LC42ZfHoCLHHAYQJUzc"
REDIS_DEPLOY_WEBHOOK="http://***************:3000/api/deploy/-VY33hmpoVgSfKzoNgjFL"
POSTGRES_DEPLOY_WEBHOOK="http://***************:3000/api/deploy/-MchTyG9ezcur27KfSRS-"
REGISTRY="ghcr.io/darrenedward"
PROJECT_NAME="nwaalliance"
IMAGE_NAME="nwa-member-portal"

# Determine build target
BUILD_TARGET="all"
REMOTE_DEPLOY=false

if [ "$1" == "remote" ]; then
    REMOTE_DEPLOY=true
elif [ "$1" == "app" ] || [ "$1" == "redis" ] || [ "$1" == "postgres" ] || [ "$1" == "minio" ]; then
    BUILD_TARGET="$1"
elif [ "$1" == "all" ] || [ "$1" == "" ]; then
    BUILD_TARGET="all"
else
    echo "❌ Invalid argument: $1"
    echo "Usage: ./deploy.sh [app|redis|postgres|minio|all|remote]"
    echo "  app      - Build only the Next.js application container"
    echo "  redis    - Build only the Redis container"
    echo "  postgres - Build only the PostgreSQL container"
    echo "  minio    - Build only the MinIO container"
    echo "  all      - Build all containers (default)"
    echo "  remote   - Deploy all containers to remote server"
    exit 1
fi

if [ "$REMOTE_DEPLOY" = true ]; then
    echo "🚀 Starting NWA Alliance REMOTE deployment..."
    echo "=========================================="
else
    if [ "$BUILD_TARGET" = "all" ]; then
        echo "🚀 Starting NWA Alliance LOCAL deployment (all containers)..."
    else
        echo "🚀 Starting NWA Alliance LOCAL deployment ($BUILD_TARGET only)..."
    fi
    echo "=========================================="
fi

# Function to check if a command succeeded
check_command() {
    if [ $? -ne 0 ]; then
        echo "❌ Command failed: $1"
        exit 1
    else
        echo "✅ $1 completed successfully"
    fi
}

# Clean build artifacts
echo ""
echo "🧹 Cleaning build artifacts..."

# Remove Next.js build directories
echo "Removing .next directory..."
rm -rf .next
check_command ".next removal"

echo "Removing dist directory..."
rm -rf dist
check_command "dist removal"

echo "Removing build directory..."
rm -rf build
check_command "build removal"

echo "Removing standalone directory..."
rm -rf .next/standalone
check_command "standalone removal"

echo "Removing static directory..."
rm -rf .next/static
check_command "static removal"

echo "✅ Build artifacts cleaned successfully"

# Rebuild the project (only needed for app container)
if [ "$BUILD_TARGET" = "app" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo ""
    echo "🔨 Rebuilding the project..."

    # Rebuild Next.js application
    echo "Building Next.js application..."
    npm run build
    check_command "Next.js build"

    echo "✅ Project rebuilt successfully"
else
    echo ""
    echo "⏭️  Skipping application build (not building app container)"
fi

# Build Docker images with no-cache option
echo ""
echo "🏗️  Building Docker images with no-cache..."

# Build images based on target
if [ "$BUILD_TARGET" = "app" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "Building Next.js Docker image..."
    docker build --no-cache \
        -t $REGISTRY/$PROJECT_NAME/$IMAGE_NAME:latest \
        .
    check_command "Next.js Docker build"
fi

if [ "$BUILD_TARGET" = "redis" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "Building Redis Docker image..."
    docker build --no-cache \
        -f Dockerfile.redis \
        -t $REGISTRY/$PROJECT_NAME/redis:latest \
        .
    check_command "Redis Docker build"
fi

if [ "$BUILD_TARGET" = "postgres" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "Using official PostgreSQL image (no build needed)"
    # PostgreSQL uses official image, no build required
fi

if [ "$BUILD_TARGET" = "minio" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "Using official MinIO image (no build needed)"
    # MinIO uses official image, no build required
fi

echo "✅ Docker images built successfully"

# Handle deployment based on mode
if [ "$REMOTE_DEPLOY" = false ]; then
    # Local deployment - just start containers
    echo ""
    echo "🏠 Starting containers locally..."
    
    # Stop and remove containers based on target
    if [ "$BUILD_TARGET" = "all" ]; then
        docker compose down
        check_command "Stop existing containers"
        docker compose up -d
        check_command "Start all containers"
    else
        docker compose stop $BUILD_TARGET
        check_command "Stop $BUILD_TARGET container"
        docker compose up -d $BUILD_TARGET
        check_command "Start $BUILD_TARGET container"
    fi
    
    echo ""
    echo "✅ Local deployment completed successfully!"
    echo "========================================"
    if [ "$BUILD_TARGET" = "all" ]; then
        echo "Application available at: http://localhost:3001"
        echo "PostgreSQL database: localhost:5434"
        echo "MinIO dashboard: http://localhost:9003"
    elif [ "$BUILD_TARGET" = "app" ]; then
        echo "Application available at: http://localhost:3001"
    elif [ "$BUILD_TARGET" = "postgres" ]; then
        echo "PostgreSQL database: localhost:5434"
    elif [ "$BUILD_TARGET" = "minio" ]; then
        echo "MinIO dashboard: http://localhost:9003"
    elif [ "$BUILD_TARGET" = "redis" ]; then
        echo "Redis cache: localhost:6379"
    fi
    echo ""
    echo "💡 To deploy to remote server, use: ./deploy.sh remote"
    exit 0
fi

# Remote deployment - push to registry and trigger webhooks
# Push Docker images to registry
echo ""
echo "🚀 Pushing Docker images to registry..."

# Login to registry
echo "Logging in to GitHub Container Registry..."
echo $GITHUB_TOKEN | docker login ghcr.io -u $GITHUB_USERNAME --password-stdin
check_command "Docker registry login"

# Push images based on target
if [ "$BUILD_TARGET" = "app" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "Pushing Next.js image..."
    docker push $REGISTRY/$PROJECT_NAME/$IMAGE_NAME:latest
    check_command "Next.js Docker image push"
fi

if [ "$BUILD_TARGET" = "redis" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "Pushing Redis image..."
    docker push $REGISTRY/$PROJECT_NAME/redis:latest
    check_command "Redis Docker image push"
fi

if [ "$BUILD_TARGET" = "postgres" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "PostgreSQL uses official image - no push needed"
fi

if [ "$BUILD_TARGET" = "minio" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "MinIO uses official image - no push needed"
fi

echo "✅ Docker images pushed successfully"

# Show image hashes for comparison
echo ""
echo "🔍 Image hashes for verification:"
echo "Image hash:"
docker inspect --format='{{index .RepoDigests 0}}' $REGISTRY/$PROJECT_NAME/$IMAGE_NAME:latest 2>/dev/null || echo "Not yet available - will be generated after first push"

echo ""
echo "📦 Local image IDs:"
docker images $REGISTRY/$PROJECT_NAME/$IMAGE_NAME:latest --format "table {{.ID}}\t{{.CreatedAt}}" 2>/dev/null || echo "No local image found"

# Trigger redeployment on Dokploy server
echo ""
echo "🔁 Triggering redeployment on Dokploy server..."

# Trigger redeploy based on target
if [ "$BUILD_TARGET" = "app" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "Triggering NWAAlliance app redeploy..."
    curl -X POST $NWAALLIANCE_DEPLOY_WEBHOOK
    check_command "NWAAlliance app redeploy trigger"
fi

if [ "$BUILD_TARGET" = "minio" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "Triggering MinIO redeploy..."
    curl -X POST $MINIO_DEPLOY_WEBHOOK
    check_command "MinIO redeploy trigger"
fi

if [ "$BUILD_TARGET" = "redis" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "Triggering Redis redeploy..."
    curl -X POST $REDIS_DEPLOY_WEBHOOK
    check_command "Redis redeploy trigger"
fi

if [ "$BUILD_TARGET" = "postgres" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "Triggering PostgreSQL redeploy..."
    curl -X POST $POSTGRES_DEPLOY_WEBHOOK
    check_command "PostgreSQL redeploy trigger"
fi

echo ""
echo "✅ Redeployment triggered successfully"

# Summary
echo ""
echo "🎉 Complete deployment finished successfully!"
echo "============================================"
echo "1. Build artifacts cleaned"
if [ "$BUILD_TARGET" = "app" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "2. Project rebuilt"
    echo "3. Docker images built and pushed"
fi
echo "4. Redeployment triggered for containers:"
if [ "$BUILD_TARGET" = "app" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "   - NWAAlliance App"
fi
if [ "$BUILD_TARGET" = "postgres" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "   - PostgreSQL Database"
fi
if [ "$BUILD_TARGET" = "redis" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "   - Redis Cache"
fi
if [ "$BUILD_TARGET" = "minio" ] || [ "$BUILD_TARGET" = "all" ]; then
    echo "   - MinIO Storage"
fi
echo ""
echo "🌐 Application should be available at: http://nwa.cloudns.ch"
echo "📊 Check container logs on the server to verify deployment"
