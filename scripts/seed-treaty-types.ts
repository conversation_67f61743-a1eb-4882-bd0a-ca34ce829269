import { PrismaClient } from '@prisma/client'

// Create Prisma client with explicit database URL
const prisma = new PrismaClient({
  datasources: {
    db: {
      url: 'postgresql://nwa_user:nwa_password@localhost:5434/nwa_portal'
    }
  }
})

async function seedTreatyTypes() {
  console.log('🌱 Seeding treaty types...')

  // Create treaty types
  const treatyTypes = [
    { name: 'Membership Covenant', description: 'Standard membership agreement', category: 'membership' },
    { name: 'Leadership Covenant', description: 'Leadership commitment agreement', category: 'leadership' },
    { name: 'Service Agreement', description: 'Volunteer service commitment', category: 'service' },
  ]

  for (const type of treatyTypes) {
    await prisma.treatyType.upsert({
      where: { name: type.name },
      update: {},
      create: type,
    })
  }

  console.log('✅ Created treaty types')
  console.log('🎉 Treaty types seeding completed successfully!')
  
  const count = await prisma.treatyType.count()
  console.log(`Total treaty types in database: ${count}`)
}

seedTreatyTypes()
  .catch((e) => {
    console.error('❌ Error during seeding:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })