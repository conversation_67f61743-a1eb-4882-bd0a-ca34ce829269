const { PrismaClient } = require('@prisma/client')

async function main() {
  const prisma = new PrismaClient()

  try {
    console.log('🌱 Creating test user...')

    // First, let's check if the member role exists
    const memberRole = await prisma.role.findUnique({
      where: { name: 'member' }
    })

    if (!memberRole) {
      console.log('❌ Member role not found. Please run the full seed script first.')
      return
    }

    // Create a test user
    const testUser = await prisma.user.upsert({
      where: { email: '<EMAIL>' },
      update: {},
      create: {
        email: '<EMAIL>',
        name: 'Test User',
      },
    })

    // Create user profile
    const userProfile = await prisma.userProfile.upsert({
      where: { userId: testUser.id },
      update: {},
      create: {
        userId: testUser.id,
        nwaEmail: '<EMAIL>',
        country: 'USA',
        city: 'New York',
        mobile: '+1234567890',
        bio: 'Test user for development purposes',
      },
    })

    // Assign member role to test user
    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: testUser.id,
          roleId: memberRole.id,
        }
      },
      update: {},
      create: {
        userId: testUser.id,
        roleId: memberRole.id,
      },
    })

    console.log('✅ Created test user for development')
    console.log('📧 Email: <EMAIL>')
    console.log('🔑 You can now log in with this account')
  } catch (error) {
    console.error('❌ Error creating test user:', error)
  } finally {
    await prisma.$disconnect()
  }
}

main()