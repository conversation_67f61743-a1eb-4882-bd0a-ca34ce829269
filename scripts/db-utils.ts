#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client'
import { execSync } from 'child_process'

const prisma = new PrismaClient()

async function testConnection() {
  try {
    await prisma.$connect()
    console.log('✅ Database connection successful')
    
    // Test basic query
    const userCount = await prisma.user.count()
    console.log(`📊 Current user count: ${userCount}`)
    
    await prisma.$disconnect()
    return true
  } catch (error) {
    console.error('❌ Database connection failed:', error)
    return false
  }
}

async function resetDatabase() {
  try {
    console.log('🔄 Resetting database...')
    execSync('npx prisma migrate reset --force', { stdio: 'inherit' })
    console.log('✅ Database reset completed')
  } catch (error) {
    console.error('❌ Database reset failed:', error)
  }
}

async function seedDatabase() {
  try {
    console.log('🌱 Seeding database...')
    execSync('npm run db:seed', { stdio: 'inherit' })
    console.log('✅ Database seeding completed')
  } catch (error) {
    console.error('❌ Database seeding failed:', error)
  }
}

async function generateMigration(name: string) {
  try {
    console.log(`📝 Generating migration: ${name}`)
    execSync(`npx prisma migrate dev --name ${name}`, { stdio: 'inherit' })
    console.log('✅ Migration generated successfully')
  } catch (error) {
    console.error('❌ Migration generation failed:', error)
  }
}

async function main() {
  const command = process.argv[2]
  const arg = process.argv[3]

  switch (command) {
    case 'test':
      await testConnection()
      break
    case 'reset':
      await resetDatabase()
      break
    case 'seed':
      await seedDatabase()
      break
    case 'migrate':
      if (!arg) {
        console.error('❌ Please provide a migration name: npm run db:util migrate <name>')
        process.exit(1)
      }
      await generateMigration(arg)
      break
    case 'setup':
      console.log('🚀 Setting up database...')
      if (await testConnection()) {
        await generateMigration('init')
        await seedDatabase()
      }
      break
    default:
      console.log(`
Database Utilities

Usage: tsx scripts/db-utils.ts <command> [args]

Commands:
  test     - Test database connection
  reset    - Reset database (WARNING: destroys all data)
  seed     - Seed database with initial data
  migrate  - Generate new migration (requires name)
  setup    - Complete database setup (migrate + seed)

Examples:
  tsx scripts/db-utils.ts test
  tsx scripts/db-utils.ts migrate add_user_preferences
  tsx scripts/db-utils.ts setup
      `)
  }
}

main().catch(console.error)