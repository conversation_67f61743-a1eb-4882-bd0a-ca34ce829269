#!/bin/bash

# NWA Alliance Deployment Script
# This script cleans build artifacts, rebuilds the project, builds Docker images,
# pushes them to GHCR, and triggers redeployment on the Dokploy server

set -e  # Exit on any error

# Configuration
DEPLOY_WEBHOOK="http://***************:3000/api/deploy/NpQsKdeioNH5tYZXbHsAq"
REGISTRY="ghcr.io/darrenedward"
PROJECT_NAME="nwaalliance"
IMAGE_NAME="nwa-member-portal"

# Check if script is run with --local flag
LOCAL_ONLY=false
if [ "$1" == "--local" ]; then
    LOCAL_ONLY=true
fi

echo "🚀 Starting NWA Alliance deployment..."
echo "==================================="
if [ "$LOCAL_ONLY" = true ]; then
    echo "_MODE: Local deployment only_"
else
    echo "_MODE: Full deployment with registry push_"
fi
echo ""

# Function to check if a command succeeded
check_command() {
    if [ $? -ne 0 ]; then
        echo "❌ Command failed: $1"
        exit 1
    else
        echo "✅ $1 completed successfully"
    fi
}

# Clean build artifacts
echo "🧹 Cleaning build artifacts..."
echo "Removing .next directory..."
rm -rf .next
check_command ".next removal"

echo "Removing dist directory..."
rm -rf dist
check_command "dist removal"

echo "Removing build directory..."
rm -rf build
check_command "build removal"

echo "Removing standalone directory..."
rm -rf .next/standalone
check_command "standalone removal"

echo "Removing static directory..."
rm -rf .next/static
check_command "static removal"

echo "✅ Build artifacts cleaned successfully"
echo ""

# Rebuild the project
echo "🔨 Rebuilding the project..."
npm run build
check_command "Next.js build"

echo "✅ Project rebuilt successfully"
echo ""

# Build Docker images with no-cache option
echo "🏗️  Building Docker images with no-cache..."
docker build --no-cache \
    -t $REGISTRY/$PROJECT_NAME/$IMAGE_NAME:latest \
    .
check_command "Docker build"

echo "✅ Docker images built successfully"
echo ""

# If local only, stop here and start containers
if [ "$LOCAL_ONLY" = true ]; then
    echo "🏠 Starting containers locally..."
    docker-compose down
    check_command "Stop existing containers"
    
    docker-compose up -d
    check_command "Start containers"
    
    echo ""
    echo "✅ Local deployment completed successfully!"
    echo "=========================================="
    echo "Application should be available at: http://localhost:3001"
    echo "PostgreSQL database: localhost:5434"
    echo "MinIO dashboard: http://localhost:9003"
    exit 0
fi

# Push Docker images to registry
echo "🚀 Pushing Docker images to registry..."
echo "Logging in to GitHub Container Registry..."
echo $GITHUB_TOKEN | docker login ghcr.io -u $GITHUB_USERNAME --password-stdin
check_command "Docker registry login"

echo "Pushing image..."
docker push $REGISTRY/$PROJECT_NAME/$IMAGE_NAME:latest
check_command "Docker image push"

echo "✅ Docker images pushed successfully"
echo ""

# Show image hashes for comparison
echo "🔍 Image hashes for verification:"
echo "Image hash:"
docker inspect --format='{{index .RepoDigests 0}}' $REGISTRY/$PROJECT_NAME/$IMAGE_NAME:latest 2>/dev/null || echo "Not yet available - will be generated after first push"
echo ""

echo "📦 Local image IDs:"
docker images $REGISTRY/$PROJECT_NAME/$IMAGE_NAME:latest --format "table {{.ID}}\t{{.CreatedAt}}" 2>/dev/null || echo "No local image found"
echo ""

# Trigger redeployment on Dokploy server
echo "🔁 Triggering redeployment on Dokploy server..."
echo "Triggering redeploy..."
curl -X POST $DEPLOY_WEBHOOK
check_command "Redeploy trigger"

echo ""
echo "✅ Redeployment triggered successfully"
echo ""

# Summary
echo "🎉 Complete deployment finished successfully!"
echo "=========================================="
echo "1. Build artifacts cleaned"
echo "2. Project rebuilt"
echo "3. Docker images built and pushed"
echo "4. Redeployment triggered on Dokploy server"
echo ""
echo "🌐 Application should be available at: http://nwa.cloudns.ch"
echo "📊 Check container logs on the server to verify deployment"