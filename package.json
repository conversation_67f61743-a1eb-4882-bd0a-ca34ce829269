{"name": "nwa-member-portal", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "next dev -p 3001", "build": "NODE_ENV=production next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev", "prisma:seed": "prisma db seed", "prisma:studio": "prisma studio", "verify-api": "node scripts/verify-api-endpoints.js"}, "prisma": {"seed": "node --loader ts-node/esm prisma/seed.ts"}, "dependencies": {"@aws-sdk/client-s3": "^3.864.0", "@hookform/resolvers": "^3.9.0", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.1.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.0", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.1", "@tanstack/react-table": "^8.20.5", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.7", "@types/nodemailer": "^7.0.0", "@types/qrcode": "^1.5.5", "@types/uuid": "^10.0.0", "autoprefixer": "^10.4.21", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.0", "date-fns": "^3.6.0", "dotenv": "^17.2.1", "drizzle-orm": "^0.44.4", "embla-carousel-react": "^8.3.0", "framer-motion": "^11.0.3", "glob": "^9.3.5", "input-otp": "^1.2.4", "ioredis": "^5.7.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.439.0", "mini-svg-data-uri": "^1.4.4", "minio": "^8.0.1", "next": "^15.5.0", "next-auth": "4.24.10", "next-themes": "^0.3.0", "node-jose": "^2.2.0", "nodemailer": "^6.10.1", "qrcode": "^1.5.4", "rate-limiter-flexible": "^7.2.0", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-hook-form": "^7.53.0", "react-resizable-panels": "^2.1.3", "recharts": "^2.12.7", "sonner": "^1.7.4", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^10.0.0", "vaul": "^1.0.0", "zod": "^3.23.8"}, "devDependencies": {"@testing-library/jest-dom": "^6.5.0", "@testing-library/react": "^16.0.1", "@testing-library/user-event": "^14.5.2", "@types/jest": "^29.5.13", "@types/jsonwebtoken": "^9.0.7", "@types/node": "^22.5.2", "@types/react": "19.0.1", "@types/react-dom": "19.0.2", "@typescript-eslint/eslint-plugin": "^8.4.0", "@typescript-eslint/parser": "^8.4.0", "eslint": "^9.9.1", "eslint-config-next": "15.1.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8.4.41", "prisma": "^6.1.0", "tailwindcss": "^3.4.10", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "^5.7.0"}}