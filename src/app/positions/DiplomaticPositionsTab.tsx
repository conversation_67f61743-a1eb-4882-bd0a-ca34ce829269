'use client';

import React, { useState } from 'react';
import { toast } from 'sonner';
import { TitlePositionManager } from './TitlePositionManager';

interface Position {
  id: string;
  title: string;
  description: string | null;
  level: number;
  parentId: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  parent?: Position | null;
  children?: Position[];
}

interface Title {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export const DiplomaticPositionsTab: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'positions' | 'associations'>('positions');
  
  return (
    <div className="space-y-6">
      {/* Tab Navigation */}
      <div className="border-b border-gray-200 dark:border-gray-700">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('positions')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'positions'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            Manage Positions
          </button>
          <button
            onClick={() => setActiveTab('associations')}
            className={`py-4 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'associations'
                ? 'border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
            }`}
          >
            Title-Position Associations
          </button>
        </nav>
      </div>

      {/* Tab Content */}
      <div>
        {activeTab === 'positions' ? <PositionManager /> : <TitlePositionManager />}
      </div>
    </div>
  );
};

const PositionManager: React.FC = () => {
  const [titles, setTitles] = useState<Title[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTitle, setSelectedTitle] = useState<string>('');
  const [filteredPositions, setFilteredPositions] = useState<Position[]>([]);
  const [isEditing, setIsEditing] = useState<string | null>(null);
  const [newPosition, setNewPosition] = useState({ 
    title: '', 
    description: '',
    level: 1,
    parentId: ''
  });
  const [editPosition, setEditPosition] = useState({ 
    title: '', 
    description: '',
    level: 1,
    parentId: ''
  });

  // Fetch titles and positions from API
  React.useEffect(() => {
    Promise.all([
      fetchTitles(),
      fetchPositions()
    ]).finally(() => {
      setLoading(false);
    });
  }, []);

  const fetchTitles = async () => {
    try {
      const response = await fetch('/api/positions/titles');
      
      if (!response.ok) {
        throw new Error('Failed to fetch titles');
      }
      
      const data = await response.json();
      setTitles(data);
    } catch (error) {
      console.error('Error fetching titles:', error);
      toast.error('Failed to load titles');
    }
  };

  const fetchPositions = async (titleId?: string) => {
    try {
      const url = titleId 
        ? `/api/positions/positions?titleId=${titleId}`
        : '/api/positions/positions';
        
      const response = await fetch(url);
      
      if (!response.ok) {
        throw new Error('Failed to fetch positions');
      }
      
      const data = await response.json();
      if (titleId) {
        setFilteredPositions(data);
      } else {
        setPositions(data);
      }
    } catch (error) {
      console.error('Error fetching positions:', error);
      toast.error('Failed to load positions');
    }
  };

  // Filter positions when a title is selected
  const handleTitleChange = async (titleId: string) => {
    setSelectedTitle(titleId);
    if (titleId) {
      await fetchPositions(titleId);
    } else {
      setFilteredPositions([]);
    }
  };

  const handleCreatePosition = async () => {
    if (!newPosition.title.trim()) {
      toast.error('Position title is required');
      return;
    }
    
    try {
      const response = await fetch('/api/positions/positions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: newPosition.title,
          description: newPosition.description,
          level: newPosition.level,
          parentId: newPosition.parentId || null,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create position');
      }
      
      const createdPosition = await response.json();
      setPositions([...positions, createdPosition]);
      setNewPosition({ title: '', description: '', level: 1, parentId: '' });
      toast.success('Position created successfully');
      
      // Update filtered positions if we're viewing a specific title
      if (selectedTitle) {
        await fetchPositions(selectedTitle);
      }
    } catch (error: any) {
      console.error('Error creating position:', error);
      toast.error(error.message || 'Failed to create position');
    }
  };

  const handleStartEdit = (position: Position) => {
    setIsEditing(position.id);
    setEditPosition({ 
      title: position.title, 
      description: position.description || '',
      level: position.level,
      parentId: position.parentId || ''
    });
  };

  const handleUpdatePosition = async (id: string) => {
    if (!editPosition.title.trim()) {
      toast.error('Position title is required');
      return;
    }
    
    try {
      const response = await fetch(`/api/positions/positions/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: editPosition.title,
          description: editPosition.description,
          level: editPosition.level,
          parentId: editPosition.parentId || null,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update position');
      }
      
      const updatedPosition = await response.json();
      setPositions(positions.map(position => 
        position.id === id ? updatedPosition : position
      ));
      
      // Update filtered positions if we're viewing a specific title
      if (selectedTitle) {
        setFilteredPositions(filteredPositions.map(position => 
          position.id === id ? updatedPosition : position
        ));
      }
      
      setIsEditing(null);
      toast.success('Position updated successfully');
    } catch (error: any) {
      console.error('Error updating position:', error);
      toast.error(error.message || 'Failed to update position');
    }
  };

  const handleToggleStatus = async (id: string) => {
    try {
      const position = positions.find(p => p.id === id);
      if (!position) return;
      
      const response = await fetch(`/api/positions/positions/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: position.title,
          description: position.description,
          level: position.level,
          parentId: position.parentId,
          isActive: !position.isActive,
        }),
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update position');
      }
      
      const updatedPosition = await response.json();
      setPositions(positions.map(pos => 
        pos.id === id ? updatedPosition : pos
      ));
      
      // Update filtered positions if we're viewing a specific title
      if (selectedTitle) {
        setFilteredPositions(filteredPositions.map(pos => 
          pos.id === id ? updatedPosition : pos
        ));
      }
      
      toast.success(`Position ${position.isActive ? 'deactivated' : 'activated'} successfully`);
    } catch (error: any) {
      console.error('Error updating position:', error);
      toast.error(error.message || 'Failed to update position');
    }
  };

  const handleDeletePosition = async (id: string) => {
    try {
      const response = await fetch(`/api/positions/positions/${id}`, {
        method: 'DELETE',
      });
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to delete position');
      }
      
      setPositions(positions.filter(position => position.id !== id));
      
      // Update filtered positions if we're viewing a specific title
      if (selectedTitle) {
        setFilteredPositions(filteredPositions.filter(position => position.id !== id));
      }
      
      toast.success('Position deleted successfully');
    } catch (error: any) {
      console.error('Error deleting position:', error);
      toast.error(error.message || 'Failed to delete position');
    }
  };

  // Get positions to display (either filtered by title or all positions)
  const displayPositions = selectedTitle ? filteredPositions : positions;

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Create New Position</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="positionTitle" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Position Title <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="positionTitle"
              value={newPosition.title}
              onChange={(e) => setNewPosition({ ...newPosition, title: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:text-white"
              placeholder="e.g., Cultural Attaché"
            />
          </div>
          <div>
            <label htmlFor="positionDescription" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Description
            </label>
            <input
              type="text"
              id="positionDescription"
              value={newPosition.description}
              onChange={(e) => setNewPosition({ ...newPosition, description: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:text-white"
              placeholder="Brief description of the position"
            />
          </div>
          <div>
            <label htmlFor="positionLevel" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Level
            </label>
            <select
              id="positionLevel"
              value={newPosition.level}
              onChange={(e) => setNewPosition({ ...newPosition, level: parseInt(e.target.value) })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:text-white"
            >
              {[1, 2, 3, 4, 5].map(level => (
                <option key={level} value={level}>Level {level}</option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="positionParent" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Parent Position
            </label>
            <select
              id="positionParent"
              value={newPosition.parentId}
              onChange={(e) => setNewPosition({ ...newPosition, parentId: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:text-white"
            >
              <option value="">No parent</option>
              {positions.map(position => (
                <option key={position.id} value={position.id}>{position.title}</option>
              ))}
            </select>
          </div>
        </div>
        <div className="mt-4">
          <button
            onClick={handleCreatePosition}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Create Position
          </button>
        </div>
      </div>

      <div>
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-medium text-gray-900 dark:text-white">Positions</h2>
          <div className="flex items-center space-x-2">
            <label htmlFor="filterTitle" className="text-sm text-gray-700 dark:text-gray-300">
              Filter by Title:
            </label>
            <select
              id="filterTitle"
              value={selectedTitle}
              onChange={(e) => handleTitleChange(e.target.value)}
              className="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:text-white"
            >
              <option value="">All Titles</option>
              {titles.map(title => (
                <option key={title.id} value={title.id}>{title.name}</option>
              ))}
            </select>
            {selectedTitle && (
              <button
                onClick={() => {
                  setSelectedTitle('');
                  setFilteredPositions([]);
                }}
                className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              >
                Clear Filter
              </button>
            )}
          </div>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Position
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Level
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {displayPositions.map((position) => (
                <tr key={position.id}>
                  {isEditing === position.id ? (
                    <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="text"
                          value={editPosition.title}
                          onChange={(e) => setEditPosition({ ...editPosition, title: e.target.value })}
                          className="w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="text"
                          value={editPosition.description}
                          onChange={(e) => setEditPosition({ ...editPosition, description: e.target.value })}
                          className="w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <select
                          value={editPosition.level}
                          onChange={(e) => setEditPosition({ ...editPosition, level: parseInt(e.target.value) })}
                          className="w-full px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-700 dark:text-white"
                        >
                          {[1, 2, 3, 4, 5].map(level => (
                            <option key={level} value={level}>Level {level}</option>
                          ))}
                        </select>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {position.isActive ? (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            Active
                          </span>
                        ) : (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                            Inactive
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleUpdatePosition(position.id)}
                          className="text-green-600 hover:text-green-900 dark:text-green-400 dark:hover:text-green-300 mr-3"
                        >
                          Save
                        </button>
                        <button
                          onClick={() => setIsEditing(null)}
                          className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-300"
                        >
                          Cancel
                        </button>
                      </td>
                    </>
                  ) : (
                    <>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                        {position.title}
                        {position.parentId && position.parent && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">
                            Reports to: {position.parent.title}
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {position.description || '-'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        Level {position.level}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {position.isActive ? (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-green-100 text-green-800">
                            Active
                          </span>
                        ) : (
                          <span className="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-red-100 text-red-800">
                            Inactive
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleStartEdit(position)}
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleToggleStatus(position.id)}
                          className="text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300 mr-3"
                        >
                          {position.isActive ? 'Deactivate' : 'Activate'}
                        </button>
                        <button
                          onClick={() => handleDeletePosition(position.id)}
                          className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300"
                        >
                          Delete
                        </button>
                      </td>
                    </>
                  )}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};