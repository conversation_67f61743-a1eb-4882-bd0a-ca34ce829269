'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';

interface Title {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
}

interface Position {
  id: string;
  title: string;
  description: string | null;
  level: number;
  parentId: string | null;
  isActive: boolean;
}

interface TitlePosition {
  id: string;
  titleId: string;
  positionId: string;
  title: Title;
  position: Position;
}

export const TitlePositionManager: React.FC = () => {
  const [titles, setTitles] = useState<Title[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [titlePositions, setTitlePositions] = useState<TitlePosition[]>([]);
  const [selectedTitle, setSelectedTitle] = useState<string>('');
  const [selectedPosition, setSelectedPosition] = useState<string>('');
  const [loading, setLoading] = useState(true);

  // Fetch all data on component mount
  useEffect(() => {
    Promise.all([
      fetchTitles(),
      fetchPositions(),
      fetchTitlePositions()
    ]).finally(() => {
      setLoading(false);
    });
  }, []);

  const fetchTitles = async () => {
    try {
      const response = await fetch('/api/positions/titles');
      
      if (!response.ok) {
        throw new Error('Failed to fetch titles');
      }
      
      const data = await response.json();
      setTitles(data);
    } catch (error) {
      console.error('Error fetching titles:', error);
      toast.error('Failed to load titles');
    }
  };

  const fetchPositions = async () => {
    try {
      const response = await fetch('/api/positions/positions');
      
      if (!response.ok) {
        throw new Error('Failed to fetch positions');
      }
      
      const data = await response.json();
      setPositions(data);
    } catch (error) {
      console.error('Error fetching positions:', error);
      toast.error('Failed to load positions');
    }
  };

  const fetchTitlePositions = async () => {
    try {
      const response = await fetch('/api/positions/title-positions');
      
      if (!response.ok) {
        throw new Error('Failed to fetch title-position relationships');
      }
      
      const data = await response.json();
      setTitlePositions(data);
    } catch (error) {
      console.error('Error fetching title-position relationships:', error);
      toast.error('Failed to load title-position relationships');
    }
  };

  const handleAssociate = async () => {
    if (!selectedTitle || !selectedPosition) {
      toast.error('Please select both a title and a position');
      return;
    }

    try {
      const response = await fetch('/api/positions/title-positions', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          titleId: selectedTitle,
          positionId: selectedPosition,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to associate title and position');
      }

      const newTitlePosition = await response.json();
      setTitlePositions([...titlePositions, newTitlePosition]);
      setSelectedTitle('');
      setSelectedPosition('');
      toast.success('Title and position associated successfully');
    } catch (error: any) {
      console.error('Error associating title and position:', error);
      toast.error(error.message || 'Failed to associate title and position');
    }
  };

  const handleDisassociate = async (titleId: string, positionId: string) => {
    try {
      const response = await fetch('/api/positions/title-positions', {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          titleId,
          positionId,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to disassociate title and position');
      }

      setTitlePositions(titlePositions.filter(tp => 
        !(tp.titleId === titleId && tp.positionId === positionId)
      ));
      toast.success('Title and position disassociated successfully');
    } catch (error: any) {
      console.error('Error disassociating title and position:', error);
      toast.error(error.message || 'Failed to disassociate title and position');
    }
  };

  // Get positions associated with a specific title
  const getPositionsForTitle = (titleId: string) => {
    return titlePositions
      .filter(tp => tp.titleId === titleId)
      .map(tp => tp.position);
  };

  // Get titles associated with a specific position
  const getTitlesForPosition = (positionId: string) => {
    return titlePositions
      .filter(tp => tp.positionId === positionId)
      .map(tp => tp.title);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-32">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Associate Titles and Positions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label htmlFor="titleSelect" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Title
            </label>
            <select
              id="titleSelect"
              value={selectedTitle}
              onChange={(e) => setSelectedTitle(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:text-white"
            >
              <option value="">Select a title</option>
              {titles.map(title => (
                <option key={title.id} value={title.id}>{title.name}</option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="positionSelect" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Position
            </label>
            <select
              id="positionSelect"
              value={selectedPosition}
              onChange={(e) => setSelectedPosition(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 dark:bg-gray-600 dark:text-white"
            >
              <option value="">Select a position</option>
              {positions.map(position => (
                <option key={position.id} value={position.id}>{position.title}</option>
              ))}
            </select>
          </div>
        </div>
        <div className="mt-4">
          <button
            onClick={handleAssociate}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Associate Title and Position
          </button>
        </div>
      </div>

      <div>
        <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Current Associations</h2>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
            <thead className="bg-gray-50 dark:bg-gray-700">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Title
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Associated Positions
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
              {titles.map(title => {
                const associatedPositions = getPositionsForTitle(title.id);
                return (
                  <tr key={title.id}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                      {title.name}
                    </td>
                    <td className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400">
                      {associatedPositions.length > 0 ? (
                        <ul className="list-disc pl-5 space-y-1">
                          {associatedPositions.map(position => (
                            <li key={position.id} className="flex justify-between items-center">
                              <span>{position.title}</span>
                              <button
                                onClick={() => handleDisassociate(title.id, position.id)}
                                className="text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300 text-xs ml-2"
                              >
                                Remove
                              </button>
                            </li>
                          ))}
                        </ul>
                      ) : (
                        <span className="text-gray-400">No positions associated</span>
                      )}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                      {associatedPositions.length} position{associatedPositions.length !== 1 ? 's' : ''}
                    </td>
                  </tr>
                );
              })}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};