'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { TreatyUploadForm } from '@/components/treaties/TreatyUploadForm';
import { TreatyList } from '@/components/treaties/TreatyList';
import { TreatySearch } from '@/components/treaties/TreatySearch';

export default function TreatiesPage() {
  const [activeTab, setActiveTab] = useState<'upload' | 'view'>('upload');
  const [searchQuery, setSearchQuery] = useState('');
  const [treaties, setTreaties] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    totalCount: 0,
    pageSize: 10
  });

  const fetchTreaties = useCallback(async () => {
    setLoading(true);
    try {
      const response = await fetch(
        `/api/treaties?search=${encodeURIComponent(searchQuery)}&page=${pagination.currentPage}&limit=${pagination.pageSize}`
      );
      
      if (!response.ok) {
        throw new Error('Failed to fetch treaties');
      }
      
      const data = await response.json();
      setTreaties(data.treaties);
      setPagination(prev => ({
        ...prev,
        totalPages: data.totalPages,
        totalCount: data.totalCount
      }));
    } catch (error) {
      console.error('Error fetching treaties:', error);
    } finally {
      setLoading(false);
    }
  }, [searchQuery, pagination.currentPage, pagination.pageSize]);

  // Fetch treaties when viewing the list or when search/pagination changes
  useEffect(() => {
    if (activeTab === 'view') {
      fetchTreaties();
    }
  }, [activeTab, fetchTreaties]);

  // Fetch recent treaties for the upload tab
  useEffect(() => {
    if (activeTab === 'upload') {
      fetchTreaties();
    }
  }, [activeTab, fetchTreaties]);

  const handleSearch = (query: string) => {
    setSearchQuery(query);
    setPagination(prev => ({ ...prev, currentPage: 1 })); // Reset to first page on new search
  };

  const handlePageChange = (page: number) => {
    setPagination(prev => ({ ...prev, currentPage: page }));
  };

  return (
    <DashboardLayout>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="p-6">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-white mb-6">Treaties</h1>
          
          {/* Tab Navigation */}
          <div className="border-b border-gray-200 dark:border-gray-700 mb-6">
            <nav className="flex space-x-8">
              <button
                onClick={() => setActiveTab('upload')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'upload'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                  </svg>
                  Upload Treaty
                </div>
              </button>
              <button
                onClick={() => setActiveTab('view')}
                className={`py-4 px-1 border-b-2 font-medium text-sm ${
                  activeTab === 'view'
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400 dark:border-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <div className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                  </svg>
                  View Treaties
                </div>
              </button>
            </nav>
          </div>

          {/* Tab Content */}
          {activeTab === 'upload' ? (
            <div>
              <TreatyUploadForm onUploadSuccess={fetchTreaties} />
              <div className="mt-8">
                <h2 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Recent Treaties</h2>
                {loading ? (
                  <div className="animate-pulse space-y-4">
                    {[...Array(5)].map((_, i) => (
                      <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                    ))}
                  </div>
                ) : (
                  <TreatyList 
                    treaties={treaties.slice(0, 5)} 
                    showPagination={false}
                    onViewDetails={(id) => console.log('View treaty:', id)}
                  />
                )}
              </div>
            </div>
          ) : (
            <div>
              <TreatySearch onSearch={handleSearch} />
              {loading ? (
                <div className="animate-pulse space-y-4 mt-6">
                  {[...Array(5)].map((_, i) => (
                    <div key={i} className="h-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                  ))}
                </div>
              ) : (
                <TreatyList 
                  treaties={treaties} 
                  showPagination={true}
                  pagination={pagination}
                  onPageChange={handlePageChange}
                  onViewDetails={(id) => console.log('View treaty:', id)}
                />
              )}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}