'use server'

import { prisma } from '@/lib/prisma'
import { ApiKeyService } from '@/lib/services/api-key'

/**
 * Fetch all remote servers (projects)
 */
export async function getRemoteServers() {
  try {
    const servers = await prisma.project.findMany({
      orderBy: {
        createdAt: 'desc'
      }
    })
    
    return { success: true, data: servers }
  } catch (error: any) {
    console.error('Failed to fetch remote servers:', error)
    return { success: false, error: 'Failed to fetch remote servers' }
  }
}

/**
 * Create a new remote server (project)
 */
export async function createRemoteServer(data: {
  name: string
  url: string
  description?: string
}) {
  try {
    // Create API key service and generate new API key
    const apiKeyService = new ApiKeyService()
    const apiKey = apiKeyService.generateApiKey()
    const apiKeyHash = apiKeyService.hashApiKey(apiKey)
    
    // Create new project
    const server = await prisma.project.create({
      data: {
        name: data.name,
        description: data.description,
        apiKeyHash,
        allowedOrigins: [data.url],
        isActive: true
      }
    })
    
    return { success: true, data: { ...server, apiKey } }
  } catch (error: any) {
    console.error('Failed to create remote server:', error)
    return { success: false, error: 'Failed to create remote server' }
  }
}

/**
 * Update a remote server (project)
 */
export async function updateRemoteServer(id: string, data: {
  name?: string
  url?: string
  description?: string
  isActive?: boolean
}) {
  try {
    // Get existing project
    const existingProject = await prisma.project.findUnique({
      where: { id }
    })
    
    if (!existingProject) {
      return { success: false, error: 'Remote server not found' }
    }
    
    // Update project
    const server = await prisma.project.update({
      where: { id },
      data: {
        ...(data.name && { name: data.name }),
        ...(data.description !== undefined && { description: data.description }),
        ...(data.url && { allowedOrigins: [data.url] }),
        ...(data.isActive !== undefined && { isActive: data.isActive })
      }
    })
    
    return { success: true, data: server }
  } catch (error: any) {
    console.error('Failed to update remote server:', error)
    return { success: false, error: 'Failed to update remote server' }
  }
}

/**
 * Delete a remote server (project)
 */
export async function deleteRemoteServer(id: string) {
  try {
    // Check if project exists
    const existingProject = await prisma.project.findUnique({
      where: { id }
    })
    
    if (!existingProject) {
      return { success: false, error: 'Remote server not found' }
    }
    
    // Delete project
    await prisma.project.delete({
      where: { id }
    })
    
    return { success: true }
  } catch (error: any) {
    console.error('Failed to delete remote server:', error)
    return { success: false, error: 'Failed to delete remote server' }
  }
}

/**
 * Test connection to a remote server
 */
export async function testRemoteServerConnection(url: string, apiKey: string) {
  try {
    // In a real implementation, we would test the connection here
    // For now, we'll simulate a successful connection
    console.log(`Testing connection to ${url}`)
    
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    return { success: true, message: 'Connection successful' }
  } catch (error: any) {
    console.error('Failed to test remote server connection:', error)
    return { success: false, error: 'Failed to test connection' }
  }
}