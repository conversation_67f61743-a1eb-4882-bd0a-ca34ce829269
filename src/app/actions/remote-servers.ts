'use server'

import { prisma } from '@/lib/prisma'

/**
 * Fetch permissions from a remote server
 */
export async function fetchRemoteServerPermissions(serverId: string) {
  try {
    console.log('=== FETCHING REMOTE SERVER PERMISSIONS ===')
    console.log('Server ID:', serverId)
    
    // Get the remote server
    const remoteServers: any = await prisma.$queryRawUnsafe(`SELECT * FROM remote_servers WHERE id = $1`, serverId)
    const server = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : null;
    
    console.log('Server Data:', server)
    
    if (!server) {
      return { success: false, error: 'Remote server not found' }
    }
    
    // Make an HTTP request to the remote server's /api/permissions endpoint to fetch permissions and roles
    const permissionUrl = `${server.url}/api/permissions`
    console.log('Fetching permissions from remote server:', permissionUrl)
    console.log('Using API Key:', server.apiKey)
    
    const infoResponse = await fetch(permissionUrl, {
      headers: {
        'Authorization': `Bearer ${server.apiKey}`,
        'Content-Type': 'application/json'
      }
    })
    
    console.log('Remote server response status:', infoResponse.status)
    
    if (!infoResponse.ok) {
      return { success: false, error: 'Error connecting to remote server' }
    }
    
    const info = await infoResponse.json()
    console.log('Remote server permissions response:', info)
    
    // Transform the response to match the expected format
    // Handle both array of strings and array of objects
    const permissions = (info.permissions || []).map((perm: any) => {
      if (typeof perm === 'string') {
        return {
          name: perm,
          description: ''
        }
      } else {
        return {
          name: perm.name,
          description: perm.description || ''
        }
      }
    })
    
    const roles = (info.roles || []).map((role: any) => {
      if (typeof role === 'string') {
        return {
          name: role,
          description: '',
          permissions: []
        }
      } else {
        return {
          name: role.name,
          description: role.description || '',
          permissions: role.permissions || []
        }
      }
    })
    
    return { 
      success: true, 
      data: {
        permissions,
        roles
      }
    }
  } catch (error: any) {
    console.error('Failed to fetch remote server permissions:', error)
    console.error('Error type:', typeof error)
    console.error('Error message:', error.message || 'No message')
    return { success: false, error: 'Failed to fetch permissions' }
  }
}

/**
 * Assign permissions to a user for a specific remote server
 */
export async function assignUserPermissions(
  userId: string,
  serverId: string,
  permissionNames: string[]
) {
  try {
    // Get the remote server (project) and user
    const [server, user] = await Promise.all([
      prisma.project.findUnique({ where: { id: serverId } }),
      prisma.user.findUnique({ where: { id: userId } })
    ])
    
    if (!server) {
      return { success: false, error: 'Remote server not found' }
    }
    
    if (!user) {
      return { success: false, error: 'User not found' }
    }
    
    // Get the scopes that match the permission names
    const scopes = await prisma.scope.findMany({
      where: {
        name: {
          in: permissionNames
        }
      }
    })
    
    // Create UserProjectScope entries for each permission
    const userProjectScopes = await Promise.all(
      scopes.map(scope => 
        prisma.userProjectScope.upsert({
          where: {
            userId_projectId_scopeId: {
              userId,
              projectId: serverId,
              scopeId: scope.id
            }
          },
          update: {},
          create: {
            userId,
            projectId: serverId,
            scopeId: scope.id
          }
        })
      )
    )
    
    return { success: true, data: userProjectScopes }
  } catch (error: any) {
    console.error('Failed to assign user permissions:', error)
    return { success: false, error: 'Failed to assign permissions' }
  }
}

/**
 * Remove permissions from a user for a specific remote server
 */
export async function removeUserPermissions(
  userId: string,
  serverId: string,
  permissionNames: string[]
) {
  try {
    // Get the scopes that match the permission names
    const scopes = await prisma.scope.findMany({
      where: {
        name: {
          in: permissionNames
        }
      }
    })
    
    // Delete UserProjectScope entries for each permission
    await Promise.all(
      scopes.map(scope => 
        prisma.userProjectScope.deleteMany({
          where: {
            userId,
            projectId: serverId,
            scopeId: scope.id
          }
        })
      )
    )
    
    return { success: true }
  } catch (error: any) {
    console.error('Failed to remove user permissions:', error)
    return { success: false, error: 'Failed to remove permissions' }
  }
}

/**
 * Get user permissions for a specific remote server
 */
export async function getUserPermissions(userId: string, serverId: string) {
  try {
    const userProjectScopes = await prisma.userProjectScope.findMany({
      where: {
        userId,
        projectId: serverId
      },
      include: {
        scope: true
      }
    })
    
    const permissions = userProjectScopes.map(ups => ups.scope)
    
    return { success: true, data: permissions }
  } catch (error: any) {
    console.error('Failed to get user permissions:', error)
    return { success: false, error: 'Failed to get permissions' }
  }
}