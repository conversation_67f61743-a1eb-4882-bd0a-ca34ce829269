'use server'

import { prisma } from '@/lib/prisma'

/**
 * Fetch all remote servers with OAuth information
 */
export async function getAllRemoteServers() {
  try {
    // Use raw SQL query to fetch from remote_servers table
    const remoteServers: any = await prisma.$queryRawUnsafe(`
      SELECT 
        id,
        name,
        url,
        "apiKey",
        description,
        "isActive",
        "createdAt",
        "updatedAt",
        client_id as "clientId",
        client_secret as "clientSecret",
        redirect_uris as "redirectUris"
      FROM remote_servers
      ORDER BY "createdAt" DESC
    `)
    
    return { success: true, data: remoteServers }
  } catch (error: any) {
    console.error('Failed to fetch remote servers:', error)
    return { success: false, error: 'Failed to fetch remote servers' }
  }
}

/**
 * Fetch a specific remote server with OAuth information
 */
export async function getRemoteServerById(id: string) {
  try {
    // Use raw SQL query to fetch specific remote server
    const remoteServers: any = await prisma.$queryRawUnsafe(`
      SELECT 
        id,
        name,
        url,
        "apiKey",
        description,
        "isActive",
        "createdAt",
        "updatedAt",
        client_id as "clientId",
        client_secret as "clientSecret",
        redirect_uris as "redirectUris"
      FROM remote_servers
      WHERE id = $1
    `, id)
    
    const server = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : null
    
    if (!server) {
      return { success: false, error: 'Remote server not found' }
    }
    
    return { success: true, data: server }
  } catch (error: any) {
    console.error('Failed to fetch remote server:', error)
    return { success: false, error: 'Failed to fetch remote server' }
  }
}

/**
 * Update a remote server's OAuth configuration
 */
export async function updateRemoteServerConfig(id: string, data: {
  name?: string
  url?: string
  apiKey?: string
  description?: string
  isActive?: boolean
  clientId?: string
  clientSecret?: string
  redirectUris?: string[]
}) {
  try {
    // First check if remote server exists
    const existingServers: any = await prisma.$queryRawUnsafe(
      `SELECT id FROM remote_servers WHERE id = $1`,
      id
    )
    
    const existingServer = Array.isArray(existingServers) && existingServers.length > 0 ? existingServers[0] : null
    
    if (!existingServer) {
      return { success: false, error: 'Remote server not found' }
    }
    
    // Build update query dynamically
    const updates: string[] = []
    const values: any[] = []
    let valueIndex = 2 // $1 is reserved for id
    
    if (data.name !== undefined) {
      updates.push(`name = ${valueIndex}`)
      values.push(data.name)
      valueIndex++
    }
    
    if (data.url !== undefined) {
      updates.push(`url = ${valueIndex}`)
      values.push(data.url)
      valueIndex++
    }
    
    if (data.description !== undefined) {
      updates.push(`description = ${valueIndex}`)
      values.push(data.description)
      valueIndex++
    }
    
    if (data.isActive !== undefined) {
      updates.push(`"isActive" = ${valueIndex}`)
      values.push(data.isActive)
      valueIndex++
    }
    
    if (data.clientId !== undefined) {
      updates.push(`client_id = ${valueIndex}`)
      values.push(data.clientId)
      valueIndex++
    }
    
    if (data.clientSecret !== undefined) {
      updates.push(`client_secret = ${valueIndex}`)
      values.push(data.clientSecret)
      valueIndex++
    }
    
    if (data.redirectUris !== undefined) {
      updates.push(`redirect_uris = ${valueIndex}`)
      values.push(data.redirectUris)
      valueIndex++
    }
    
    if (data.apiKey !== undefined) {
      updates.push(`"apiKey" = ${valueIndex}`)
      values.push(data.apiKey)
      valueIndex++
    }
    
    // Always update the updated_at timestamp
    updates.push(`"updatedAt" = NOW()`)
    
    if (updates.length === 1) {
      // Only timestamp update, no other changes
      return { success: true, message: 'No changes to update' }
    }
    
    // Build the final query
    const query = `
      UPDATE remote_servers 
      SET ${updates.join(', ')}
      WHERE id = $1
      RETURNING 
        id,
        name,
        url,
        "apiKey",
        description,
        "isActive",
        "createdAt",
        "updatedAt",
        client_id as "clientId",
        client_secret as "clientSecret",
        redirect_uris as "redirectUris"
    `
    
    // Add id to values array
    values.unshift(id)
    
    const updatedServers: any = await prisma.$queryRawUnsafe(query, ...values)
    const updatedServer = Array.isArray(updatedServers) && updatedServers.length > 0 ? updatedServers[0] : null
    
    return { success: true, data: updatedServer }
  } catch (error: any) {
    console.error('Failed to update remote server:', error)
    return { success: false, error: 'Failed to update remote server' }
  }
}

/**
 * Delete a remote server
 */
export async function deleteRemoteServerById(id: string) {
  try {
    // First check if remote server exists
    const existingServers: any = await prisma.$queryRawUnsafe(
      `SELECT id FROM remote_servers WHERE id = $1`,
      id
    )
    
    const existingServer = Array.isArray(existingServers) && existingServers.length > 0 ? existingServers[0] : null
    
    if (!existingServer) {
      return { success: false, error: 'Remote server not found' }
    }
    
    // Delete the remote server
    await prisma.$executeRawUnsafe(
      `DELETE FROM remote_servers WHERE id = $1`,
      id
    )
    
    return { success: true }
  } catch (error: any) {
    console.error('Failed to delete remote server:', error)
    return { success: false, error: 'Failed to delete remote server' }
  }
}