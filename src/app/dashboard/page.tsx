'use client';

import React from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import Link from 'next/link';

export default function DashboardPage() {
  return (
    <DashboardLayout>
      <div className="flex flex-col">
        <h1 className="text-3xl font-bold text-gray-800 dark:text-white mb-6">
          Member Dashboard
        </h1>
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <div className="lg:col-span-3">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6 mb-6">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
                Welcome to NWA Member Portal
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-4">
                This is your personalized dashboard where you can manage your profile, 
                track your ordinances and treaties, and access all member resources.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <Link href="/profile" className="block">
                  <div className="bg-emerald-50 dark:bg-emerald-900/20 rounded-lg p-4 hover:bg-emerald-100 dark:hover:bg-emerald-900/30 transition-colors cursor-pointer">
                    <h3 className="font-semibold text-emerald-800 dark:text-emerald-200 mb-1">
                      Profile
                    </h3>
                    <p className="text-sm text-emerald-600 dark:text-emerald-400">
                      View and update your information
                    </p>
                  </div>
                </Link>
                <Link href="/ordinances" className="block">
                  <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors cursor-pointer">
                    <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-1">
                      Ordinances
                    </h3>
                    <p className="text-sm text-blue-600 dark:text-blue-400">
                      Track completed and pending ordinances
                    </p>
                  </div>
                </Link>
                <Link href="/treaties" className="block">
                  <div className="bg-purple-50 dark:bg-purple-900/20 rounded-lg p-4 hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors cursor-pointer">
                    <h3 className="font-semibold text-purple-800 dark:text-purple-200 mb-1">
                      Treaties
                    </h3>
                    <p className="text-sm text-purple-600 dark:text-purple-400">
                      Manage signed treaties and commitments
                    </p>
                  </div>
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}