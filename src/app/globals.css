/* Google Fonts Import */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;900&family=Space+Grotesk:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* NWA Brand Colors */
    --color-primary: #10b981;
    --color-primary-dark: #059669;
    --color-secondary: #1e293b;
    --color-background-dark: #0f172a;
    --color-text-light: #ffffff;
    --color-text-dark: #0f172a;
    --color-gray-50: #f8fafc;
    --color-gray-100: #f1f5f9;
    --color-gray-500: #64748b;

    /* shadcn/ui CSS Variables */
    --background: 248 250 252;
    /* #f8fafc */
    --foreground: 15 23 42;
    /* #0f172a */
    --card: 255 255 255;
    --card-foreground: 15 23 42;
    --popover: 255 255 255;
    --popover-foreground: 15 23 42;
    --primary: 5 150 105;
    /* #059669 - darker green to match homepage */
    --primary-foreground: 255 255 255;
    --secondary: 241 245 249;
    /* #f1f5f9 */
    --secondary-foreground: 15 23 42;
    --muted: 241 245 249;
    --muted-foreground: 100 116 139;
    --accent: 241 245 249;
    --accent-foreground: 15 23 42;
    --destructive: 239 68 68;
    --destructive-foreground: 255 255 255;
    --border: 0 0 0;
    --input: 0 0 0;
    --ring: 5 150 105;
    /* #059669 - matching the primary color */
    --radius: 0.75rem;

    /* Spacing */
    --spacing-xs: 0.5rem;
    --spacing-sm: 1rem;
    --spacing-md: 1.5rem;
    --spacing-lg: 2rem;
    --spacing-xl: 3rem;

    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 8px 25px rgba(0, 0, 0, 0.15);
    --shadow-xl: 0 25px 50px rgba(0, 0, 0, 0.25);
  }

  .dark {
    --background: 15 23 42;
    --foreground: 248 250 252;
    --card: 30 41 59;
    --card-foreground: 248 250 252;
    --popover: 30 41 59;
    --popover-foreground: 248 250 252;
    --primary: 16 185 129;
    /* #10b981 - brighter green for dark mode */
    --primary-foreground: 15 23 42;
    --secondary: 51 65 85;
    --secondary-foreground: 248 250 252;
    --muted: 51 65 85;
    --muted-foreground: 148 163 184;
    --accent: 51 65 85;
    --accent-foreground: 248 250 252;
    --destructive: 220 38 38;
    --destructive-foreground: 248 250 252;
    --border: 255 255 255;
    --input: 255 255 255;
    --ring: 16 185 129;
    /* #10b981 - matching the primary color */
  }
}

@layer base {
  * {
    @apply border-border;
    box-sizing: border-box;
  }

  body {
    @apply bg-background text-foreground;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
    line-height: 1.6;
    font-synthesis: none;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    font-family: 'Space Grotesk', sans-serif;
  }

  /* Fix for browser autofill yellow backgrounds */
  input:-webkit-autofill,
  input:-webkit-autofill:hover,
  input:-webkit-autofill:focus,
  input:-webkit-autofill:active,
  textarea:-webkit-autofill,
  textarea:-webkit-autofill:hover,
  textarea:-webkit-autofill:focus,
  textarea:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 30px white inset !important;
    -webkit-text-fill-color: #0f172a !important;
  }

  /* Fix for Firefox yellow autofill */
  input:autofill,
  textarea:autofill {
    background-color: white !important;
    color: #0f172a !important;
  }

  /* Fix border gaps - comprehensive approach */
  .border-black {
    border: 1px solid #000000 !important;
    position: relative;
  }

  /* Ensure all border-black elements have proper borders */
  .border-black,
  input.border-black,
  textarea.border-black,
  .card .border-black {
    border: 1px solid #000000 !important;
    outline: none !important;
  }

  /* Fix form text colors */
  input,
  textarea,
  select {
    color: #0f172a !important;
  }

  /* Ensure form placeholders are visible */
  input::placeholder,
  textarea::placeholder {
    color: #64748b !important;
  }

  /* Fix any white text in forms */
  .card input,
  .card textarea,
  .card select {
    color: #0f172a !important;
  }

  /* Ensure all text in cards is dark */
  .card {
    color: #0f172a !important;
  }

  /* Fix any inherited white text */
  .card * {
    color: inherit;
  }

  /* Override any white text specifically in cards and forms */
  .card .text-white,
  input.text-white,
  textarea.text-white {
    color: #0f172a !important;
  }

  /* Fix labels and text in server configuration */
  .card label,
  .card .text-muted-foreground,
  .card p,
  .card span {
    color: #0f172a !important;
  }

  /* Fix specific server configuration text */
  .card .text-sm,
  .card .font-mono {
    color: #0f172a !important;
  }

  /* Fix API key input field specifically */
  .card input[type="password"],
  .card input[type="text"] {
    background-color: white !important;
    color: #0f172a !important;
    -webkit-text-fill-color: #0f172a !important;
  }


  /* Fix for alert components in API key display */
  .alert-white {
    background-color: white !important;
    color: #0f172a !important;
    border-color: #e2e8f0 !important;
  }

  .alert-white .text-black {
    color: #0f172a !important;
  }

  /* Fix muted foreground text specifically */
  .text-muted-foreground {
    color: #0f172a !important;
  }

  /* Ensure all text in cards is readable */
  .card * {
    color: #0f172a !important;
  }

  /* Ensure no border gaps in cards */
  .rounded-xl {
    border-radius: 0.75rem;
  }

  /* Fix border rendering issues */
  * {
    box-sizing: border-box;
  }

  /* Ensure borders render properly */
  .border {
    border-width: 1px;
    border-style: solid;
  }

  /* Ensure card borders are visible */
  .card {
    border: 1px solid hsl(var(--border)) !important;
  }

  /* Custom width for settings menu */
  .w-100px {
    width: 200px;
  }
}

@layer components {

  /* Button Utilities */
  .btn-gradient-primary {
    @apply bg-gradient-to-br from-emerald-500 to-emerald-600 text-white font-semibold rounded-xl px-6 py-3 transition-all duration-200 hover:from-emerald-600 hover:to-emerald-700 hover:-translate-y-0.5 hover:shadow-lg;
  }

  /* Card Utilities */
  .card-glass {
    @apply bg-white/95 backdrop-blur-sm rounded-2xl border border-white/20 shadow-xl;
  }

  .card-elevated {
    @apply bg-white rounded-2xl shadow-md border border-gray-100;
  }

  /* Text Utilities */
  .text-gradient-primary {
    @apply bg-gradient-to-r from-emerald-500 to-emerald-600 bg-clip-text text-transparent;
  }
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Scrollbar Styling */
  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Focus Styles */
  .focus-ring {
    @apply focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-emerald-500 focus-visible:ring-offset-2;
  }
}

/* Selection Styles */
::selection {
  background-color: rgba(16, 185, 129, 0.2);
  color: #0f172a;
}

/* Responsive Images */
img {
  max-width: 100%;
  height: auto;
}

/* Animation Keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fadeIn 0.6s ease-out;
}

.animate-slide-up {
  animation: slideUp 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}