import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { getTokenFromRequest } from '@/lib/services/oauth'
import { headers } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    // Extract token from Authorization header
    const token = getTokenFromRequest(request)
    
    if (!token) {
      return Response.json(
        { 
          error: 'invalid_token', 
          error_description: 'Missing or invalid token' 
        }, 
        { status: 401 }
      )
    }
    
    // Find the OAuth token with user and remote server information
    const oauthTokens: any = await prisma.$queryRawUnsafe(`
      SELECT ot.*, u.name as user_name, u.email as user_email
      FROM oauth_tokens ot
      JOIN users u ON ot.user_id = u.id
      WHERE ot.access_token = $1
    `, token)
    
    const oauthToken = Array.isArray(oauthTokens) && oauthTokens.length > 0 ? oauthTokens[0] : null;
    
    if (!oauthToken) {
      return Response.json(
        { 
          error: 'invalid_token', 
          error_description: 'Invalid token' 
        }, 
        { status: 401 }
      )
    }
    
    // Check if the token has expired
    if (new Date(oauthToken.expires_at) < new Date()) {
      return Response.json(
        { 
          error: 'invalid_token', 
          error_description: 'Token has expired' 
        }, 
        { status: 401 }
      )
    }
    
    // Get user permissions and roles
    const userProjectScopes: any = await prisma.$queryRawUnsafe(`
      SELECT ups.*, s.name as scope_name
      FROM user_project_scopes ups
      JOIN scopes s ON ups.scope_id = s.id
      WHERE ups.user_id = $1
    `, oauthToken.user_id)
    
    const permissions = userProjectScopes.map((ups: any) => ups.scope_name)
    
    // In a real implementation, we would also fetch user roles
    // For now, we'll just return an empty array
    const roles: string[] = []
    
    // Return user information
    return Response.json({
      sub: oauthToken.user_id,
      name: oauthToken.user_name || undefined,
      email: oauthToken.user_email || undefined,
      roles,
      permissions
    })
  } catch (error) {
    console.error('OAuth userinfo error:', error)
    return Response.json(
      { 
        error: 'server_error', 
        error_description: 'Internal server error' 
      }, 
      { status: 500 }
    )
  }
}