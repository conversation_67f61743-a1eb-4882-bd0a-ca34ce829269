import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { generateAccessToken, generateRefreshToken } from '@/lib/services/oauth'
import { headers } from 'next/headers'

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData()
    const grantType = formData.get('grant_type') as string
    const clientId = formData.get('client_id') as string
    const clientSecret = formData.get('client_secret') as string
    const code = formData.get('code') as string
    const redirectUri = formData.get('redirect_uri') as string
    const refreshToken = formData.get('refresh_token') as string
    
    // Validate grant_type
    if (!grantType) {
      return Response.json(
        { 
          error: 'invalid_request', 
          error_description: 'Missing grant_type parameter' 
        }, 
        { status: 400 }
      )
    }
    
    // Handle authorization_code grant type
    if (grantType === 'authorization_code') {
      if (!code || !redirectUri || !clientId || !clientSecret) {
        return Response.json(
          { 
            error: 'invalid_request', 
            error_description: 'Missing required parameters for authorization_code grant' 
          }, 
          { status: 400 }
        )
      }
      
      // Find the remote server by client_id
      const remoteServers: any = await prisma.$queryRawUnsafe(`SELECT * FROM remote_servers WHERE client_id = $1`, clientId)
      const remoteServer = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : null;
      
      if (!remoteServer) {
        return Response.json(
          { 
            error: 'invalid_client', 
            error_description: 'Invalid client credentials' 
          }, 
          { status: 401 }
        )
      }
      
      // Validate client secret
      if (remoteServer.client_secret !== clientSecret) {
        return Response.json(
          { 
            error: 'invalid_client', 
            error_description: 'Invalid client credentials' 
          }, 
          { status: 401 }
        )
      }
      
      // Find the authorization code
      console.log('Looking for authorization code:', code);
      const authCodes: any = await prisma.$queryRawUnsafe(`SELECT * FROM authorization_codes WHERE code = $1`, code)
      const authCode = Array.isArray(authCodes) && authCodes.length > 0 ? authCodes[0] : null;
      console.log('Found auth code:', authCode);
      
      if (!authCode) {
        console.log('Authorization code not found in database');
        return Response.json(
          { 
            error: 'invalid_grant', 
            error_description: 'Invalid authorization code' 
          }, 
          { status: 400 }
        )
      }
      
      // Check if the authorization code has expired
      if (new Date(authCode.expires_at) < new Date()) {
        return Response.json(
          { 
            error: 'invalid_grant', 
            error_description: 'Authorization code has expired' 
          }, 
          { status: 400 }
        )
      }
      
      // Check if the authorization code has already been used
      if (authCode.used_at) {
        return Response.json(
          { 
            error: 'invalid_grant', 
            error_description: 'Authorization code has already been used' 
          }, 
          { status: 400 }
        )
      }
      
      // Validate redirect_uri
      if (authCode.redirect_uri !== redirectUri) {
        return Response.json(
          { 
            error: 'invalid_grant', 
            error_description: 'Invalid redirect URI' 
          }, 
          { status: 400 }
        )
      }
      
      // Mark the authorization code as used
      console.log('Marking authorization code as used:', authCode.id);
      await prisma.$queryRawUnsafe(`UPDATE authorization_codes SET used_at = $1 WHERE id = $2`, new Date(), authCode.id)
      
      // Generate access and refresh tokens
      const accessToken = await generateAccessToken(
        remoteServer.id,
        authCode.user_id,
        authCode.scope || ''
      )
      
      const refreshTokenValue = await generateRefreshToken(
        remoteServer.id,
        authCode.user_id,
        authCode.scope || ''
      )
      
      // Create OAuth tokens in database
      const oauthTokens: any = await prisma.$queryRawUnsafe(`
        INSERT INTO oauth_tokens (
          id, remote_server_id, user_id, access_token, refresh_token, 
          token_type, scope, expires_at, created_at, updated_at
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10
        ) RETURNING *
      `,
        // Generate a unique ID (you might want to use a proper ID generation method)
        `token_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        remoteServer.id,
        authCode.user_id,
        accessToken.token,
        refreshTokenValue.token,
        'Bearer',
        authCode.scope || '',
        accessToken.expiresAt,
        new Date(),
        new Date()
      )
      
      const oauthToken = Array.isArray(oauthTokens) && oauthTokens.length > 0 ? oauthTokens[0] : oauthTokens;
      
      // Return token response
      return Response.json({
        access_token: oauthToken.access_token,
        token_type: oauthToken.token_type,
        expires_in: Math.floor((new Date(oauthToken.expires_at).getTime() - Date.now()) / 1000),
        refresh_token: oauthToken.refresh_token,
        scope: oauthToken.scope
      })
    }
    // Handle refresh_token grant type
    else if (grantType === 'refresh_token') {
      if (!refreshToken || !clientId || !clientSecret) {
        return Response.json(
          { 
            error: 'invalid_request', 
            error_description: 'Missing required parameters for refresh_token grant' 
          }, 
          { status: 400 }
        )
      }
      
      // Find the remote server by client_id
      const remoteServers: any = await prisma.$queryRawUnsafe(`SELECT * FROM remote_servers WHERE client_id = $1`, clientId)
      const remoteServer = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : null;
      
      if (!remoteServer) {
        return Response.json(
          { 
            error: 'invalid_client', 
            error_description: 'Invalid client credentials' 
          }, 
          { status: 401 }
        )
      }
      
      // Validate client secret
      if (remoteServer.client_secret !== clientSecret) {
        return Response.json(
          { 
            error: 'invalid_client', 
            error_description: 'Invalid client credentials' 
          }, 
          { status: 401 }
        )
      }
      
      // Find the refresh token
      const oauthTokens: any = await prisma.$queryRawUnsafe(`
        SELECT * FROM oauth_tokens 
        WHERE refresh_token = $1 AND remote_server_id = $2
      `, refreshToken, remoteServer.id)
      
      const oauthToken = Array.isArray(oauthTokens) && oauthTokens.length > 0 ? oauthTokens[0] : null;
      
      if (!oauthToken) {
        return Response.json(
          { 
            error: 'invalid_grant', 
            error_description: 'Invalid refresh token' 
          }, 
          { status: 400 }
        )
      }
      
      // Check if the refresh token has expired
      if (new Date(oauthToken.expires_at) < new Date()) {
        return Response.json(
          { 
            error: 'invalid_grant', 
            error_description: 'Refresh token has expired' 
          }, 
          { status: 400 }
        )
      }
      
      // Generate new access and refresh tokens
      const newAccessToken = await generateAccessToken(
        remoteServer.id,
        oauthToken.user_id,
        oauthToken.scope || ''
      )
      
      const newRefreshToken = await generateRefreshToken(
        remoteServer.id,
        oauthToken.user_id,
        oauthToken.scope || ''
      )
      
      // Update OAuth tokens in database
      const updatedTokens: any = await prisma.$queryRawUnsafe(`
        UPDATE oauth_tokens 
        SET access_token = $1, refresh_token = $2, expires_at = $3, updated_at = $4
        WHERE id = $5
        RETURNING *
      `, newAccessToken.token, newRefreshToken.token, newAccessToken.expiresAt, new Date(), oauthToken.id)
      
      const updatedToken = Array.isArray(updatedTokens) && updatedTokens.length > 0 ? updatedTokens[0] : updatedTokens;
      
      // Return token response
      return Response.json({
        access_token: updatedToken.access_token,
        token_type: updatedToken.token_type,
        expires_in: Math.floor((new Date(updatedToken.expires_at).getTime() - Date.now()) / 1000),
        refresh_token: updatedToken.refresh_token,
        scope: updatedToken.scope
      })
    }
    // Unsupported grant type
    else {
      return Response.json(
        { 
          error: 'unsupported_grant_type', 
          error_description: 'Unsupported grant type' 
        }, 
        { status: 400 }
      )
    }
  } catch (error: any) {
    console.error('OAuth token error:', error)
    console.error('Error stack:', error.stack)
    console.error('Error message:', error.message)
    return Response.json(
      { 
        error: 'server_error', 
        error_description: 'Internal server error: ' + (error.message || 'Unknown error') 
      }, 
      { status: 500 }
    )
  }
}