import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { generateAuthorizationCode } from '@/lib/services/oauth'
import { redirect } from 'next/navigation'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const clientId = searchParams.get('client_id')
    const redirectUri = searchParams.get('redirect_uri')
    const responseType = searchParams.get('response_type')
    const scope = searchParams.get('scope')
    const state = searchParams.get('state')
    
    // Validate required parameters
    if (!clientId || !redirectUri || !responseType) {
      const errorParams = new URLSearchParams({
        error: 'invalid_request',
        error_description: 'Missing required parameters'
      })
      if (state) errorParams.append('state', state)
      return redirect(`${redirectUri}?${errorParams.toString()}`)
    }
    
    // Validate response_type
    if (responseType !== 'code') {
      const errorParams = new URLSearchParams({
        error: 'unsupported_response_type',
        error_description: 'Only authorization code flow is supported'
      })
      if (state) errorParams.append('state', state)
      return redirect(`${redirectUri}?${errorParams.toString()}`)
    }
    
    // Find the remote server by client_id
    const remoteServers: any = await prisma.$queryRawUnsafe(`SELECT * FROM remote_servers WHERE client_id = $1`, clientId)
    const remoteServer = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : null;
    
    if (!remoteServer) {
      const errorParams = new URLSearchParams({
        error: 'unauthorized_client',
        error_description: 'Invalid client ID'
      })
      if (state) errorParams.append('state', state)
      return redirect(`${redirectUri}?${errorParams.toString()}`)
    }
    
    // Validate redirect_uri
    let redirectUris: string[] = [];
    if (Array.isArray(remoteServer.redirect_uris)) {
      redirectUris = remoteServer.redirect_uris;
    } else if (typeof remoteServer.redirect_uris === 'string') {
      try {
        redirectUris = JSON.parse(remoteServer.redirect_uris);
      } catch (e) {
        redirectUris = [remoteServer.redirect_uris];
      }
    }
    
    if (!redirectUris.includes(redirectUri)) {
      const errorParams = new URLSearchParams({
        error: 'invalid_request',
        error_description: 'Invalid redirect URI'
      });
      if (state) errorParams.append('state', state);
      return redirect(`${redirectUri}?${errorParams.toString()}`);
    }
    
    // Validate scope (if provided)
    if (scope) {
      const requestedScopes = scope.split(' ')
      const validScopes = remoteServer.default_scopes || ['read:profile']
      
      console.log('Requested scopes:', requestedScopes);
      console.log('Valid scopes:', validScopes);
      
      // Check if all requested scopes are valid
      const invalidScopes = requestedScopes.filter(s => !validScopes.includes(s))
      if (invalidScopes.length > 0) {
        console.log('Invalid scopes found:', invalidScopes);
        const errorParams = new URLSearchParams({
          error: 'invalid_scope',
          error_description: `Invalid scopes: ${invalidScopes.join(', ')}`
        })
        if (state) errorParams.append('state', state)
        return redirect(`${redirectUri}?${errorParams.toString()}`)
      }
    }
    
    // First, check if user is authenticated
    const session = await getServerSession(authOptions)
    
    // Log session for debugging
    console.log('Session object:', JSON.stringify(session, null, 2));
    
    // If user is not authenticated, redirect to login page
    if (!session || !session.user) {
      console.log('User not authenticated, redirecting to login');
      // Get the current URL to use as callback after login
      const currentUrl = request.url
      const loginUrl = new URL('/login', process.env.NEXTAUTH_URL)
      loginUrl.searchParams.set('callbackUrl', currentUrl)
      return redirect(loginUrl.toString())
    }
    
    // Now that user is authenticated, generate authorization code using the actual user ID
    // The id property is added in the auth callbacks, but TypeScript doesn't know about it
    const userId = (session.user as any).id
    
    // Log user ID for debugging
    console.log('Authenticated user ID:', userId);
    
    console.log('Generating authorization code for:', {
      remoteServerId: remoteServer.id,
      userId: userId,
      redirectUri,
      scope: scope || (remoteServer.default_scopes || ['read:profile']).join(' ')
    });
    
    console.log('About to generate authorization code with:', {
      remoteServerId: remoteServer.id,
      userId: userId,
      redirectUri,
      scope: scope || (remoteServer.default_scopes || ['read:profile']).join(' ')
    });
    
    // Generate authorization code
    const authorizationCode = await generateAuthorizationCode(
      remoteServer.id,
      userId, // Use actual authenticated user ID instead of hardcoded test user
      redirectUri,
      scope || (remoteServer.default_scopes || ['read:profile']).join(' ')
    )
    
    console.log('Authorization code generated successfully:', authorizationCode);
    
    console.log('Generated authorization code:', authorizationCode);
    
    // Redirect back to client with authorization code
    const successParams = new URLSearchParams({
      code: authorizationCode.code
    })
    
    if (state) successParams.append('state', state)
    
    return redirect(`${redirectUri}?${successParams.toString()}`)
  } catch (error: any) {
    // Check if this is a NEXT_REDIRECT error (expected behavior)
    if (error?.digest?.includes('NEXT_REDIRECT')) {
      // Re-throw NEXT_REDIRECT errors as they are expected
      throw error;
    }
    
    console.error('OAuth authorize error:', error)
    console.error('Error type:', typeof error)
    console.error('Error message:', error.message || 'No message')
    console.error('Error stack:', error.stack || 'No stack trace')
    
    // Parse redirect_uri from request
    const { searchParams } = new URL(request.url)
    const redirectUri = searchParams.get('redirect_uri') || '/'
    const state = searchParams.get('state')
    
    const errorParams = new URLSearchParams({
      error: 'server_error',
      error_description: 'Internal server error'
    })
    
    if (state) errorParams.append('state', state)
    
    return redirect(`${redirectUri}?${errorParams.toString()}`)
  }
}