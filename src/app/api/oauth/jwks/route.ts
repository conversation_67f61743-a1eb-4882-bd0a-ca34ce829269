
import { NextResponse } from 'next/server';

export async function GET() {
  const jwks = {
    keys: [
      {
        kty: 'RSA',
        kid: 'lj0ZjkiW5bmU8Zw-pUoQ4MJqFDqYhkRCT3ToH_AFiaQ',
        n: 'ofue9e6feS2JiTMoPQtrOV22QmUIqQ9rGM1Wu4DN1uWqXRUTnC7ey0Lqgkps9bTR0UQFfbmHfVe6Yqt2NOOQ5Kie6sZ8XnoIEDTB1pC8QRhFZmxj73busjJuuiDL4sJzFzlHhNNIY0YOpnAZdv2-ezxrRvjYvadodCbip3JBsPo-RafZCOdASjxx0UIIREaNz6uORXKIX_TZaDBOofdkWlnB5Nugn52MjSVt7sIdeMhaLqSAH3LdK-UNcQzZFP96hHvLUGk3YH-UW7jRLvKIQjIbV1hFTfCq71sLByRjPOr35jVI2oDJt0P3gYsykj0JRb1QO2cQCbiS4-F_F9GIcw',
        e: 'AQAB',
      },
    ],
  };

  return NextResponse.json(jwks);
}
