import { NextRequest } from 'next/server'
import { prisma } from '@/lib/prisma'
import { generateRandomString } from '@/lib/utils'

export async function POST(request: NextRequest) {
  try {
    const body = await request.json()
    const { url } = body
    
    // Validate required parameters
    if (!url) {
      return Response.json(
        { error: 'Missing required parameter: url' },
        { status: 400 }
      )
    }
    
    // Validate URL format
    try {
      new URL(url)
    } catch (error) {
      return Response.json(
        { error: 'Invalid URL format' },
        { status: 400 }
      )
    }
    
    // Check if remote server is already registered
    const existingServers: any = await prisma.$queryRawUnsafe(
      `SELECT * FROM remote_servers WHERE url = $1`,
      url
    )
    const existingServer = existingServers.length > 0 ? existingServers[0] : null
    
    if (existingServer) {
      return Response.json(
        { error: 'Remote server already registered' },
        { status: 409 }
      )
    }
    
    // Fetch information from the remote server
    const permissionUrl = `${url}/api/permissions`
    console.log('Registering remote server, fetching permissions from:', permissionUrl)
    
    // For registration, we might not have an API key yet, but let's try with a generic one
    // In a real implementation, this would be handled differently
    const infoResponse = await fetch(permissionUrl, {
      headers: {
        'Content-Type': 'application/json'
      }
    })
    
    console.log('Registration response status:', infoResponse.status)
    
    if (!infoResponse.ok) {
      return Response.json(
        { error: 'Error connecting to remote server' },
        { status: 502 }
      )
    }
    
    const info = await infoResponse.json()
    
    // Generate client credentials
    const clientId = generateRandomString(32)
    const clientSecret = generateRandomString(64)
    
    // Create remote server in database using raw query
    const remoteServers: any = await prisma.$queryRawUnsafe(`
      INSERT INTO remote_servers (
        id, name, url, "apiKey", description, client_id, client_secret, 
        redirect_uris, default_scopes, is_active, created_at, updated_at
      ) VALUES (
        $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12
      ) RETURNING *
    `, [
      `server_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`, // id
      info.name || 'Remote Server', // name
      url, // url
      generateRandomString(64), // apiKey
      info.description, // description
      clientId, // client_id
      clientSecret, // client_secret
      [], // redirect_uris
      info.permissions?.map((p: any) => p.name) || ['read:profile'], // default_scopes
      true, // is_active
      new Date(), // created_at
      new Date() // updated_at
    ])
    
    const remoteServer = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : remoteServers;
    
    return Response.json({
      id: remoteServer.id,
      name: remoteServer.name,
      url: remoteServer.url,
      description: remoteServer.description,
      isActive: remoteServer.isActive,
      createdAt: remoteServer.createdAt,
      updatedAt: remoteServer.updatedAt
    })
  } catch (error) {
    console.error('Remote server registration error:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}