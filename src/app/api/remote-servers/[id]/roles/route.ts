import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: remoteServerId } = await params
    
    // Find the remote server
    const remoteServers: any = await prisma.$queryRawUnsafe(`SELECT * FROM remote_servers WHERE id = $1`, remoteServerId)
    const remoteServer = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : null;
    
    if (!remoteServer) {
      return Response.json(
        { error: 'Remote server not found' },
        { status: 404 }
      )
    }
    
    // Make an HTTP request to the remote server's /api/permissions endpoint to fetch roles
    const permissionUrl = `${remoteServer.url}/api/permissions`
    console.log('Fetching roles from:', permissionUrl)
    console.log('Using API Key:', remoteServer.apiKey)
    
    const infoResponse = await fetch(permissionUrl, {
      headers: {
        'Authorization': `Bearer ${remoteServer.apiKey}`,
        'Content-Type': 'application/json'
      }
    })
    
    console.log('Roles response status:', infoResponse.status)
    
    if (!infoResponse.ok) {
      return Response.json(
        { error: 'Error connecting to remote server' },
        { status: 502 }
      )
    }
    
    const info = await infoResponse.json()
    
    // Transform the response to match the expected format
    const roles = info.roles?.map((role: any, index: number) => ({
      id: `role-${index}`,
      name: role.name,
      description: role.description,
      permissions: (role.permissions || []).map((permName: string, permIndex: number) => ({
        id: `perm-${index}-${permIndex}`,
        name: permName
      })),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    })) || []
    
    return Response.json(roles)
  } catch (error) {
    console.error('Get roles error:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}