import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: remoteServerId } = await params
    const body = await request.json()
    const { userId, permissionId } = body
    
    // Validate required parameters
    if (!userId || !permissionId) {
      return Response.json(
        { error: 'Missing required parameters: userId and permissionId' },
        { status: 400 }
      )
    }
    
    // Find the remote server
    const remoteServers: any = await prisma.$queryRawUnsafe(`SELECT * FROM remote_servers WHERE id = $1`, remoteServerId)
    const remoteServer = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : null;
    
    if (!remoteServer) {
      return Response.json(
        { error: 'Remote server not found' },
        { status: 404 }
      )
    }
    
    // Check if user exists
    const users: any = await prisma.$queryRawUnsafe(`SELECT * FROM users WHERE id = $1`, userId)
    const user = Array.isArray(users) && users.length > 0 ? users[0] : null;
    
    if (!user) {
      return Response.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }
    
    // Since there are no remote server permission tables in the database,
    // we'll simulate the permission assignment using mock data
    // In a real implementation, these would be stored in database tables
    
    // Check if permission exists (using mock data)
    const mockPermissions = [
      { id: 'perm1', name: 'read:documents', description: 'Read documents' },
      { id: 'perm2', name: 'write:documents', description: 'Create and edit documents' },
      { id: 'perm3', name: 'delete:documents', description: 'Delete documents' },
      { id: 'perm4', name: 'read:users', description: 'Read user information' },
      { id: 'perm5', name: 'write:users', description: 'Create and edit users' }
    ]
    
    const permission = mockPermissions.find(p => p.id === permissionId)
    
    if (!permission) {
      return Response.json(
        { error: 'Permission not found' },
        { status: 404 }
      )
    }
    
    // Since there's no user_remote_server_permissions table,
    // we'll return a mock response for a successful assignment
    const userPermission = {
      id: `user-perm-${Date.now()}`,
      userId,
      remoteServerId,
      permissionId,
      assignedAt: new Date().toISOString(),
      assignedBy: 'system'
    }
    
    return Response.json(userPermission)
  } catch (error) {
    console.error('Assign user permission error:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}