import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function DELETE(
  request: NextRequest, 
  { params }: { params: Promise<{ id: string; userPermissionId: string }> }
) {
  try {
    const { id: remoteServerId, userPermissionId } = await params
    
    // Find the remote server
    const remoteServers: any = await prisma.$queryRawUnsafe(`SELECT * FROM remote_servers WHERE id = $1`, remoteServerId)
    const remoteServer = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : null;
    
    if (!remoteServer) {
      return Response.json(
        { error: 'Remote server not found' },
        { status: 404 }
      )
    }
    
    // Since there's no user_remote_server_permissions table,
    // we'll simulate the deletion using mock data
    // In a real implementation, this would delete from the database
    
    // For now, we'll just return a success response
    return Response.json({ success: true })
  } catch (error) {
    console.error('Remove user permission error:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}