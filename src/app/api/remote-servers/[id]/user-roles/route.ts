import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function POST(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: remoteServerId } = await params
    const body = await request.json()
    const { userId, roleId } = body
    
    // Validate required parameters
    if (!userId || !roleId) {
      return Response.json(
        { error: 'Missing required parameters: userId and roleId' },
        { status: 400 }
      )
    }
    
    // Find the remote server
    const remoteServers: any = await prisma.$queryRawUnsafe(`SELECT * FROM remote_servers WHERE id = $1`, remoteServerId)
    const remoteServer = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : null;
    
    if (!remoteServer) {
      return Response.json(
        { error: 'Remote server not found' },
        { status: 404 }
      )
    }
    
    // Check if user exists
    const users: any = await prisma.$queryRawUnsafe(`SELECT * FROM users WHERE id = $1`, userId)
    const user = Array.isArray(users) && users.length > 0 ? users[0] : null;
    
    if (!user) {
      return Response.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }
    
    // Since there are no remote server role tables in the database,
    // we'll simulate the role assignment using mock data
    // In a real implementation, these would be stored in database tables
    
    // Check if role exists (using mock data)
    const mockRoles = [
      { id: 'role1', name: 'Administrator', description: 'Full system access' },
      { id: 'role2', name: 'Editor', description: 'Can create and edit content' },
      { id: 'role3', name: 'Viewer', description: 'Read-only access' }
    ]
    
    const role = mockRoles.find(r => r.id === roleId)
    
    if (!role) {
      return Response.json(
        { error: 'Role not found' },
        { status: 404 }
      )
    }
    
    // Since there's no user_remote_server_roles table,
    // we'll return a mock response for a successful assignment
    const userRole = {
      id: `user-role-${Date.now()}`,
      userId,
      remoteServerId,
      roleId,
      assignedAt: new Date().toISOString(),
      assignedBy: 'system'
    }
    
    return Response.json(userRole)
  } catch (error) {
    console.error('Assign user role error:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}