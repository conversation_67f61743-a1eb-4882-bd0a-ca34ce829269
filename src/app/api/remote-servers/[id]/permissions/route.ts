import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const { id: remoteServerId } = await params
    
    console.log('=== FETCHING PERMISSIONS FOR REMOTE SERVER ===')
    console.log('Remote Server ID:', remoteServerId)
    
    // Find the remote server
    const remoteServers: any = await prisma.$queryRawUnsafe(`SELECT * FROM remote_servers WHERE id = $1`, remoteServerId)
    const remoteServer = Array.isArray(remoteServers) && remoteServers.length > 0 ? remoteServers[0] : null;
    
    console.log('Remote Server Data:', remoteServer)
    
    if (!remoteServer) {
      return Response.json(
        { error: 'Remote server not found' },
        { status: 404 }
      )
    }
    
    // Make an HTTP request to the remote server's /api/permissions endpoint to fetch permissions
    const permissionUrl = `${remoteServer.url}/api/permissions`
    console.log('Fetching permissions from:', permissionUrl)
    console.log('Using API Key:', remoteServer.apiKey)
    
    const infoResponse = await fetch(permissionUrl, {
      headers: {
        'Authorization': `Bearer ${remoteServer.apiKey}`,
        'Content-Type': 'application/json'
      }
    })
    
    console.log('Response status:', infoResponse.status)
    console.log('Response headers:', Object.fromEntries(infoResponse.headers))
    
    if (!infoResponse.ok) {
      return Response.json(
        { error: 'Error connecting to remote server' },
        { status: 502 }
      )
    }
    
    const info = await infoResponse.json()
    console.log('Received permissions data:', info)
    
    // Transform the response to match the expected format
    const permissions = info.permissions?.map((perm: any) => ({
      name: perm.name,
      description: perm.description
    })) || []
    
    return Response.json(permissions)
  } catch (error) {
    console.error('Get permissions error:', error)
    console.error('Error type:', typeof error)
    console.error('Error stack:', error instanceof Error ? error.stack : 'No stack trace')
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}