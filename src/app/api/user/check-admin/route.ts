import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function GET(request: NextRequest) {
    try {
        // Get the current session
        const session = await getServerSession(authOptions);

        if (!session?.user?.email) {
            return NextResponse.json(
                { isAdmin: false, error: 'Authentication required' },
                { status: 401 }
            );
        }

        // Get the user from the database with roles
        const user = await prisma.user.findUnique({
            where: { email: session.user.email },
            select: {
                id: true,
                email: true,
                userRoles: {
                    include: {
                        role: true,
                    },
                },
            },
        });

        if (!user) {
            return NextResponse.json(
                { isAdmin: false, error: 'User not found' },
                { status: 404 }
            );
        }

        // Check if user has admin role
        const hasAdminRole = user.userRoles.some(
            (userRole) => userRole.role.name === 'admin' || userRole.role.name === 'super_admin'
        );

        return NextResponse.json(
            { isAdmin: hasAdminRole },
            { status: 200 }
        );

    } catch (error) {
        console.error('Admin check error:', error);
        return NextResponse.json(
            { isAdmin: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
} 