import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import crypto from 'crypto';
import { EmailService } from '@/lib/services/email-service';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';

// Validation schema for email update request
const emailUpdateSchema = z.object({
  newEmail: z.string().email('Invalid email format'),
});

// Validation schema for email verification
const emailVerificationSchema = z.object({
  token: z.string(),
});

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting for email updates
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    const body = await req.json();
    
    // If this is a verification request (with token)
    if (body.token) {
      const validation = emailVerificationSchema.safeParse(body);
      
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Validation error', details: validation.error.flatten() },
          { status: 400 }
        );
      }
      
      const { token } = validation.data;
      
      // Find verification token
      const verificationToken = await prisma.verificationToken.findUnique({
        where: {
          token: token,
        },
      });
      
      if (!verificationToken || verificationToken.expires < new Date()) {
        return NextResponse.json({ error: 'Invalid or expired token' }, { status: 400 });
      }
      
      // Update user's personal email
      await prisma.user.update({
        where: { id: (session.user as any).id },
        data: {
          email: verificationToken.identifier,
          emailVerified: new Date(), // Mark as verified when email is updated
        },
      });
      
      // Delete used token
      await prisma.verificationToken.delete({
        where: { token: token },
      });
      
      return NextResponse.json({ message: 'Email updated successfully' });
    }
    
    // If this is an email update request (with new email)
    const validation = emailUpdateSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    const { newEmail } = validation.data;
    
    // Check if email is already in use
    const existingUser = await prisma.user.findUnique({
      where: { email: newEmail },
    });
    
    if (existingUser) {
      return NextResponse.json({ error: 'Email is already in use' }, { status: 400 });
    }
    
    // Generate verification token
    const token = crypto.randomBytes(32).toString('hex');
    const expires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    
    // Save verification token
    await prisma.verificationToken.create({
      data: {
        identifier: newEmail,
        token: token,
        expires: expires,
      },
    });
    
    // Send verification email
    if (process.env.NODE_ENV === 'production') {
      const emailService = new EmailService();
      await emailService.sendVerificationEmail(newEmail, token);
    }
    
    // In development, return the token in the response
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.json({ 
        message: 'Verification email sent', 
        token: token,
        email: newEmail 
      });
    }
    
    return NextResponse.json({ message: 'Verification email sent. Please check your inbox.' });
  } catch (error) {
    console.error('Error updating email:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}