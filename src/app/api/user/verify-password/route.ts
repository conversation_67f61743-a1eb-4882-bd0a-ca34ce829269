import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';

export async function POST(request: NextRequest) {
    try {
        // Get the current session
        const session = await getServerSession(authOptions);

        if (!session?.user?.email) {
            return NextResponse.json(
                { success: false, error: 'Authentication required' },
                { status: 401 }
            );
        }

        // Get the password from the request body
        const { password } = await request.json();

        if (!password) {
            return NextResponse.json(
                { success: false, error: 'Password is required' },
                { status: 400 }
            );
        }

        // Get the user from the database with roles
        const user = await prisma.user.findUnique({
            where: { email: session.user.email },
            select: {
                id: true,
                email: true,
                passwordHash: true,
                userRoles: {
                    include: {
                        role: true,
                    },
                },
            },
        });

        if (!user) {
            return NextResponse.json(
                { success: false, error: 'User not found' },
                { status: 404 }
            );
        }

        // Check if user has admin role
        const hasAdminRole = user.userRoles.some(
            (userRole) => userRole.role.name === 'admin' || userRole.role.name === 'super_admin'
        );

        if (!hasAdminRole) {
            return NextResponse.json(
                { success: false, error: 'Admin privileges required' },
                { status: 403 }
            );
        }

        // Verify the password
        if (!user.passwordHash) {
            return NextResponse.json(
                { success: false, error: 'Password verification not available' },
                { status: 400 }
            );
        }

        const isPasswordValid = await bcrypt.compare(password, user.passwordHash);

        if (!isPasswordValid) {
            return NextResponse.json(
                { success: false, error: 'Invalid password' },
                { status: 401 }
            );
        }

        // Password is valid and user has admin role
        return NextResponse.json(
            { success: true, message: 'Password verified successfully' },
            { status: 200 }
        );

    } catch (error) {
        console.error('Password verification error:', error);
        return NextResponse.json(
            { success: false, error: 'Internal server error' },
            { status: 500 }
        );
    }
} 