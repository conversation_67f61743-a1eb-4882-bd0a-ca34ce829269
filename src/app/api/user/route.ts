import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for profile updates
const profileUpdateSchema = z.object({
  firstName: z.string().min(1, 'First name is required').optional(),
  lastName: z.string().min(1, 'Last name is required').optional(),
  bio: z.string().max(500, 'Bio must be less than 500 characters').optional(),
  phone: z.string().optional(),
  dateOfBirth: z.string().optional(),
  country: z.string().optional(),
  city: z.string().optional(),
});

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        profile: true,
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    return NextResponse.json({
      id: user.id,
      name: user.name,
      email: user.email,
      nwaEmail: user.profile?.nwaEmail,
      firstName: user.profile?.firstName,
      lastName: user.profile?.lastName,
      bio: user.profile?.bio,
      phone: user.profile?.mobile,
      dateOfBirth: user.profile?.dateOfBirth,
      country: user.profile?.country,
      city: user.profile?.city,
      image: user.image,
      twoFactorEnabled: user.twoFactorEnabled,
    });
  } catch (error) {
    console.error('Error fetching user profile:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    
    // Check if this is a password update
    if (body.currentPassword && body.newPassword) {
      // Handle password update in the password route
      return NextResponse.json({ error: 'Use /api/user/password for password updates' }, { status: 400 });
    }
    
    // Regular profile update
    const validation = profileUpdateSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    const { firstName, lastName, bio, phone, dateOfBirth, country, city } = validation.data;
    
    // Update user and profile
    const updatedUser = await prisma.user.update({
      where: { id: (session.user as any).id },
      data: {
        // Update the main user name with the full name
        name: firstName && lastName ? `${firstName} ${lastName}` : (firstName || lastName || undefined),
        profile: {
          upsert: {
            create: {
              firstName,
              lastName,
              bio,
              mobile: phone,
              dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
              country,
              city,
            },
            update: {
              firstName,
              lastName,
              bio,
              mobile: phone,
              dateOfBirth: dateOfBirth ? new Date(dateOfBirth) : undefined,
              country,
              city,
            },
          },
        },
      },
      include: {
        profile: true,
      },
    });
    
    return NextResponse.json({
      id: updatedUser.id,
      name: updatedUser.name,
      email: updatedUser.email,
      nwaEmail: updatedUser.profile?.nwaEmail,
      firstName: updatedUser.profile?.firstName,
      lastName: updatedUser.profile?.lastName,
      bio: updatedUser.profile?.bio,
      phone: updatedUser.profile?.mobile,
      dateOfBirth: updatedUser.profile?.dateOfBirth,
      country: updatedUser.profile?.country,
      city: updatedUser.profile?.city,
      image: updatedUser.image,
      twoFactorEnabled: updatedUser.twoFactorEnabled,
    });
  } catch (error) {
    console.error('Error updating user profile:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}