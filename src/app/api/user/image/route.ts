import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { FileStorageService } from '@/lib/services/file-storage';

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get the form data
    const formData = await req.formData();
    const file = formData.get('image') as File | null;
    
    if (!file) {
      return NextResponse.json({ error: 'No file provided' }, { status: 400 });
    }
    
    // Check file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json({ error: 'File size exceeds 5MB limit' }, { status: 400 });
    }
    
    // Check file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif'];
    if (!allowedTypes.includes(file.type)) {
      return NextResponse.json({ error: 'Invalid file type. Only JPEG, PNG, and GIF are allowed.' }, { status: 400 });
    }
    
    // Convert file to buffer
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    
    // Upload file using FileStorageService
    const fileStorageService = new FileStorageService();
    const imageUrl = await fileStorageService.uploadFile(buffer, file.name, file.type);
    
    // Update user's image in database
    await prisma.user.update({
      where: { id: (session.user as any).id },
      data: { image: imageUrl },
    });
    
    return NextResponse.json({ 
      message: 'Image uploaded successfully',
      url: imageUrl
    });
  } catch (error) {
    console.error('Error uploading image:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get current user to get image URL
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
    });
    
    if (user?.image) {
      // Extract filename from URL (assuming format /api/files/filename)
      const filename = user.image.split('/').pop();
      if (filename) {
        // Delete file from storage
        const fileStorageService = new FileStorageService();
        await fileStorageService.deleteFile(filename);
      }
    }

    // Remove user's image from database
    await prisma.user.update({
      where: { id: (session.user as any).id },
      data: { image: null },
    });
    
    return NextResponse.json({ message: 'Image removed successfully' });
  } catch (error) {
    console.error('Error removing image:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}