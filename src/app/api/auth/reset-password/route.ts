import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import crypto from 'crypto';
import bcrypt from 'bcryptjs';
import { EmailService } from '@/lib/services/email-service';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';

// Validation schema for requesting password reset
const passwordResetRequestSchema = z.object({
  email: z.string().email('Invalid email format'),
});

// Validation schema for resetting password
const passwordResetSchema = z.object({
  token: z.string(),
  newPassword: z.string().min(8, 'Password must be at least 8 characters long'),
});

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    
    // If this is a password reset request (with email)
    if (body.email) {
      // Apply rate limiting for password reset request
      const rateLimitResult = await predefinedRateLimiters.passwordReset(req);
      if (!rateLimitResult.allowed) {
        return NextResponse.json(
          { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
          { 
            status: 429,
            headers: rateLimitResult.headers
          }
        );
      }
      
      const validation = passwordResetRequestSchema.safeParse(body);
      
      if (!validation.success) {
        return NextResponse.json(
          { error: 'Validation error', details: validation.error.flatten() },
          { status: 400 }
        );
      }
      
      const { email } = validation.data;
      
      // Check if user exists
      const user = await prisma.user.findUnique({
        where: { email },
      });
      
      if (!user) {
        // Don't reveal whether the email exists or not
        return NextResponse.json({ message: 'If your email is registered, you will receive a password reset link' });
      }
      
      // Generate reset token
      const token = crypto.randomBytes(32).toString('hex');
      const expires = new Date(Date.now() + 60 * 60 * 1000); // 1 hour
      
      // Save reset token
      await prisma.verificationToken.create({
        data: {
          identifier: email,
          token: token,
          expires: expires,
        },
      });
      
      // Send reset email
      if (process.env.NODE_ENV === 'production') {
        const emailService = new EmailService();
        await emailService.sendPasswordResetEmail(email, token);
      }
      
      // In development, return the token in the response
      if (process.env.NODE_ENV === 'development') {
        return NextResponse.json({ 
          message: 'Password reset email sent', 
          token: token,
          email: email 
        });
      }
      
      return NextResponse.json({ message: 'If your email is registered, you will receive a password reset link' });
    }
    
    // Apply rate limiting for password reset
    const rateLimitResult = await predefinedRateLimiters.strict(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }
    
    // If this is a password reset (with token and new password)
    const validation = passwordResetSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    const { token, newPassword } = validation.data;
    
    // Find reset token
    const resetToken = await prisma.verificationToken.findUnique({
      where: {
        token: token,
      },
    });
    
    if (!resetToken || resetToken.expires < new Date()) {
      return NextResponse.json({ error: 'Invalid or expired token' }, { status: 400 });
    }
    
    // Hash the new password
    const hashedPassword = await bcrypt.hash(newPassword, 12);
    
    // Update user's password
    await prisma.user.update({
      where: { email: resetToken.identifier },
      data: {
        passwordHash: hashedPassword,
      },
    });
    
    // Delete used token
    await prisma.verificationToken.delete({
      where: { token: token },
    });
    
    return NextResponse.json({ message: 'Password reset successfully' });
  } catch (error) {
    console.error('Error resetting password:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}