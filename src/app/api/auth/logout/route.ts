import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { redirect } from 'next/navigation';

export async function GET(req: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session) {
    return redirect('/login');
  }
  
  // NextAuth handles logout automatically through the signOut function
  // This route is just for demonstration
  return redirect('/login');
}