import { NextRequest, NextResponse } from 'next/server';
import { createA<PERSON><PERSON><PERSON><PERSON> } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import { JwtService } from '@/lib/services/jwt';
import { z } from 'zod';

// Zod schema for request validation
const tokenSchema = z.object({
  userId: z.string().optional(),
  audience: z.string().optional(),
});

// Create authentication middleware chain
const authMiddleware = createAuthChain({
  requireApiKey: true,
  requireJwt: false, // Don't require JWT for token generation
  requireCors: true,
  rateLimitConfig: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 50, // 50 requests per minute
  },
});

export async function POST(request: NextRequest) {
  try {
    // Run authentication middleware
    const authResponse = await authMiddleware(request);
    if (authResponse) {
      return authResponse;
    }

    // Parse and validate request body
    const body = await request.json();
    const parsed = tokenSchema.safeParse(body);
    
    if (!parsed.success) {
      return NextResponse.json(
        { 
          error: 'Bad Request', 
          message: 'Invalid request body',
          details: parsed.error.flatten()
        },
        { status: 400 }
      );
    }

    const { userId, audience } = parsed.data;

    // Extract context from headers (set by middleware)
    const projectId = request.headers.get('x-project-id') || '';

    // Get project details
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      select: {
        id: true,
        name: true,
      },
    });

    if (!project) {
      return NextResponse.json(
        { error: 'Not Found', message: 'Project not found' },
        { status: 404 }
      );
    }

    // If userId is provided, verify the user exists and has access to this project
    if (userId) {
      const userProjectScope = await prisma.userProjectScope.findFirst({
        where: {
          userId,
          projectId,
        },
      });

      if (!userProjectScope) {
        return NextResponse.json(
          { error: 'Forbidden', message: 'User does not have access to this project' },
          { status: 403 }
        );
      }
    }

    // Create JWT service and generate token
    const jwtService = new JwtService();
    
    // Determine token expiration (1 hour default)
    const expiresIn = '1h';
    
    // Generate JWT token
    const token = await jwtService.signToken({
      projectId,
      userId,
      scopes: userId ? [] : ['service:access'], // Service tokens get service access scope
    }, expiresIn);

    // Return token
    return NextResponse.json({
      token,
      expiresIn: 3600, // 1 hour in seconds
      tokenType: 'Bearer',
    });
  } catch (error) {
    console.error('Token generation error:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}