import { NextRequest, NextResponse } from 'next/server';
import { create<PERSON><PERSON><PERSON><PERSON><PERSON> } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import { JwtService } from '@/lib/services/jwt';
import { z } from 'zod';

// Zod schema for request validation
const validateSchema = z.object({
  requiredScopes: z.array(z.string()).optional(),
});

// Create authentication middleware chain
const authMiddleware = createAuthChain({
  requireApiKey: true,
  requireJwt: true,
  requireCors: true,
  rateLimitConfig: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100, // 100 requests per minute
  },
});

export async function POST(request: NextRequest) {
  try {
    // Run authentication middleware
    const authResponse = await authMiddleware(request);
    if (authResponse) {
      return authResponse;
    }

    // Parse and validate request body
    const body = await request.json();
    const parsed = validateSchema.safeParse(body);
    
    if (!parsed.success) {
      return NextResponse.json(
        { 
          error: 'Bad Request', 
          message: 'Invalid request body',
          details: parsed.error.flatten()
        },
        { status: 400 }
      );
    }

    const { requiredScopes = [] } = parsed.data;

    // Extract context from headers (set by middleware)
    const projectId = request.headers.get('x-project-id') || '';
    const userId = request.headers.get('x-user-id') || '';
    const scopes = JSON.parse(request.headers.get('x-scopes') || '[]');

    // If requiredScopes is provided, validate them
    if (requiredScopes.length > 0) {
      const missingScopes = requiredScopes.filter(scope => !scopes.includes(scope));
      
      if (missingScopes.length > 0) {
        return NextResponse.json(
          {
            error: 'Forbidden',
            message: `Missing required permissions: ${missingScopes.join(', ')}`,
            details: {
              requiredScopes,
              grantedScopes: scopes,
              missingScopes,
            },
          },
          { status: 403 }
        );
      }
    }

    // Get project details
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      select: {
        id: true,
        name: true,
      },
    });

    if (!project) {
      return NextResponse.json(
        { error: 'Not Found', message: 'Project not found' },
        { status: 404 }
      );
    }

    // Get user details if userId is provided
    let user = null;
    if (userId) {
      user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
        },
      });
    }

    // Return validation result
    return NextResponse.json({
      valid: true,
      project: {
        id: project.id,
        name: project.name,
      },
      user: user ? {
        id: user.id,
        name: user.name,
        email: user.email,
      } : undefined,
      scopes,
    });
  } catch (error) {
    console.error('External validation error:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}