import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';

// Validation schema for email verification
const emailVerificationSchema = z.object({
  token: z.string(),
});

export async function POST(req: NextRequest) {
  try {
    // Apply rate limiting for email verification
    const rateLimitResult = await predefinedRateLimiters.auth(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    const body = await req.json();
    const validation = emailVerificationSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    const { token } = validation.data;
    
    // Find verification token
    const verificationToken = await prisma.verificationToken.findUnique({
      where: {
        token: token,
      },
    });
    
    if (!verificationToken || verificationToken.expires < new Date()) {
      return NextResponse.json({ error: 'Invalid or expired token' }, { status: 400 });
    }
    
    // Update user's email verification status
    await prisma.user.update({
      where: { email: verificationToken.identifier },
      data: {
        emailVerified: new Date(),
      },
    });
    
    // Delete used token
    await prisma.verificationToken.delete({
      where: { token: token },
    });
    
    return NextResponse.json({ message: 'Email verified successfully' });
  } catch (error) {
    console.error('Error verifying email:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}