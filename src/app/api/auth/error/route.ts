import { NextRequest } from 'next/server'
import { redirect } from 'next/navigation'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const error = searchParams.get('error')
    const errorDescription = searchParams.get('error_description')
    
    console.log('=== Auth Error Page ===')
    console.log('Error:', error)
    console.log('Error Description:', errorDescription)
    
    // For now, redirect to a simple error page or login page
    // In a real implementation, you might want to show a proper error page
    return redirect('/login?error=auth_failed&error_description=' + encodeURIComponent(errorDescription || 'Authentication failed'))
  } catch (error: any) {
    console.error('Error page error:', error)
    return redirect('/login?error=unknown&error_description=Unknown error occurred')
  }
}