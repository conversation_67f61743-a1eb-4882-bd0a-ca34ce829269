import { NextRequest } from 'next/server'
import { redirect } from 'next/navigation'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const callbackUrl = searchParams.get('callbackUrl') || '/dashboard'
    
    // Generate state parameter for security
    const state = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15)
    
    // Store state in a session or database for validation in the callback
    // For this example, we'll just pass it through
    
    // Redirect to the OAuth authorization endpoint
    const authUrl = new URL('http://localhost:3001/api/oauth/authorize')
    authUrl.searchParams.set('response_type', 'code')
    authUrl.searchParams.set('client_id', 'nwapromote-client-local')
    authUrl.searchParams.set('redirect_uri', 'http://localhost:3002/api/auth/callback/member-portal-custom')
    authUrl.searchParams.set('scope', 'read:profile')
    authUrl.searchParams.set('state', state)
    
    console.log('=== Initiating OAuth Flow ===')
    console.log('Redirecting to:', authUrl.toString())
    
    return redirect(authUrl.toString())
  } catch (error: any) {
    console.error('Signin error:', error)
    return redirect(`/auth/error?error=signin_failed&error_description=${encodeURIComponent(error.message || 'Failed to initiate signin')}`)
  }
}