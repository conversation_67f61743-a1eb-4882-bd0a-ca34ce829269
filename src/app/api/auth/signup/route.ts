import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import crypto from 'crypto';
import bcrypt from 'bcryptjs';
import { EmailService } from '@/lib/services/email-service';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';

// Validation schema for signup
const signupSchema = z.object({
  email: z.string().email('Invalid email format'),
  password: z.string().min(8, 'Password must be at least 8 characters long'),
  name: z.string().min(1, 'Name is required'),
});

export async function POST(req: NextRequest) {
  try {
    // Apply rate limiting for signup
    const rateLimitResult = await predefinedRateLimiters.signup(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }
    
    const body = await req.json();
    const validation = signupSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    const { email, password, name } = validation.data;
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });
    
    if (existingUser) {
      return NextResponse.json({ error: 'User already exists' }, { status: 400 });
    }
    
    // Hash the password
    const hashedPassword = await bcrypt.hash(password, 12);
    
    // Create the user
    const user = await prisma.user.create({
      data: {
        email,
        name,
        passwordHash: hashedPassword,
        emailVerified: null, // Email is not verified yet
      },
    });
    
    // Create a basic user profile
    const userProfile = await prisma.userProfile.create({
      data: {
        userId: user.id,
        nwaEmail: email, // For now, use the same email as NWA email
      },
    });
    
    // Generate verification token
    const token = crypto.randomBytes(32).toString('hex');
    const expires = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours
    
    // Save verification token
    await prisma.verificationToken.create({
      data: {
        identifier: email,
        token: token,
        expires: expires,
      },
    });
    
    // Send verification email
    if (process.env.NODE_ENV === 'production') {
      const emailService = new EmailService();
      await emailService.sendVerificationEmail(email, token);
    }
    
    // In development, return the token in the response
    if (process.env.NODE_ENV === 'development') {
      return NextResponse.json({ 
        message: 'User created successfully. Please verify your email.',
        verificationToken: token, // Only in development
        user: {
          id: user.id,
          email: user.email,
          name: user.name,
        }
      });
    }
    
    return NextResponse.json({ 
      message: 'User created successfully. Please check your email to verify your account.',
      user: {
        id: user.id,
        email: user.email,
        name: user.name,
      }
    });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}