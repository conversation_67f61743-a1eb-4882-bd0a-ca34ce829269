import { NextRequest, NextResponse } from 'next/server';
import { Client } from 'minio';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ filename: string }> }
) {
  try {
    const { filename } = await params;
    
    // Initialize MinIO client
    const endpoint = process.env.MINIO_ENDPOINT || 'localhost';
    const port = parseInt(process.env.MINIO_PORT || '9000', 10);
    const accessKey = process.env.MINIO_ACCESS_KEY || 'minioadmin';
    const secretKey = process.env.MINIO_SECRET_KEY || 'minioadmin123';
    const bucketName = process.env.MINIO_BUCKET_NAME || 'nwa-uploads';

    const client = new Client({
      endPoint: endpoint,
      port: port,
      useSSL: process.env.MINIO_USE_SSL === 'true',
      accessKey: accessKey,
      secretKey: secretKey,
    });

    // Get the object stream
    const objectStream = await client.getObject(bucketName, filename);
    
    // Get object metadata to determine content type
    const stat = await client.statObject(bucketName, filename);
    const contentType = stat.metaData['content-type'] || 'application/octet-stream';
    
    // Convert stream to buffer
    const chunks: Buffer[] = [];
    for await (const chunk of objectStream) {
      chunks.push(chunk);
    }
    const buffer = Buffer.concat(chunks);
    
    // Return the file with appropriate content type
    return new NextResponse(buffer, {
      headers: {
        'Content-Type': contentType,
        'Cache-Control': 'public, max-age=31536000, immutable', // 1 year cache
      },
    });
  } catch (error: any) {
    console.error('Error serving file:', error);
    
    if (error.code === 'NoSuchKey') {
      return NextResponse.json({ error: 'File not found' }, { status: 404 });
    }
    
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}