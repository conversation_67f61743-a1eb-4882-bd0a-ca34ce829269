import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for user creation
const userCreateSchema = z.object({
  firstName: z.string().min(1, 'First name is required'),
  surname: z.string().min(1, 'Surname is required'),
  email: z.string().email('Invalid email format'),
  dob: z.string().optional(),
  phone: z.string().optional(),
  country: z.string().optional(),
  city: z.string().optional(),
  license: z.string().optional(),
  passport: z.string().optional(),
  bio: z.string().optional(),
  titleId: z.string().optional(),
  positionId: z.string().optional(),
});

// Validation schema for user update
const userUpdateSchema = z.object({
  firstName: z.string().min(1, 'First name is required').optional(),
  surname: z.string().min(1, 'Surname is required').optional(),
  email: z.string().email('Invalid email format').optional(),
  dob: z.string().optional(),
  phone: z.string().optional(),
  country: z.string().optional(),
  city: z.string().optional(),
  license: z.string().optional(),
  passport: z.string().optional(),
  bio: z.string().optional(),
  titleId: z.string().optional(),
  positionId: z.string().optional(),
});

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const search = searchParams.get('search') || '';
    const position = searchParams.get('position') || '';
    const title = searchParams.get('title') || '';
    const email = searchParams.get('email') || '';
    const phone = searchParams.get('phone') || '';
    const country = searchParams.get('country') || '';
    const city = searchParams.get('city') || '';
    
    const skip = (page - 1) * limit;
    
    // Build where clause for search
    const where: any = {};
    
    // Add search conditions
    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    // Add filter conditions
    if (position) {
      where.positionId = position;
    }
    
    if (title) {
      where.titleId = title;
    }
    
    if (email) {
      where.email = { contains: email, mode: 'insensitive' };
    }
    
    if (phone) {
      where.profile = {
        mobile: { contains: phone, mode: 'insensitive' }
      };
    }
    
    if (country) {
      where.profile = {
        ...where.profile,
        country: { contains: country, mode: 'insensitive' }
      };
    }
    
    if (city) {
      where.profile = {
        ...where.profile,
        city: { contains: city, mode: 'insensitive' }
      };
    }
    
    // Fetch users with pagination
    const [users, totalCount] = await Promise.all([
      prisma.user.findMany({
        where,
        include: {
          profile: true,
          userPositions: {
            include: {
              position: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limit,
      }),
      prisma.user.count({ where }),
    ]);
    
    const totalPages = Math.ceil(totalCount / limit);
    
    return NextResponse.json({
      users: users.map(user => ({
        id: user.id,
        firstName: user.name?.split(' ')[0] || '',
        surname: user.name?.split(' ')[1] || user.name || '',
        email: user.email,
        dob: user.profile?.dateOfBirth,
        phone: user.profile?.mobile,
        country: user.profile?.country,
        city: user.profile?.city,
        license: user.profile?.license || '', // This field doesn't exist in the schema, so we'll need to add it
        passport: user.profile?.passport || '', // This field doesn't exist in the schema, so we'll need to add it
        bio: user.profile?.bio,
        titleId: user.profile?.titleId || '', // This field doesn't exist in the schema, so we'll need to add it
        positionId: user.userPositions[0]?.positionId || '', // Get the first position
        created: user.createdAt,
        updated: user.updatedAt,
      })),
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        pageSize: limit,
      },
    });
  } catch (error) {
    console.error('Error fetching users:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    
    // Validate input
    const validation = userCreateSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    const {
      firstName,
      surname,
      email,
      dob,
      phone,
      country,
      city,
      license,
      passport,
      bio,
      titleId,
      positionId
    } = validation.data;
    
    // Check if user already exists
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });
    
    if (existingUser) {
      return NextResponse.json({ error: 'User with this email already exists' }, { status: 400 });
    }
    
    // Create user
    const user = await prisma.user.create({
      data: {
        name: `${firstName} ${surname}`,
        email,
        profile: {
          create: {
            dateOfBirth: dob ? new Date(dob) : undefined,
            mobile: phone,
            country,
            city,
            bio,
            // Note: license, passport, and titleId fields don't exist in the current schema
            // You would need to add these fields to the UserProfile model
          },
        },
        // Note: Position assignment would require a separate UserPosition record
      },
    });
    
    return NextResponse.json({
      message: 'User created successfully',
      id: user.id,
    });
  } catch (error) {
    console.error('Error creating user:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { id, ...updateData } = body;
    
    if (!id) {
      return NextResponse.json({ error: 'User ID is required' }, { status: 400 });
    }
    
    // Validate input
    const validation = userUpdateSchema.safeParse(updateData);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    const {
      firstName,
      surname,
      email,
      dob,
      phone,
      country,
      city,
      license,
      passport,
      bio,
      titleId,
      positionId
    } = validation.data;
    
    // Update user
    const user = await prisma.user.update({
      where: { id },
      data: {
        ...(firstName || surname) && {
          name: `${firstName || ''} ${surname || ''}`.trim()
        },
        ...(email && { email }),
        profile: {
          update: {
            dateOfBirth: dob ? new Date(dob) : undefined,
            mobile: phone,
            country,
            city,
            bio,
            // Note: license, passport, and titleId fields don't exist in the current schema
          },
        },
      },
      include: {
        profile: true,
      },
    });
    
    return NextResponse.json({
      message: 'User updated successfully',
      user: {
        id: user.id,
        name: user.name,
        email: user.email,
        profile: user.profile,
      },
    });
  } catch (error) {
    console.error('Error updating user:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}