import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    const { userIds } = body;
    
    if (!userIds || !Array.isArray(userIds) || userIds.length === 0) {
      return NextResponse.json({ error: 'User IDs are required' }, { status: 400 });
    }
    
    // Fetch users data
    const users = await prisma.user.findMany({
      where: {
        id: {
          in: userIds
        }
      },
      include: {
        profile: true,
        userPositions: {
          include: {
            position: true
          }
        }
      }
    });
    
    // Transform data for CSV export
    const csvData = users.map(user => ({
      id: user.id,
      firstName: user.name?.split(' ')[0] || '',
      surname: user.name?.split(' ')[1] || user.name || '',
      email: user.email,
      dob: user.profile?.dateOfBirth ? user.profile.dateOfBirth.toISOString().split('T')[0] : '',
      phone: user.profile?.mobile || '',
      country: user.profile?.country || '',
      city: user.profile?.city || '',
      license: user.profile?.license || '', // This field doesn't exist in the schema
      passport: user.profile?.passport || '', // This field doesn't exist in the schema
      bio: user.profile?.bio || '',
      titleId: user.profile?.titleId || '', // This field doesn't exist in the schema
      position: user.userPositions[0]?.position?.title || '', // Get the first position title
      created: user.createdAt.toISOString().split('T')[0],
      updated: user.updatedAt.toISOString().split('T')[0],
    }));
    
    // In a real implementation, you would:
    // 1. Generate a CSV file from the data
    // 2. Save it to storage or return it directly
    // 3. Return a download URL or stream the file
    
    // For this demo, we'll just return the data
    return NextResponse.json({ 
      message: 'Users data retrieved for export',
      data: csvData,
      count: csvData.length
    });
  } catch (error) {
    console.error('Error exporting users:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}