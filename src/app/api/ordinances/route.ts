import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for ordinance creation
const ordinanceCreateSchema = z.object({
  ordinanceTypeId: z.string().min(1, 'Ordinance type is required'),
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
  completedDate: z.string().optional(),
  expirationDate: z.string().optional(),
});

// Validation schema for ordinance update
const ordinanceUpdateSchema = z.object({
  ordinanceTypeId: z.string().min(1, 'Ordinance type is required').optional(),
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
  status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'EXPIRED', 'CANCELLED']).optional(),
  completedDate: z.string().optional(),
  expirationDate: z.string().optional(),
});

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const ordinanceId = searchParams.get('id');
    const typesOnly = searchParams.get('typesOnly') === 'true';
    
    // If typesOnly is requested, return ordinance types
    if (typesOnly) {
      const ordinanceTypes = await prisma.ordinanceType.findMany({
        where: {
          isActive: true,
        },
        orderBy: {
          name: 'asc',
        },
      });
      
      return NextResponse.json({
        ordinanceTypes: ordinanceTypes.map(type => ({
          id: type.id,
          name: type.name,
          description: type.description,
          category: type.category,
        })),
      });
    }
    
    // If an ID is provided, return a specific ordinance
    if (ordinanceId) {
      const ordinance = await prisma.ordinance.findUnique({
        where: {
          id: ordinanceId,
          userId: (session.user as any).id,
        },
        include: {
          ordinanceType: true,
        },
      });
      
      if (!ordinance) {
        return NextResponse.json({ error: 'Ordinance not found' }, { status: 404 });
      }
      
      return NextResponse.json({
        id: ordinance.id,
        ordinanceTypeId: ordinance.ordinanceTypeId,
        title: ordinance.notes || ordinance.ordinanceType?.name || 'Unnamed Ordinance',
        ordinanceTypeName: ordinance.ordinanceType?.name,
        status: ordinance.status,
        notes: ordinance.notes,
        completedDate: ordinance.completedDate,
        expirationDate: ordinance.expirationDate,
        documentPath: ordinance.documentPath,
        createdAt: ordinance.createdAt,
        updatedAt: ordinance.updatedAt,
      });
    }
    
    const skip = (page - 1) * limit;
    
    // Build where clause for search
    const where: any = {
      userId: (session.user as any).id,
    };
    
    if (search) {
      where.OR = [
        { ordinanceType: { name: { contains: search, mode: 'insensitive' } } },
        { notes: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    // Fetch ordinances with pagination
    const [ordinances, totalCount] = await Promise.all([
      prisma.ordinance.findMany({
        where,
        include: {
          ordinanceType: true,
        },
        orderBy: {
          createdAt: 'desc', // Most recent first
        },
        skip,
        take: limit,
      }),
      prisma.ordinance.count({ where }),
    ]);
    
    const totalPages = Math.ceil(totalCount / limit);
    
    return NextResponse.json({
      ordinances: ordinances.map(ordinance => ({
        id: ordinance.id,
        ordinanceTypeId: ordinance.ordinanceTypeId,
        title: ordinance.notes || ordinance.ordinanceType?.name || 'Unnamed Ordinance',
        status: ordinance.status,
        notes: ordinance.notes,
        completedDate: ordinance.completedDate,
        expirationDate: ordinance.expirationDate,
        documentPath: ordinance.documentPath,
        createdAt: ordinance.createdAt,
        updatedAt: ordinance.updatedAt,
      })),
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        pageSize: limit,
      },
    });
  } catch (error) {
    console.error('Error fetching ordinances:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    
    // Validate input
    const validation = ordinanceCreateSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    const { ordinanceTypeId, notes, completedDate, expirationDate } = validation.data;
    
    // Verify that the ordinance type exists
    const ordinanceType = await prisma.ordinanceType.findUnique({
      where: { id: ordinanceTypeId },
    });
    
    if (!ordinanceType) {
      return NextResponse.json(
        { error: 'Invalid ordinance type' },
        { status: 400 }
      );
    }
    
    // Check if user is admin to set status to COMPLETED, otherwise PENDING
    let status = 'PENDING';
    const userId = (session.user as any).id;
    
    // Check if user has admin role
    const userRoles = await prisma.userRole.findMany({
      where: { userId },
      include: { role: true }
    });
    
    const isAdmin = userRoles.some(userRole => 
      userRole.role.name === 'ADMIN' || userRole.role.name === 'admin'
    );
    
    if (isAdmin) {
      status = 'COMPLETED';
    }
    
    // Check for duplicate ordinance (same notes for the same user and type)
    const existingOrdinance = await prisma.ordinance.findFirst({
      where: {
        userId,
        ordinanceTypeId,
        notes: notes !== undefined ? notes : null,
      },
    });
    
    if (existingOrdinance) {
      return NextResponse.json(
        { error: 'An ordinance with the same details already exists' },
        { status: 400 }
      );
    }
    
    // Create ordinance record
    const ordinance = await prisma.ordinance.create({
      data: {
        userId,
        ordinanceTypeId,
        notes,
        completedDate: completedDate ? new Date(completedDate) : undefined,
        expirationDate: expirationDate ? new Date(expirationDate) : undefined,
        status,
      },
    });
    
    return NextResponse.json({
      message: 'Ordinance created successfully',
      id: ordinance.id,
      ordinance: {
        id: ordinance.id,
        ordinanceTypeId: ordinance.ordinanceTypeId,
        title: notes || 'Unnamed Ordinance',
        status: ordinance.status,
        notes: ordinance.notes,
        completedDate: ordinance.completedDate,
        expirationDate: ordinance.expirationDate,
        documentPath: ordinance.documentPath,
        createdAt: ordinance.createdAt,
        updatedAt: ordinance.updatedAt,
      },
    });
  } catch (error) {
    console.error('Error creating ordinance:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const ordinanceId = searchParams.get('id');
    
    if (!ordinanceId) {
      return NextResponse.json({ error: 'Ordinance ID is required' }, { status: 400 });
    }
    
    const body = await req.json();
    
    // Validate input
    const validation = ordinanceUpdateSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    // Check if the ordinance belongs to the user
    const existingOrdinance = await prisma.ordinance.findUnique({
      where: {
        id: ordinanceId,
        userId: (session.user as any).id,
      },
    });
    
    if (!existingOrdinance) {
      return NextResponse.json({ error: 'Ordinance not found' }, { status: 404 });
    }
    
    const updateData: any = {};
    const { ordinanceTypeId, notes, status, completedDate, expirationDate } = validation.data;
    
    if (ordinanceTypeId) {
      // Verify that the ordinance type exists
      const ordinanceType = await prisma.ordinanceType.findUnique({
        where: { id: ordinanceTypeId },
      });
      
      if (!ordinanceType) {
        return NextResponse.json(
          { error: 'Invalid ordinance type' },
          { status: 400 }
        );
      }
      updateData.ordinanceTypeId = ordinanceTypeId;
    }
    
    if (notes !== undefined) updateData.notes = notes;
    if (status) updateData.status = status;
    if (completedDate !== undefined) {
      updateData.completedDate = completedDate ? new Date(completedDate) : null;
    }
    if (expirationDate !== undefined) {
      updateData.expirationDate = expirationDate ? new Date(expirationDate) : null;
    }
    
    // Update ordinance record
    const ordinance = await prisma.ordinance.update({
      where: {
        id: ordinanceId,
      },
      data: updateData,
    });
    
    return NextResponse.json({
      message: 'Ordinance updated successfully',
      id: ordinance.id,
      ordinance: {
        id: ordinance.id,
        ordinanceTypeId: ordinance.ordinanceTypeId,
        title: notes || ordinance.notes || 'Unnamed Ordinance',
        status: ordinance.status,
        notes: ordinance.notes,
        completedDate: ordinance.completedDate,
        expirationDate: ordinance.expirationDate,
        documentPath: ordinance.documentPath,
        createdAt: ordinance.createdAt,
        updatedAt: ordinance.updatedAt,
      },
    });
  } catch (error) {
    console.error('Error updating ordinance:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const ordinanceId = searchParams.get('id');
    
    if (!ordinanceId) {
      return NextResponse.json({ error: 'Ordinance ID is required' }, { status: 400 });
    }
    
    // Check if the ordinance belongs to the user
    const existingOrdinance = await prisma.ordinance.findUnique({
      where: {
        id: ordinanceId,
        userId: (session.user as any).id,
      },
    });
    
    if (!existingOrdinance) {
      return NextResponse.json({ error: 'Ordinance not found' }, { status: 404 });
    }
    
    // Delete the ordinance
    await prisma.ordinance.delete({
      where: {
        id: ordinanceId,
      },
    });
    
    return NextResponse.json({
      message: 'Ordinance deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting ordinance:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}