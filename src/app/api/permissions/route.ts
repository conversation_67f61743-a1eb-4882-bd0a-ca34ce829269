import { NextRequest } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    // Log the incoming request for debugging
    console.log('=== PERMISSIONS ENDPOINT REQUEST ===')
    console.log('URL:', request.url)
    console.log('Method:', request.method)
    console.log('Headers:', Object.fromEntries(request.headers))
    console.log('Authorization Header:', request.headers.get('authorization') || 'None provided')
    console.log('IP:', request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'Unknown')
    console.log('User-Agent:', request.headers.get('user-agent') || 'Unknown')
    console.log('====================================')
    
    // Return information about this remote server
    return Response.json({
      name: 'NWA Member Portal',
      description: 'A comprehensive member management system for the NWA Alliance',
      permissions: [
        {
          name: 'read:profile',
          description: 'Read user profile information'
        },
        {
          name: 'write:profile',
          description: 'Update user profile information'
        },
        {
          name: 'read:documents',
          description: 'Read documents and files'
        },
        {
          name: 'write:documents',
          description: 'Create and edit documents'
        },
        {
          name: 'delete:documents',
          description: 'Delete documents'
        },
        {
          name: 'manage:users',
          description: 'Manage user accounts'
        },
        {
          name: 'view:reports',
          description: 'View system reports'
        }
      ],
      roles: [
        {
          name: 'admin',
          description: 'Administrator with full access',
          permissions: [
            'read:profile',
            'write:profile',
            'read:documents',
            'write:documents',
            'delete:documents',
            'manage:users',
            'view:reports'
          ]
        },
        {
          name: 'editor',
          description: 'Content editor with document management access',
          permissions: [
            'read:profile',
            'write:profile',
            'read:documents',
            'write:documents'
          ]
        },
        {
          name: 'user',
          description: 'Regular user with read access',
          permissions: [
            'read:profile',
            'read:documents'
          ]
        }
      ]
    })
  } catch (error) {
    console.error('Info endpoint error:', error)
    return Response.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}