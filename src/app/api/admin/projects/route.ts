import { NextRequest, NextResponse } from 'next/server';
import { createA<PERSON><PERSON>hai<PERSON> } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import { ApiKeyService } from '@/lib/services/api-key';
import { z } from 'zod';

// Zod schema for request validation
const projectSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  allowedOrigins: z.array(z.string().url()).optional(),
});

// Create authentication middleware chain for admin access
const authMiddleware = createAuthChain({
  requireApiKey: true,
  requireJwt: true,
  requireCors: true,
  requiredScopes: ['admin:*', 'admin:projects'],
  rateLimitConfig: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 50, // 50 requests per minute
  },
});

export async function GET(request: NextRequest) {
  try {
    // Run authentication middleware
    const authResponse = await authMiddleware(request);
    if (authResponse) {
      return authResponse;
    }

    // Parse query parameters
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page') || '1', 10);
    const limit = parseInt(url.searchParams.get('limit') || '25', 10);
    const active = url.searchParams.get('active');

    // Validate pagination parameters
    if (page < 1 || limit < 1 || limit > 100) {
      return NextResponse.json(
        { error: 'Bad Request', message: 'Invalid pagination parameters' },
        { status: 400 }
      );
    }

    // Build where clause
    const where: any = {};
    if (active === 'true') {
      where.isActive = true;
    } else if (active === 'false') {
      where.isActive = false;
    }

    // Fetch projects with pagination
    const [projects, total] = await Promise.all([
      prisma.project.findMany({
        where,
        select: {
          id: true,
          name: true,
          description: true,
          allowedOrigins: true,
          isActive: true,
          createdAt: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.project.count({ where }),
    ]);

    // Return paginated response
    return NextResponse.json({
      projects,
      pagination: {
        page,
        limit,
        total,
      },
    });
  } catch (error) {
    console.error('Error fetching projects:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    // Run authentication middleware
    const authResponse = await authMiddleware(request);
    if (authResponse) {
      return authResponse;
    }

    // Parse and validate request body
    const body = await request.json();
    const parsed = projectSchema.safeParse(body);
    
    if (!parsed.success) {
      return NextResponse.json(
        { 
          error: 'Bad Request', 
          message: 'Invalid request body',
          details: parsed.error.flatten()
        },
        { status: 400 }
      );
    }

    const { name, description, allowedOrigins } = parsed.data;

    // Create API key service and generate new API key
    const apiKeyService = new ApiKeyService();
    const apiKey = apiKeyService.generateApiKey();
    const apiKeyHash = apiKeyService.hashApiKey(apiKey);

    // Create new project
    const project = await prisma.project.create({
      data: {
        name,
        description,
        apiKeyHash,
        allowedOrigins: allowedOrigins || [],
        isActive: true,
      },
      select: {
        id: true,
        name: true,
        description: true,
        allowedOrigins: true,
        isActive: true,
        createdAt: true,
      },
    });

    // Return project with API key (only on creation)
    return NextResponse.json({
      project: {
        ...project,
        apiKey, // Include the API key only on creation
      },
    });
  } catch (error) {
    console.error('Error creating project:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}