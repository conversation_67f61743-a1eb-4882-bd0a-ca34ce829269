import { NextRequest, NextResponse } from 'next/server';
import { createA<PERSON><PERSON>hai<PERSON> } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Zod schema for request validation
const updateProjectSchema = z.object({
  name: z.string().min(1).max(100).optional(),
  description: z.string().max(500).optional(),
  allowedOrigins: z.array(z.string().url()).optional(),
  isActive: z.boolean().optional(),
});

// Create authentication middleware chain for admin access
const authMiddleware = createAuthChain({
  requireApiKey: true,
  requireJwt: true,
  requireCors: true,
  requiredScopes: ['admin:*', 'admin:projects'],
  rateLimitConfig: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 50, // 50 requests per minute
  },
});

export async function GET(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Run authentication middleware
    const authResponse = await authMiddleware(request);
    if (authResponse) {
      return authResponse;
    }

    const { id: projectId } = await params;

    // Fetch project by ID
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      select: {
        id: true,
        name: true,
        description: true,
        allowedOrigins: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    if (!project) {
      return NextResponse.json(
        { error: 'Not Found', message: 'Project not found' },
        { status: 404 }
      );
    }

    // Return project details
    return NextResponse.json({ project });
  } catch (error) {
    console.error('Error fetching project:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Run authentication middleware
    const authResponse = await authMiddleware(request);
    if (authResponse) {
      return authResponse;
    }

    const { id: projectId } = await params;

    // Parse and validate request body
    const body = await request.json();
    const parsed = updateProjectSchema.safeParse(body);
    
    if (!parsed.success) {
      return NextResponse.json(
        { 
          error: 'Bad Request', 
          message: 'Invalid request body',
          details: parsed.error.flatten()
        },
        { status: 400 }
      );
    }

    const { name, description, allowedOrigins, isActive } = parsed.data;

    // Check if project exists
    const existingProject = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!existingProject) {
      return NextResponse.json(
        { error: 'Not Found', message: 'Project not found' },
        { status: 404 }
      );
    }

    // Update project
    const project = await prisma.project.update({
      where: { id: projectId },
      data: {
        ...(name !== undefined && { name }),
        ...(description !== undefined && { description }),
        ...(allowedOrigins !== undefined && { allowedOrigins }),
        ...(isActive !== undefined && { isActive }),
      },
      select: {
        id: true,
        name: true,
        description: true,
        allowedOrigins: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
      },
    });

    // Return updated project
    return NextResponse.json({ project });
  } catch (error) {
    console.error('Error updating project:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    // Run authentication middleware
    const authResponse = await authMiddleware(request);
    if (authResponse) {
      return authResponse;
    }

    const { id: projectId } = await params;
    const url = new URL(request.url);
    const permanent = url.searchParams.get('permanent') === 'true';

    // Check if project exists
    const existingProject = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!existingProject) {
      return NextResponse.json(
        { error: 'Not Found', message: 'Project not found' },
        { status: 404 }
      );
    }

    if (permanent) {
      // Permanently delete project
      await prisma.project.delete({
        where: { id: projectId },
      });
      
      return NextResponse.json({
        success: true,
        message: 'Project deleted permanently',
      });
    } else {
      // Deactivate project
      const project = await prisma.project.update({
        where: { id: projectId },
        data: {
          isActive: false,
        },
        select: {
          id: true,
          name: true,
          isActive: true,
        },
      });
      
      return NextResponse.json({
        success: true,
        message: 'Project deactivated successfully',
        project,
      });
    }
  } catch (error) {
    console.error('Error deleting project:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}