import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';
import { predefinedRateLimiters } from '@/lib/middleware/rate-limiting';

// Validation schema for creating/updating roles
const roleSchema = z.object({
  name: z.string().min(1, 'Role name is required').max(50, 'Role name must be less than 50 characters'),
  description: z.string().max(255, 'Description must be less than 255 characters').optional(),
  permissionIds: z.array(z.string()).optional(),
});

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = user.userRoles.some(userRole => 
      userRole.role.name === 'admin'
    );

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Fetch all roles with counts
    const roles = await prisma.role.findMany({
      include: {
        _count: {
          select: {
            userRoles: true,
            rolePermissions: true,
          },
        },
      },
      orderBy: {
        name: 'asc',
      },
    });

    // Transform roles for response
    const transformedRoles = roles.map(role => ({
      id: role.id,
      name: role.name,
      description: role.description,
      isSystem: role.isSystem,
      userCount: role._count.userRoles,
      permissionCount: role._count.rolePermissions,
      createdAt: role.createdAt.toISOString(),
      updatedAt: role.updatedAt.toISOString(),
    }));

    return NextResponse.json({ roles: transformedRoles });
  } catch (error) {
    console.error('Error fetching roles:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = user.userRoles.some(userRole => 
      userRole.role.name === 'admin'
    );

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await req.json();
    const validation = roleSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Bad Request', message: 'Invalid request body', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { name, description, permissionIds = [] } = validation.data;

    // Check if role already exists
    const existingRole = await prisma.role.findUnique({
      where: { name },
    });

    if (existingRole) {
      return NextResponse.json({ error: 'Role with this name already exists' }, { status: 400 });
    }

    // Verify all permissions exist
    if (permissionIds.length > 0) {
      const permissions = await prisma.permission.findMany({
        where: {
          id: {
            in: permissionIds,
          },
        },
      });

      if (permissions.length !== permissionIds.length) {
        return NextResponse.json({ error: 'One or more permissions not found' }, { status: 400 });
      }
    }

    // Create role
    const role = await prisma.role.create({
      data: {
        name,
        description,
        isSystem: false, // Only system roles are marked as system
      },
    });

    // Assign permissions to role
    if (permissionIds.length > 0) {
      await Promise.all(
        permissionIds.map(permissionId =>
          prisma.rolePermission.create({
            data: {
              roleId: role.id,
              permissionId: permissionId,
            },
          })
        )
      );
    }

    // Fetch role with counts
    const roleWithCounts = await prisma.role.findUnique({
      where: { id: role.id },
      include: {
        _count: {
          select: {
            userRoles: true,
            rolePermissions: true,
          },
        },
      },
    });

    // Transform role for response
    const transformedRole = {
      id: roleWithCounts!.id,
      name: roleWithCounts!.name,
      description: roleWithCounts!.description,
      isSystem: roleWithCounts!.isSystem,
      userCount: roleWithCounts!._count.userRoles,
      permissionCount: roleWithCounts!._count.rolePermissions,
      createdAt: roleWithCounts!.createdAt.toISOString(),
      updatedAt: roleWithCounts!.updatedAt.toISOString(),
    };

    return NextResponse.json({ 
      message: 'Role created successfully',
      role: transformedRole,
    });
  } catch (error) {
    console.error('Error creating role:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function PUT(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = user.userRoles.some(userRole => 
      userRole.role.name === 'admin'
    );

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id: roleId } = await params;

    // Validate role exists and is not a system role
    const existingRole = await prisma.role.findUnique({
      where: { id: roleId },
    });

    if (!existingRole) {
      return NextResponse.json({ error: 'Role not found' }, { status: 404 });
    }

    if (existingRole.isSystem) {
      return NextResponse.json({ error: 'Cannot modify system roles' }, { status: 400 });
    }

    const body = await req.json();
    const validation = roleSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Bad Request', message: 'Invalid request body', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { name, description, permissionIds = [] } = validation.data;

    // Check if another role with the same name exists
    if (name !== existingRole.name) {
      const duplicateRole = await prisma.role.findUnique({
        where: { name },
      });

      if (duplicateRole) {
        return NextResponse.json({ error: 'Role with this name already exists' }, { status: 400 });
      }
    }

    // Verify all permissions exist
    if (permissionIds.length > 0) {
      const permissions = await prisma.permission.findMany({
        where: {
          id: {
            in: permissionIds,
          },
        },
      });

      if (permissions.length !== permissionIds.length) {
        return NextResponse.json({ error: 'One or more permissions not found' }, { status: 400 });
      }
    }

    // Update role
    const updatedRole = await prisma.role.update({
      where: { id: roleId },
      data: {
        name,
        description,
      },
    });

    // Update role permissions
    // First, delete existing permissions
    await prisma.rolePermission.deleteMany({
      where: {
        roleId: roleId,
      },
    });

    // Then, assign new permissions
    if (permissionIds.length > 0) {
      await Promise.all(
        permissionIds.map(permissionId =>
          prisma.rolePermission.create({
            data: {
              roleId: roleId,
              permissionId: permissionId,
            },
          })
        )
      );
    }

    // Fetch updated role with counts
    const roleWithCounts = await prisma.role.findUnique({
      where: { id: updatedRole.id },
      include: {
        _count: {
          select: {
            userRoles: true,
            rolePermissions: true,
          },
        },
      },
    });

    // Transform role for response
    const transformedRole = {
      id: roleWithCounts!.id,
      name: roleWithCounts!.name,
      description: roleWithCounts!.description,
      isSystem: roleWithCounts!.isSystem,
      userCount: roleWithCounts!._count.userRoles,
      permissionCount: roleWithCounts!._count.rolePermissions,
      createdAt: roleWithCounts!.createdAt.toISOString(),
      updatedAt: roleWithCounts!.updatedAt.toISOString(),
    };

    return NextResponse.json({ 
      message: 'Role updated successfully',
      role: transformedRole,
    });
  } catch (error) {
    console.error('Error updating role:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest, { params }: { params: Promise<{ id: string }> }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Apply rate limiting
    const rateLimitResult = await predefinedRateLimiters.api(req);
    if (!rateLimitResult.allowed) {
      return NextResponse.json(
        { error: 'Too Many Requests', message: 'Rate limit exceeded. Please try again later.' },
        { 
          status: 429,
          headers: rateLimitResult.headers
        }
      );
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: {
        userRoles: {
          include: {
            role: true,
          },
        },
      },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = user.userRoles.some(userRole => 
      userRole.role.name === 'admin'
    );

    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id: roleId } = await params;

    // Validate role exists and is not a system role
    const existingRole = await prisma.role.findUnique({
      where: { id: roleId },
    });

    if (!existingRole) {
      return NextResponse.json({ error: 'Role not found' }, { status: 404 });
    }

    if (existingRole.isSystem) {
      return NextResponse.json({ error: 'Cannot delete system roles' }, { status: 400 });
    }

    // Delete role
    await prisma.role.delete({
      where: { id: roleId },
    });

    return NextResponse.json({ message: 'Role deleted successfully' });
  } catch (error) {
    console.error('Error deleting role:', error);
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}