import { NextRequest, NextResponse } from 'next/server';
import { createAuth<PERSON>hai<PERSON> } from '@/lib/middleware';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Zod schema for request validation
const assignScopesSchema = z.object({
  scopeIds: z.array(z.string()),
});

// Create authentication middleware chain for admin access
const authMiddleware = createAuthChain({
  requireApiKey: true,
  requireJwt: true,
  requireCors: true,
  requiredScopes: ['admin:*', 'admin:projects'],
  rateLimitConfig: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 50, // 50 requests per minute
  },
});

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string; projectId: string }> }
) {
  try {
    // Run authentication middleware
    const authResponse = await authMiddleware(request);
    if (authResponse) {
      return authResponse;
    }

    const { userId, projectId } = await params;

    // Verify user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        id: true,
        name: true,
        email: true,
      },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Not Found', message: 'User not found' },
        { status: 404 }
      );
    }

    // Verify project exists
    const project = await prisma.project.findUnique({
      where: { id: projectId },
      select: {
        id: true,
        name: true,
      },
    });

    if (!project) {
      return NextResponse.json(
        { error: 'Not Found', message: 'Project not found' },
        { status: 404 }
      );
    }

    // Fetch user's scopes for this project
    const userProjectScopes = await prisma.userProjectScope.findMany({
      where: {
        userId,
        projectId,
      },
      include: {
        scope: {
          select: {
            id: true,
            name: true,
            description: true,
          },
        },
      },
    });

    const scopes = userProjectScopes.map(ups => ups.scope);

    // Return user, project, and scopes
    return NextResponse.json({
      user,
      project,
      scopes,
    });
  } catch (error) {
    console.error('Error fetching user project scopes:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string; projectId: string }> }
) {
  try {
    // Run authentication middleware
    const authResponse = await authMiddleware(request);
    if (authResponse) {
      return authResponse;
    }

    const { userId, projectId } = await params;

    // Parse and validate request body
    const body = await request.json();
    const parsed = assignScopesSchema.safeParse(body);
    
    if (!parsed.success) {
      return NextResponse.json(
        { 
          error: 'Bad Request', 
          message: 'Invalid request body',
          details: parsed.error.flatten()
        },
        { status: 400 }
      );
    }

    const { scopeIds } = parsed.data;

    // Verify user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Not Found', message: 'User not found' },
        { status: 404 }
      );
    }

    // Verify project exists
    const project = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!project) {
      return NextResponse.json(
        { error: 'Not Found', message: 'Project not found' },
        { status: 404 }
      );
    }

    // Verify all scopes exist
    const scopes = await prisma.scope.findMany({
      where: {
        id: {
          in: scopeIds,
        },
      },
    });

    if (scopes.length !== scopeIds.length) {
      const foundScopeIds = scopes.map(s => s.id);
      const missingScopeIds = scopeIds.filter(id => !foundScopeIds.includes(id));
      
      return NextResponse.json(
        { error: 'Not Found', message: 'Some scopes not found', missingScopeIds },
        { status: 404 }
      );
    }

    // Assign scopes to user for this project
    const userProjectScopes = await Promise.all(
      scopeIds.map(scopeId =>
        prisma.userProjectScope.upsert({
          where: {
            userId_projectId_scopeId: {
              userId,
              projectId,
              scopeId,
            },
          },
          update: {},
          create: {
            userId,
            projectId,
            scopeId,
          },
          include: {
            scope: {
              select: {
                name: true,
              },
            },
          },
        })
      )
    );

    const grantedScopes = userProjectScopes.map(ups => ups.scope.name);

    // Return success response
    return NextResponse.json({
      success: true,
      grantedScopes,
      message: 'Scopes granted successfully',
    });
  } catch (error) {
    console.error('Error assigning scopes:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ userId: string; projectId: string }> }
) {
  try {
    // Run authentication middleware
    const authResponse = await authMiddleware(request);
    if (authResponse) {
      return authResponse;
    }

    const { userId, projectId } = await params;
    const body = await request.json();
    const { scopeId } = body;

    // Verify user exists
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) {
      return NextResponse.json(
        { error: 'Not Found', message: 'User not found' },
        { status: 404 }
      );
    }

    // Verify project exists
    const project = await prisma.project.findUnique({
      where: { id: projectId },
    });

    if (!project) {
      return NextResponse.json(
        { error: 'Not Found', message: 'Project not found' },
        { status: 404 }
      );
    }

    // Verify scope exists
    const scope = await prisma.scope.findUnique({
      where: { id: scopeId },
    });

    if (!scope) {
      return NextResponse.json(
        { error: 'Not Found', message: 'Scope not found' },
        { status: 404 }
      );
    }

    // Check if the user-project-scope relationship exists
    const userProjectScope = await prisma.userProjectScope.findUnique({
      where: {
        userId_projectId_scopeId: {
          userId,
          projectId,
          scopeId,
        },
      },
    });

    if (!userProjectScope) {
      return NextResponse.json(
        { error: 'Not Found', message: 'User does not have this scope for this project' },
        { status: 404 }
      );
    }

    // Revoke scope from user for this project
    await prisma.userProjectScope.delete({
      where: {
        userId_projectId_scopeId: {
          userId,
          projectId,
          scopeId,
        },
      },
    });

    // Return success response
    return NextResponse.json({
      success: true,
      message: 'Scope revoked successfully',
    });
  } catch (error) {
    console.error('Error revoking scope:', error);
    
    return NextResponse.json(
      { error: 'Internal Server Error', message: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}