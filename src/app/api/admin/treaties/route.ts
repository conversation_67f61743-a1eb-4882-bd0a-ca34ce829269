import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for admin treaty queries
const adminTreatyQuerySchema = z.object({
  userId: z.string().optional(),
  status: z.enum(['ACTIVE', 'EXPIRED', 'TERMINATED', 'PENDING_RENEWAL']).optional(),
  treatyTypeId: z.string().optional(),
  page: z.string().optional(),
  limit: z.string().optional(),
});

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = user.userRoles.some(userRole => userRole.role.name === 'ADMIN');
    
    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const queryData = {
      userId: searchParams.get('userId') || undefined,
      status: searchParams.get('status') as any || undefined,
      treatyTypeId: searchParams.get('treatyTypeId') || undefined,
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '10',
    };

    // Validate query parameters
    const validation = adminTreatyQuerySchema.safeParse(queryData);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { userId, status, treatyTypeId, page, limit } = validation.data;
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;
    const skip = (pageNum - 1) * limitNum;

    // Build where clause
    const where: any = {};
    
    if (userId) {
      where.userId = userId;
    }
    
    if (status) {
      where.status = status;
    }
    
    if (treatyTypeId) {
      where.treatyTypeId = treatyTypeId;
    }

    // Fetch treaties with pagination
    const [treaties, totalCount] = await Promise.all([
      prisma.treaty.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          treatyType: true,
        },
        orderBy: {
          signedDate: 'desc',
        },
        skip,
        take: limitNum,
      }),
      prisma.treaty.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limitNum);

    return NextResponse.json({
      treaties: treaties.map(treaty => ({
        id: treaty.id,
        userId: treaty.userId,
        userName: treaty.user?.name,
        userEmail: treaty.user?.email,
        treatyTypeId: treaty.treatyTypeId,
        treatyTypeName: treaty.treatyType?.name,
        status: treaty.status,
        notes: treaty.notes,
        signedDate: treaty.signedDate,
        expirationDate: treaty.expirationDate,
        renewalDate: treaty.renewalDate,
        documentPath: treaty.documentPath,
        createdAt: treaty.createdAt,
        updatedAt: treaty.updatedAt,
      })),
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalCount,
        pageSize: limitNum,
      },
    });
  } catch (error) {
    console.error('Error fetching treaties:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}