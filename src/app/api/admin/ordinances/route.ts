import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for admin ordinance queries
const adminOrdinanceQuerySchema = z.object({
  userId: z.string().optional(),
  status: z.enum(['PENDING', 'IN_PROGRESS', 'COMPLETED', 'EXPIRED', 'CANCELLED']).optional(),
  ordinanceTypeId: z.string().optional(),
  page: z.string().optional(),
  limit: z.string().optional(),
});

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } },
    });

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }

    const isAdmin = user.userRoles.some(userRole => userRole.role.name === 'ADMIN');
    
    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(req.url);
    const queryData = {
      userId: searchParams.get('userId') || undefined,
      status: searchParams.get('status') as any || undefined,
      ordinanceTypeId: searchParams.get('ordinanceTypeId') || undefined,
      page: searchParams.get('page') || '1',
      limit: searchParams.get('limit') || '10',
    };

    // Validate query parameters
    const validation = adminOrdinanceQuerySchema.safeParse(queryData);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }

    const { userId, status, ordinanceTypeId, page, limit } = validation.data;
    const pageNum = page ? parseInt(page, 10) : 1;
    const limitNum = limit ? parseInt(limit, 10) : 10;
    const skip = (pageNum - 1) * limitNum;

    // Build where clause
    const where: any = {};
    
    if (userId) {
      where.userId = userId;
    }
    
    if (status) {
      where.status = status;
    }
    
    if (ordinanceTypeId) {
      where.ordinanceTypeId = ordinanceTypeId;
    }

    // Fetch ordinances with pagination
    const [ordinances, totalCount] = await Promise.all([
      prisma.ordinance.findMany({
        where,
        include: {
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
          ordinanceType: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip,
        take: limitNum,
      }),
      prisma.ordinance.count({ where }),
    ]);

    const totalPages = Math.ceil(totalCount / limitNum);

    return NextResponse.json({
      ordinances: ordinances.map(ordinance => ({
        id: ordinance.id,
        userId: ordinance.userId,
        userName: ordinance.user?.name,
        userEmail: ordinance.user?.email,
        ordinanceTypeId: ordinance.ordinanceTypeId,
        ordinanceTypeName: ordinance.ordinanceType?.name,
        status: ordinance.status,
        notes: ordinance.notes,
        completedDate: ordinance.completedDate,
        expirationDate: ordinance.expirationDate,
        documentPath: ordinance.documentPath,
        createdAt: ordinance.createdAt,
        updatedAt: ordinance.updatedAt,
      })),
      pagination: {
        currentPage: pageNum,
        totalPages,
        totalCount,
        pageSize: limitNum,
      },
    });
  } catch (error) {
    console.error('Error fetching ordinances:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}