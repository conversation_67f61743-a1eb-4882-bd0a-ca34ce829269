import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// PUT /api/positions/positions/[id] - Update a position
export async function PUT(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name === 'ADMIN');
    
    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;
    const body = await request.json();
    const { title, description, level, parentId, isActive } = body;

    // Validate input
    if (!title || title.trim().length === 0) {
      return NextResponse.json({ error: 'Position title is required' }, { status: 400 });
    }

    // Check if another position already exists with this title
    const existingPosition = await prisma.position.findFirst({
      where: {
        title: title.trim(),
        NOT: { id }
      }
    });

    if (existingPosition) {
      return NextResponse.json({ error: 'A position with this title already exists' }, { status: 400 });
    }

    const position = await prisma.position.update({
      where: { id },
      data: {
        title: title.trim(),
        description: description?.trim() || null,
        level: level || 1,
        parentId: parentId || null,
        isActive: typeof isActive === 'boolean' ? isActive : undefined
      }
    });

    return NextResponse.json(position);
  } catch (error) {
    console.error('Error updating position:', error);
    return NextResponse.json({ error: 'Failed to update position' }, { status: 500 });
  }
}

// DELETE /api/positions/positions/[id] - Delete a position
export async function DELETE(request: Request, { params }: { params: Promise<{ id: string }> }) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name === 'ADMIN');
    
    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { id } = await params;

    // Check if position is being used by any users
    const userPositionCount = await prisma.userPosition.count({
      where: { positionId: id }
    });

    if (userPositionCount > 0) {
      return NextResponse.json({ 
        error: 'Cannot delete position because it is assigned to users' 
      }, { status: 400 });
    }

    // Delete the position
    await prisma.position.delete({
      where: { id }
    });

    return NextResponse.json({ message: 'Position deleted successfully' });
  } catch (error: any) {
    if (error.code === 'P2025') {
      return NextResponse.json({ error: 'Position not found' }, { status: 404 });
    }
    
    console.error('Error deleting position:', error);
    return NextResponse.json({ error: 'Failed to delete position' }, { status: 500 });
  }
}