import { NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';

// GET /api/positions/positions - Get all positions
// Optional query param: titleId to filter positions by title
export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name === 'ADMIN');
    
    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const { searchParams } = new URL(request.url);
    const titleId = searchParams.get('titleId');

    let positions;
    if (titleId) {
      // Get positions associated with a specific title
      positions = await prisma.position.findMany({
        where: {
          titlePositions: {
            some: {
              titleId
            }
          }
        },
        include: {
          parent: true,
          children: true
        },
        orderBy: { title: 'asc' }
      });
    } else {
      // Get all positions
      positions = await prisma.position.findMany({
        include: {
          parent: true,
          children: true
        },
        orderBy: { title: 'asc' }
      });
    }

    return NextResponse.json(positions);
  } catch (error) {
    console.error('Error fetching positions:', error);
    return NextResponse.json({ error: 'Failed to fetch positions' }, { status: 500 });
  }
}

// POST /api/positions/positions - Create a new position
export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Check if user has admin role
    const user = await prisma.user.findUnique({
      where: { id: (session.user as any).id },
      include: { userRoles: { include: { role: true } } }
    });

    const isAdmin = user?.userRoles.some(userRole => userRole.role.name === 'ADMIN');
    
    if (!isAdmin) {
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    const body = await request.json();
    const { title, description, level, parentId } = body;

    // Validate input
    if (!title || title.trim().length === 0) {
      return NextResponse.json({ error: 'Position title is required' }, { status: 400 });
    }

    // Check if position already exists
    const existingPosition = await prisma.position.findUnique({
      where: { title: title.trim() }
    });

    if (existingPosition) {
      return NextResponse.json({ error: 'A position with this title already exists' }, { status: 400 });
    }

    const position = await prisma.position.create({
      data: {
        title: title.trim(),
        description: description?.trim() || null,
        level: level || 1,
        parentId: parentId || null
      }
    });

    return NextResponse.json(position, { status: 201 });
  } catch (error) {
    console.error('Error creating position:', error);
    return NextResponse.json({ error: 'Failed to create position' }, { status: 500 });
  }
}