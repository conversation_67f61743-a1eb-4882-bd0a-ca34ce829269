import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

// Validation schema for treaty creation
const treatyCreateSchema = z.object({
  treatyTypeId: z.string().min(1, 'Treaty type is required'),
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
  signedDate: z.string().optional(),
  expirationDate: z.string().optional(),
  renewalDate: z.string().optional(),
});

// Validation schema for treaty update
const treatyUpdateSchema = z.object({
  treatyTypeId: z.string().min(1, 'Treaty type is required').optional(),
  notes: z.string().max(1000, 'Notes must be less than 1000 characters').optional(),
  status: z.enum(['ACTIVE', 'EXPIRED', 'TERMINATED', 'PENDING_RENEWAL']).optional(),
  signedDate: z.string().optional(),
  expirationDate: z.string().optional(),
  renewalDate: z.string().optional(),
});

export async function GET(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const search = searchParams.get('search') || '';
    const page = parseInt(searchParams.get('page') || '1', 10);
    const limit = parseInt(searchParams.get('limit') || '10', 10);
    const treatyId = searchParams.get('id');
    
    // If an ID is provided, return a specific treaty
    if (treatyId) {
      const treaty = await prisma.treaty.findUnique({
        where: {
          id: treatyId,
          userId: (session.user as any).id,
        },
        include: {
          treatyType: true,
        },
      });
      
      if (!treaty) {
        return NextResponse.json({ error: 'Treaty not found' }, { status: 404 });
      }
      
      return NextResponse.json({
        id: treaty.id,
        treatyTypeId: treaty.treatyTypeId,
        treatyTypeName: treaty.treatyType?.name,
        status: treaty.status,
        notes: treaty.notes,
        signedDate: treaty.signedDate,
        expirationDate: treaty.expirationDate,
        renewalDate: treaty.renewalDate,
        documentPath: treaty.documentPath,
        createdAt: treaty.createdAt,
        updatedAt: treaty.updatedAt,
      });
    }
    
    const skip = (page - 1) * limit;
    
    // Build where clause for search
    const where: any = {
      userId: (session.user as any).id,
    };
    
    if (search) {
      where.OR = [
        { treatyType: { name: { contains: search, mode: 'insensitive' } } },
        { notes: { contains: search, mode: 'insensitive' } },
      ];
    }
    
    // Fetch treaties with pagination
    const [treaties, totalCount] = await Promise.all([
      prisma.treaty.findMany({
        where,
        include: {
          treatyType: true,
        },
        orderBy: {
          signedDate: 'desc', // Most recent first
        },
        skip,
        take: limit,
      }),
      prisma.treaty.count({ where }),
    ]);
    
    const totalPages = Math.ceil(totalCount / limit);
    
    return NextResponse.json({
      treaties: treaties.map(treaty => ({
        id: treaty.id,
        treatyTypeId: treaty.treatyTypeId,
        title: treaty.treatyType?.name || 'Unnamed Treaty',
        status: treaty.status,
        notes: treaty.notes,
        signedDate: treaty.signedDate,
        expirationDate: treaty.expirationDate,
        renewalDate: treaty.renewalDate,
        documentPath: treaty.documentPath,
        createdAt: treaty.createdAt,
        updatedAt: treaty.updatedAt,
      })),
      pagination: {
        currentPage: page,
        totalPages,
        totalCount,
        pageSize: limit,
      },
    });
  } catch (error) {
    console.error('Error fetching treaties:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function POST(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const body = await req.json();
    
    // Validate input
    const validation = treatyCreateSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    const { treatyTypeId, notes, signedDate, expirationDate, renewalDate } = validation.data;
    
    // Verify that the treaty type exists
    const treatyType = await prisma.treatyType.findUnique({
      where: { id: treatyTypeId },
    });
    
    if (!treatyType) {
      return NextResponse.json(
        { error: 'Invalid treaty type' },
        { status: 400 }
      );
    }
    
    // Create treaty record
    const treaty = await prisma.treaty.create({
      data: {
        userId: (session.user as any).id,
        treatyTypeId,
        notes,
        signedDate: signedDate ? new Date(signedDate) : undefined,
        expirationDate: expirationDate ? new Date(expirationDate) : undefined,
        renewalDate: renewalDate ? new Date(renewalDate) : undefined,
        status: 'ACTIVE',
      },
    });
    
    return NextResponse.json({
      message: 'Treaty created successfully',
      id: treaty.id,
      treaty: {
        id: treaty.id,
        treatyTypeId: treaty.treatyTypeId,
        status: treaty.status,
        notes: treaty.notes,
        signedDate: treaty.signedDate,
        expirationDate: treaty.expirationDate,
        renewalDate: treaty.renewalDate,
        documentPath: treaty.documentPath,
        createdAt: treaty.createdAt,
        updatedAt: treaty.updatedAt,
      },
    });
  } catch (error) {
    console.error('Error creating treaty:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function PUT(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const treatyId = searchParams.get('id');
    
    if (!treatyId) {
      return NextResponse.json({ error: 'Treaty ID is required' }, { status: 400 });
    }
    
    const body = await req.json();
    
    // Validate input
    const validation = treatyUpdateSchema.safeParse(body);
    
    if (!validation.success) {
      return NextResponse.json(
        { error: 'Validation error', details: validation.error.flatten() },
        { status: 400 }
      );
    }
    
    // Check if the treaty belongs to the user
    const existingTreaty = await prisma.treaty.findUnique({
      where: {
        id: treatyId,
        userId: (session.user as any).id,
      },
    });
    
    if (!existingTreaty) {
      return NextResponse.json({ error: 'Treaty not found' }, { status: 404 });
    }
    
    const updateData: any = {};
    const { treatyTypeId, notes, status, signedDate, expirationDate, renewalDate } = validation.data;
    
    if (treatyTypeId) {
      // Verify that the treaty type exists
      const treatyType = await prisma.treatyType.findUnique({
        where: { id: treatyTypeId },
      });
      
      if (!treatyType) {
        return NextResponse.json(
          { error: 'Invalid treaty type' },
          { status: 400 }
        );
      }
      updateData.treatyTypeId = treatyTypeId;
    }
    
    if (notes !== undefined) updateData.notes = notes;
    if (status) updateData.status = status;
    if (signedDate !== undefined) {
      updateData.signedDate = signedDate ? new Date(signedDate) : null;
    }
    if (expirationDate !== undefined) {
      updateData.expirationDate = expirationDate ? new Date(expirationDate) : null;
    }
    if (renewalDate !== undefined) {
      updateData.renewalDate = renewalDate ? new Date(renewalDate) : null;
    }
    
    // Update treaty record
    const treaty = await prisma.treaty.update({
      where: {
        id: treatyId,
      },
      data: updateData,
    });
    
    return NextResponse.json({
      message: 'Treaty updated successfully',
      id: treaty.id,
      treaty: {
        id: treaty.id,
        treatyTypeId: treaty.treatyTypeId,
        status: treaty.status,
        notes: treaty.notes,
        signedDate: treaty.signedDate,
        expirationDate: treaty.expirationDate,
        renewalDate: treaty.renewalDate,
        documentPath: treaty.documentPath,
        createdAt: treaty.createdAt,
        updatedAt: treaty.updatedAt,
      },
    });
  } catch (error) {
    console.error('Error updating treaty:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}

export async function DELETE(req: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const { searchParams } = new URL(req.url);
    const treatyId = searchParams.get('id');
    
    if (!treatyId) {
      return NextResponse.json({ error: 'Treaty ID is required' }, { status: 400 });
    }
    
    // Check if the treaty belongs to the user
    const existingTreaty = await prisma.treaty.findUnique({
      where: {
        id: treatyId,
        userId: (session.user as any).id,
      },
    });
    
    if (!existingTreaty) {
      return NextResponse.json({ error: 'Treaty not found' }, { status: 404 });
    }
    
    // Delete the treaty
    await prisma.treaty.delete({
      where: {
        id: treatyId,
      },
    });
    
    return NextResponse.json({
      message: 'Treaty deleted successfully',
    });
  } catch (error) {
    console.error('Error deleting treaty:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}