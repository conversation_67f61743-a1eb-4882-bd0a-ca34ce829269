'use client';

import React, { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { RemoteServersTab } from '@/components/settings/RemoteServersTab';
import { SecurityAuditTab } from '@/components/settings/SecurityAuditTab';
import { SystemSettingsTab } from '@/components/settings/SystemSettingsTab';
import { UserManagementTab } from '@/components/settings/UserManagementTab';
import { Card, CardContent } from '@/components/ui/card';
import {
  Server,
  Shield,
  Settings as SettingsIcon,
  Users,
  Key,
  Activity
} from 'lucide-react';

export default function SettingsPage() {
  const [activeTab, setActiveTab] = useState('remote-servers');

  const settingsSections = [
    {
      id: 'remote-servers',
      title: 'Remote Servers',
      description: 'Manage external applications and API connections',
      icon: <Server className="h-6 w-6" />,
      component: <RemoteServersTab />
    },
    {
      id: 'user-management',
      title: 'User Management',
      description: 'Manage users, roles, and permissions',
      icon: <Users className="h-6 w-6" />,
      component: <UserManagementTab />
    },
    {
      id: 'security-audit',
      title: 'Security Audit',
      description: 'Monitor system activities and security events',
      icon: <Activity className="h-6 w-6" />,
      component: <SecurityAuditTab />
    },
    {
      id: 'system-settings',
      title: 'System Settings',
      description: 'Configure security settings and policies',
      icon: <Shield className="h-6 w-6" />,
      component: <SystemSettingsTab />
    },
    {
      id: 'api-keys',
      title: 'API Keys',
      description: 'Manage API keys and access tokens',
      icon: <Key className="h-6 w-6" />,
      component: (
        <div className="text-center py-12">
          <Key className="mx-auto h-12 w-12 text-gray-400" />
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">API Keys</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            API key management coming soon.
          </p>
        </div>
      )
    }
  ];

  const activeSection = settingsSections.find(section => section.id === activeTab) || settingsSections[0];

  return (
    <DashboardLayout>
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow">
        <div className="p-3 sm:p-4">
          <h1 className="text-xl font-bold text-gray-800 dark:text-white mb-4">System Settings</h1>

          <div className="flex flex-col lg:flex-row gap-4">
            {/* Sidebar Navigation */}
            <div className="w-100px">
              <nav className="space-y-1 bg-emerald-600 p-2 rounded-lg">
                {settingsSections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setActiveTab(section.id)}
                    className={`flex items-center p-2 rounded transition-colors duration-200 text-sm font-medium w-full text-left text-white ${activeTab === section.id
                      ? 'bg-emerald-700'
                      : 'hover:bg-emerald-700'
                      }`}
                  >
                    <div className="mr-3 text-white">
                      {section.icon}
                    </div>
                    {section.title}
                  </button>
                ))}
              </nav>
            </div>

            {/* Main Content */}
            <div className="flex-1">
              <Card>
                <CardContent className="p-4">
                  <h2 className="text-lg font-semibold mb-2 flex items-center">
                    <div className="mr-2 text-gray-500">
                      {activeSection.icon}
                    </div>
                    {activeSection.title}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400 mb-4 text-sm">
                    {activeSection.description}
                  </p>
                  {activeSection.component}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}