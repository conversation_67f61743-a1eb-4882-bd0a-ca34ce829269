
import { NextResponse } from 'next/server';

export async function GET() {
  const configuration = {
    issuer: 'http://localhost:3001',
    authorization_endpoint: 'http://localhost:3001/api/oauth/authorize',
    token_endpoint: 'http://localhost:3001/api/oauth/token',
    userinfo_endpoint: 'http://localhost:3001/api/oauth/userinfo',
    jwks_uri: 'http://localhost:3001/api/oauth/jwks',
    scopes_supported: ['read:profile'],
    response_types_supported: ['code'],
    token_endpoint_auth_methods_supported: ['client_secret_post'],
  };

  return NextResponse.json(configuration);
}
