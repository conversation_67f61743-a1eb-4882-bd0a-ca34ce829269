import LoginPage from "@/components/LoginPage";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";

export default async function LoginPageWrapper() {
  const session = await getServerSession(authOptions);

  // If the user is already logged in, redirect to dashboard
  if (session) {
    redirect("/dashboard");
  }

  // Show login page if user is not authenticated
  return <LoginPage />;
}