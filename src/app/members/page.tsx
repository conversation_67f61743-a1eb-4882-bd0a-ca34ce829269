'use client';

import React, { useState } from 'react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { CreateUserTab } from '@/components/members/CreateUserTab';
import { UpdateUserTab } from '@/components/members/UpdateUserTab';
import { SearchUserTab } from '@/components/members/SearchUserTab';
import { MemberTreatyTab } from '@/components/members/MemberTreatyTab';
import { Card, CardContent } from '@/components/ui/card';
import { UserPlus, UserCog, UserSearch, FileText, BookOpen } from 'lucide-react';

export default function MembersPage() {
  const [activeTab, setActiveTab] = useState<'create' | 'update' | 'search' | 'treaty'>('create');

  const memberSections = [
    {
      id: 'create',
      title: 'Create User',
      description: 'Add new members to the system',
      icon: <UserPlus className="h-5 w-5" />,
      component: <CreateUserTab />
    },
    {
      id: 'treaty',
      title: 'Member Treaty',
      description: 'Assign treaties and ordinances to members',
      icon: <FileText className="h-5 w-5" />,
      component: <MemberTreatyTab />
    },
    {
      id: 'update',
      title: 'Update User',
      description: 'Modify existing member information',
      icon: <UserCog className="h-5 w-5" />,
      component: <UpdateUserTab />
    },
    {
      id: 'search',
      title: 'Search User',
      description: 'Find and view member details',
      icon: <UserSearch className="h-5 w-5" />,
      component: <SearchUserTab />
    }
  ];

  const activeSection = memberSections.find(section => section.id === activeTab) || memberSections[0];

  return (
    <DashboardLayout>
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow">
        <div className="p-6">
          <h1 className="text-2xl font-bold text-slate-800 dark:text-slate-100 mb-6">Members Management</h1>
          
          <div className="flex flex-col lg:flex-row gap-6">
            {/* Sidebar Navigation */}
            <div className="w-full lg:w-64">
              <nav className="space-y-1 bg-slate-800 p-2 rounded-lg">
                {memberSections.map((section) => (
                  <button
                    key={section.id}
                    onClick={() => setActiveTab(section.id as 'create' | 'update' | 'search' | 'treaty')}
                    className={`flex items-center p-3 rounded transition-colors duration-200 text-sm font-medium w-full text-left text-white ${
                      activeTab === section.id
                        ? 'bg-slate-700'
                        : 'hover:bg-slate-700'
                    }`}
                  >
                    <div className="mr-3 text-white">
                      {section.icon}
                    </div>
                    {section.title}
                  </button>
                ))}
              </nav>
            </div>

            {/* Main Content */}
            <div className="flex-1">
              <Card className="bg-white dark:bg-slate-800">
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold mb-2 flex items-center text-slate-800 dark:text-slate-100">
                    <div className="mr-3 text-slate-600 dark:text-slate-400">
                      {activeSection.icon}
                    </div>
                    {activeSection.title}
                  </h2>
                  <p className="text-slate-600 dark:text-slate-400 mb-6 text-sm">
                    {activeSection.description}
                  </p>
                  {activeSection.component}
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}