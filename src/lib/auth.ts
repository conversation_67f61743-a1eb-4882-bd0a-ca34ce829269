import { NextAuthOptions } from "next-auth"
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials"
import { PrismaAdapter } from "@next-auth/prisma-adapter"
import { prisma } from "./prisma"
import bcrypt from "bcryptjs"

export const authOptions: NextAuthOptions = {
  adapter: PrismaAdapter(prisma),
  providers: [
    // {
    //   id: "member-portal",
    //   name: "Member Portal",
    //   type: "oauth",
    //   wellKnown: "http://localhost:3001/.well-known/openid-configuration",
    //   authorization: { params: { scope: "read:profile" } },
    //   clientId: "nwapromote-client-local",
    //   clientSecret: "da69150be2ae984ea232f144387add211844500d9d94de131ba78f9e2936fbff",
    //   checks: ["pkce", "state"],
    //   profile(profile) {
    //     return {
    //       id: profile.sub,
    //       name: profile.name,
    //       email: profile.email,
    //     }
    //   },
    //   token: {
    //     url: "http://localhost:3001/api/oauth/token",
    //   },
    //   userinfo: {
    //     url: "http://localhost:3001/api/oauth/userinfo",
    //   },
    // },
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email" },
        password: { label: "Password", type: "password" }
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        // For development, allow login with test credentials
        if (process.env.NODE_ENV === "development") {
          if (credentials.email === "<EMAIL>" && credentials.password === "password") {
            return {
              id: "cmeq62ild001uppyl068zjk6q",
              email: "<EMAIL>",
              name: "Test User",
              nwaEmail: "<EMAIL>"
            }
          }
        }

        try {
          // Find user in database with password hash
          const user = await prisma.user.findUnique({
            where: {
              email: credentials.email
            },
            include: {
              profile: true
            }
          })

          // If user doesn't exist or doesn't have a password hash
          if (!user || !user.passwordHash || !user.profile?.nwaEmail) {
            return null
          }

          // Check if the provided password matches the stored hash
          const isValid = await bcrypt.compare(credentials.password, user.passwordHash)
          
          if (!isValid) {
            return null
          }

          // Return user object without the password hash
          return {
            id: user.id,
            email: user.email,
            name: user.name,
            nwaEmail: user.profile.nwaEmail
          }
        } catch (error) {
          // If database is not available, fall back to development credentials
          if (process.env.NODE_ENV === "development") {
            if (credentials.email === "<EMAIL>" && credentials.password === "password") {
              return {
                id: "cmeq62ild001uppyl068zjk6q",
                email: "<EMAIL>",
                name: "Test User",
                nwaEmail: "<EMAIL>"
              }
            }
          }
          return null
        }
      }
    })
  ],
  session: {
    strategy: "jwt",
  },
  callbacks: {
    session: async ({ session, token }) => {
      if (session?.user && token) {
        (session.user as any).id = token.id;
        (session.user as any).nwaEmail = token.nwaEmail;
      }
      return session;
    },
    jwt: async ({ token, user }) => {
      if (user) {
        token.id = user.id;
        token.nwaEmail = (user as any).nwaEmail;
      }
      return token;
    }
  },
  pages: {
    signIn: '/login',
  },
}