import { prisma } from '../prisma';

export interface CorsValidationResult {
  isValid: boolean;
  matchedOrigin: string | null;
  reason?: string;
}

export class CorsValidationService {
  /**
   * Validates if an origin is allowed for a specific project
   */
  async validateOrigin(projectId: string, origin: string): Promise<CorsValidationResult> {
    try {
      if (!projectId) {
        throw new Error('Project ID is required');
      }

      if (!origin) {
        throw new Error('Origin is required');
      }

      if (!this.isValidOrigin(origin)) {
        throw new Error('Invalid origin format');
      }

      // Fetch project with allowed origins
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        select: {
          id: true,
          allowedOrigins: true,
          isActive: true,
        },
      });

      if (!project) {
        return {
          isValid: false,
          matchedOrigin: null,
          reason: 'Project not found',
        };
      }

      if (!project.isActive) {
        return {
          isValid: false,
          matchedOrigin: null,
          reason: 'Project is inactive',
        };
      }

      // Normalize the origin for comparison
      const normalizedOrigin = this.normalizeOrigin(origin);

      // Check for exact matches first
      for (const allowedOrigin of project.allowedOrigins) {
        const normalizedAllowed = this.normalizeOrigin(allowedOrigin);
        
        if (normalizedOrigin === normalizedAllowed) {
          return {
            isValid: true,
            matchedOrigin: allowedOrigin,
          };
        }
      }

      // Check for wildcard matches
      for (const allowedOrigin of project.allowedOrigins) {
        if (allowedOrigin.includes('*') && this.matchesWildcard(allowedOrigin, normalizedOrigin)) {
          return {
            isValid: true,
            matchedOrigin: allowedOrigin,
          };
        }
      }

      return {
        isValid: false,
        matchedOrigin: null,
        reason: 'Origin not in allowed list',
      };
    } catch (error: any) {
      throw new Error(`Failed to validate origin: ${error.message || error}`);
    }
  }

  /**
   * Validates if a string is a valid origin format
   */
  isValidOrigin(origin: string): boolean {
    if (!origin || typeof origin !== 'string') {
      return false;
    }

    try {
      const url = new URL(origin);
      
      // Must be HTTP or HTTPS
      if (!['http:', 'https:'].includes(url.protocol)) {
        return false;
      }

      // Must have a hostname
      if (!url.hostname) {
        return false;
      }

      // Should not have path, query, or fragment for CORS origins
      if (url.pathname !== '/' || url.search || url.hash) {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Checks if an origin matches a wildcard pattern
   * Supports patterns like https://*.example.com
   */
  matchesWildcard(pattern: string, origin: string): boolean {
    try {
      // Only support single wildcard at subdomain level
      if (!pattern.includes('*') || pattern.split('*').length !== 2) {
        return pattern === origin;
      }

      const patternUrl = new URL(pattern.replace('*', 'placeholder'));
      const originUrl = new URL(origin);

      // Protocol must match
      if (patternUrl.protocol !== originUrl.protocol) {
        return false;
      }

      // Port must match
      if (patternUrl.port !== originUrl.port) {
        return false;
      }

      // Extract the pattern parts
      const [beforeWildcard, afterWildcard] = pattern.split('*');
      
      // Check if origin starts with the before part and ends with the after part
      if (!origin.startsWith(beforeWildcard) || !origin.endsWith(afterWildcard)) {
        return false;
      }

      // Extract the wildcard part
      const wildcardPart = origin.slice(beforeWildcard.length, origin.length - afterWildcard.length);
      
      // Wildcard part should not be empty (must have at least one subdomain)
      if (!wildcardPart) {
        return false;
      }

      // Wildcard part should not contain protocol separators or additional dots at boundaries
      if (wildcardPart.includes('://') || wildcardPart.startsWith('.') || wildcardPart.endsWith('.')) {
        return false;
      }

      // Ensure it's a valid subdomain pattern (no multiple consecutive dots)
      if (wildcardPart.includes('..')) {
        return false;
      }

      return true;
    } catch {
      return false;
    }
  }

  /**
   * Normalizes an origin for consistent comparison
   */
  normalizeOrigin(origin: string): string {
    if (!this.isValidOrigin(origin)) {
      throw new Error('Invalid origin format');
    }

    try {
      const url = new URL(origin);
      
      // Convert hostname to lowercase
      url.hostname = url.hostname.toLowerCase();
      
      // Remove default ports
      if ((url.protocol === 'https:' && url.port === '443') ||
          (url.protocol === 'http:' && url.port === '80')) {
        url.port = '';
      }

      // Remove trailing slash
      url.pathname = '';
      
      return url.toString().replace(/\/$/, '');
    } catch (error: any) {
      throw new Error(`Failed to normalize origin: ${error.message || error}`);
    }
  }

  /**
   * Gets all allowed origins for a project
   */
  async getAllowedOrigins(projectId: string): Promise<string[]> {
    try {
      const project = await prisma.project.findUnique({
        where: { id: projectId },
        select: {
          allowedOrigins: true,
          isActive: true,
        },
      });

      if (!project || !project.isActive) {
        return [];
      }

      return project.allowedOrigins;
    } catch (error: any) {
      throw new Error(`Failed to get allowed origins: ${error.message || error}`);
    }
  }

  /**
   * Validates multiple origins at once
   */
  async validateMultipleOrigins(
    projectId: string,
    origins: string[]
  ): Promise<{ [origin: string]: CorsValidationResult }> {
    const results: { [origin: string]: CorsValidationResult } = {};

    for (const origin of origins) {
      try {
        results[origin] = await this.validateOrigin(projectId, origin);
      } catch (error: any) {
        results[origin] = {
          isValid: false,
          matchedOrigin: null,
          reason: error.message || 'Unknown error',
        };
      }
    }

    return results;
  }
}