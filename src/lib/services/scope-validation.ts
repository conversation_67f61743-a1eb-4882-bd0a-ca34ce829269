import { prisma } from '../prisma';

export interface ScopePermission {
  action: string;
  resource: string;
  isWildcard: boolean;
}

export interface ScopeValidationResult {
  isValid: boolean;
  grantedScopes: string[];
  missingScopes: string[];
}

export class ScopeValidationService {
  /**
   * Validates that a user has the required scopes for a specific project
   */
  async validateUserScopes(
    userId: string,
    projectId: string,
    requiredScopes: string[]
  ): Promise<ScopeValidationResult> {
    try {
      if (!userId) {
        throw new Error('User ID is required');
      }

      if (!projectId) {
        throw new Error('Project ID is required');
      }

      // Handle empty required scopes
      if (requiredScopes.length === 0) {
        return {
          isValid: true,
          grantedScopes: [],
          missingScopes: [],
        };
      }

      // Fetch user's granted scopes for the project
      const userProjectScopes = await prisma.userProjectScope.findMany({
        where: {
          userId,
          projectId,
          scope: {
            isActive: true,
          },
        },
        include: {
          scope: true,
        },
      });

      const grantedScopeNames = userProjectScopes.map(ups => ups.scope.name);
      
      // Check which required scopes are granted
      const grantedScopes: string[] = [];
      const missingScopes: string[] = [];

      for (const requiredScope of requiredScopes) {
        if (this.hasPermission(grantedScopeNames, requiredScope)) {
          grantedScopes.push(requiredScope);
        } else {
          missingScopes.push(requiredScope);
        }
      }

      return {
        isValid: missingScopes.length === 0,
        grantedScopes,
        missingScopes,
      };
    } catch (error: any) {
      throw new Error(`Failed to validate user scopes: ${error.message || error}`);
    }
  }

  /**
   * Validates service-level scopes for a project (no user-specific validation)
   */
  async validateServiceScopes(
    projectId: string,
    requiredScopes: string[]
  ): Promise<ScopeValidationResult> {
    try {
      if (!projectId) {
        throw new Error('Project ID is required');
      }

      // Handle empty required scopes
      if (requiredScopes.length === 0) {
        return {
          isValid: true,
          grantedScopes: [],
          missingScopes: [],
        };
      }

      const grantedScopes: string[] = [];
      const missingScopes: string[] = [];

      // Service scopes validation logic
      for (const scope of requiredScopes) {
        if (this.isValidServiceScope(scope)) {
          grantedScopes.push(scope);
        } else {
          missingScopes.push(scope);
        }
      }

      return {
        isValid: missingScopes.length === 0,
        grantedScopes,
        missingScopes,
      };
    } catch (error: any) {
      throw new Error(`Failed to validate service scopes: ${error.message || error}`);
    }
  }

  /**
   * Checks if a list of scopes includes a specific permission
   * Supports wildcard matching (e.g., admin:* matches admin:users)
   */
  hasPermission(grantedScopes: string[], requiredPermission: string): boolean {
    // Direct match
    if (grantedScopes.includes(requiredPermission)) {
      return true;
    }

    // Check for wildcard matches
    const requiredParsed = this.parseScope(requiredPermission);
    
    for (const grantedScope of grantedScopes) {
      try {
        const grantedParsed = this.parseScope(grantedScope);
        
        // Check for action wildcard (e.g., admin:* matches admin:users)
        if (grantedParsed.resource === '*' && grantedParsed.action === requiredParsed.action) {
          return true;
        }
        
        // Check for resource wildcard (e.g., users:* matches users:read)
        if (grantedParsed.resource === '*' && grantedParsed.action === requiredParsed.resource) {
          return true;
        }
      } catch {
        // Skip invalid scope formats
        continue;
      }
    }

    return false;
  }

  /**
   * Parses a scope string into its components
   */
  parseScope(scope: string): ScopePermission {
    if (!scope || typeof scope !== 'string') {
      throw new Error('Scope must be a non-empty string');
    }

    const parts = scope.split(':');
    if (parts.length !== 2) {
      throw new Error('Scope must be in format "action:resource"');
    }

    const [action, resource] = parts;
    
    if (!action || !resource) {
      throw new Error('Both action and resource must be non-empty');
    }

    return {
      action,
      resource,
      isWildcard: resource === '*',
    };
  }

  /**
   * Gets all available scopes for a specific resource category
   */
  async getScopeHierarchy(category: string): Promise<Array<{ id: string; name: string; description: string | null }>> {
    try {
      const scopes = await prisma.scope.findMany({
        where: {
          category,
          isActive: true,
        },
        select: {
          id: true,
          name: true,
          description: true,
        },
        orderBy: {
          name: 'asc',
        },
      });

      return scopes;
    } catch (error: any) {
      throw new Error(`Failed to get scope hierarchy: ${error.message || error}`);
    }
  }

  /**
   * Validates the format of a scope string
   */
  validateScopeFormat(scope: string): boolean {
    try {
      this.parseScope(scope);
      
      // Additional format validation
      const validPattern = /^[a-zA-Z0-9_-]+:[a-zA-Z0-9_*-]+$/;
      return validPattern.test(scope);
    } catch {
      return false;
    }
  }

  /**
   * Checks if a scope is valid for service-level access
   */
  private isValidServiceScope(scope: string): boolean {
    if (!this.validateScopeFormat(scope)) {
      return false;
    }

    try {
      const parsed = this.parseScope(scope);
      
      // Service scopes should be limited to specific patterns
      const validServiceActions = ['service', 'api', 'system'];
      const validServiceResources = ['access', 'read', 'write', '*'];
      
      // Don't allow user-specific scopes for service authentication
      const userSpecificResources = ['users', 'profiles', 'positions'];
      
      if (userSpecificResources.includes(parsed.resource)) {
        return false;
      }

      return (
        validServiceActions.includes(parsed.action) ||
        validServiceResources.includes(parsed.resource)
      );
    } catch {
      return false;
    }
  }
}