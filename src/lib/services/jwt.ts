import jwt from 'jsonwebtoken';

export interface JwtPayload {
  userId?: string;
  projectId: string;
  scopes: string[];
}

export interface DecodedJwtPayload extends JwtPayload {
  iss: string;
  aud: string;
  iat: number;
  exp: number;
}

export class JwtService {
  private readonly privateKey: string;
  private readonly publicKey: string;
  private readonly issuer: string;
  private readonly audience: string;
  private readonly algorithm = 'RS256';
  private readonly defaultExpiresIn = '1h';

  constructor() {
    const privateKey = process.env.JWT_PRIVATE_KEY;
    const publicKey = process.env.JWT_PUBLIC_KEY;
    
    // During build time, environment variables may not be available
    // We'll defer validation until the keys are actually used
    this.privateKey = privateKey || '';
    this.publicKey = publicKey || '';
    this.issuer = process.env.JWT_ISSUER || 'nwa-api';
    this.audience = process.env.JWT_AUDIENCE || 'nwa-external';

    // Validate keys only if they're provided (deferred validation)
    if (privateKey && publicKey) {
      this.validateKeys();
    }
  }

  /**
   * Signs a JWT token with the provided payload
   */
  async signToken(payload: JwtPayload, expiresIn: string = this.defaultExpiresIn): Promise<string> {
    // Check if private key is available
    if (!this.privateKey) {
      throw new Error('JWT_PRIVATE_KEY environment variable is required for signing tokens');
    }

    try {
      const tokenPayload = {
        ...payload,
        iss: this.issuer,
        aud: this.audience,
      };

      // Using any to bypass TypeScript issues with the jsonwebtoken types
      return (jwt.sign as any)(tokenPayload, this.privateKey, {
        algorithm: this.algorithm,
        expiresIn,
      });
    } catch (error: any) {
      throw new Error(`Failed to sign JWT token: ${error.message}`);
    }
  }

  /**
   * Verifies and decodes a JWT token
   */
  async verifyToken(token: string): Promise<DecodedJwtPayload> {
    // Check if public key is available
    if (!this.publicKey) {
      throw new Error('JWT_PUBLIC_KEY environment variable is required for verifying tokens');
    }

    try {
      // Using any to bypass TypeScript issues with the jsonwebtoken types
      const decoded = (jwt.verify as any)(token, this.publicKey, {
        algorithms: [this.algorithm],
        issuer: this.issuer,
        audience: this.audience,
      }) as DecodedJwtPayload;

      // Validate required fields
      this.validateTokenPayload(decoded);

      return decoded;
    } catch (error: any) {
      if (error instanceof jwt.JsonWebTokenError) {
        throw new Error(`Invalid JWT token: ${error.message}`);
      } else if (error instanceof jwt.TokenExpiredError) {
        throw new Error('JWT token has expired');
      } else if (error instanceof jwt.NotBeforeError) {
        throw new Error('JWT token is not active yet');
      } else {
        throw new Error(`Failed to verify JWT token: ${error.message}`);
      }
    }
  }

  /**
   * Checks if a token is expired without full verification
   */
  async isTokenExpired(token: string): Promise<boolean> {
    try {
      await this.verifyToken(token);
      return false;
    } catch (error: any) {
      if (error.message.includes('expired')) {
        return true;
      }
      // Re-throw other errors (invalid signature, malformed token, etc.)
      throw error;
    }
  }

  /**
   * Extracts payload from token without verification (for debugging/logging)
   * WARNING: This does not verify the token signature!
   */
  extractPayload(token: string): DecodedJwtPayload {
    try {
      const decoded = jwt.decode(token) as DecodedJwtPayload;
      
      if (!decoded || typeof decoded !== 'object') {
        throw new Error('Invalid token format');
      }

      return decoded;
    } catch (error: any) {
      throw new Error(`Failed to extract token payload: ${error.message}`);
    }
  }

  /**
   * Validates the private and public keys
   */
  private validateKeys(): void {
    // Skip validation if keys are not provided (during build time)
    if (!this.privateKey || !this.publicKey) {
      return;
    }

    try {
      const testPayload = {
        projectId: 'test',
        scopes: ['test'],
        iss: this.issuer,
        aud: this.audience,
      };

      const testToken = (jwt.sign as any)(testPayload, this.privateKey, {
        algorithm: this.algorithm,
        expiresIn: '1m',
      });

      (jwt.verify as any)(testToken, this.publicKey, {
        algorithms: [this.algorithm],
      });
    } catch (error: any) {
      throw new Error(`Invalid JWT keys: ${error.message}`);
    }
  }

  /**
   * Validates the structure of a decoded token payload
   */
  private validateTokenPayload(payload: any): void {
    if (!payload || typeof payload !== 'object') {
      throw new Error('Invalid token payload structure');
    }

    if (!payload.projectId || typeof payload.projectId !== 'string') {
      throw new Error('Token must contain a valid projectId');
    }

    if (!Array.isArray(payload.scopes)) {
      throw new Error('Token must contain a valid scopes array');
    }

    if (payload.scopes.some((scope: any) => typeof scope !== 'string')) {
      throw new Error('All scopes must be strings');
    }

    if (payload.userId && typeof payload.userId !== 'string') {
      throw new Error('userId must be a string if provided');
    }
  }
}