import { createHash, randomBytes, timingSafeEqual } from 'crypto';

export class ApiKeyService {
  private readonly API_KEY_PREFIX = 'nwa_';
  private readonly API_KEY_LENGTH = 32; // Length of the random part
  private readonly HASH_ALGORITHM = 'sha256';

  /**
   * Generates a new API key with the format: nwa_<random_string>
   */
  generateApiKey(): string {
    const randomPart = randomBytes(this.API_KEY_LENGTH)
      .toString('base64url') // URL-safe base64 encoding
      .slice(0, this.API_KEY_LENGTH);
    
    return `${this.API_KEY_PREFIX}${randomPart}`;
  }

  /**
   * Hashes an API key using SHA-256
   */
  hashApiKey(apiKey: string): string {
    if (!apiKey || typeof apiKey !== 'string') {
      throw new Error('API key must be a non-empty string');
    }

    return createHash(this.HASH_ALGORITHM)
      .update(apiKey)
      .digest('hex');
  }

  /**
   * Validates an API key against its stored hash using timing-safe comparison
   */
  async validateApiKey(apiKey: string, storedHash: string): Promise<boolean> {
    try {
      // First check if the API key format is valid
      if (!this.isValidApiKeyFormat(apiKey)) {
        return false;
      }

      // Hash the provided API key
      const providedHash = this.hashApiKey(apiKey);
      
      // Use timing-safe comparison to prevent timing attacks
      const providedBuffer = Buffer.from(providedHash, 'hex');
      const storedBuffer = Buffer.from(storedHash, 'hex');
      
      // Ensure both buffers are the same length for timing-safe comparison
      if (providedBuffer.length !== storedBuffer.length) {
        return false;
      }

      return timingSafeEqual(providedBuffer, storedBuffer);
    } catch (error) {
      // Log error in production, but don't expose details
      console.error('API key validation error:', error);
      return false;
    }
  }

  /**
   * Validates the format of an API key
   */
  isValidApiKeyFormat(apiKey: string): boolean {
    if (!apiKey || typeof apiKey !== 'string') {
      return false;
    }

    // Check if it starts with the correct prefix
    if (!apiKey.startsWith(this.API_KEY_PREFIX)) {
      return false;
    }

    // Check if it has content after the prefix
    const keyPart = apiKey.slice(this.API_KEY_PREFIX.length);
    if (keyPart.length === 0) {
      return false;
    }

    // Validate characters: alphanumeric, underscores, and hyphens only
    const validPattern = /^[a-zA-Z0-9_-]+$/;
    return validPattern.test(keyPart);
  }
}