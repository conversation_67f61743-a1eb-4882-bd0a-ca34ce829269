import { Client } from 'minio';
import { v4 as uuidv4 } from 'uuid';

export class FileStorageService {
  private client: Client;
  private bucketName: string;

  constructor() {
    const endpoint = process.env.MINIO_ENDPOINT || 'localhost';
    const port = parseInt(process.env.MINIO_PORT || '9000', 10);
    const accessKey = process.env.MINIO_ACCESS_KEY || 'minioadmin';
    const secretKey = process.env.MINIO_SECRET_KEY || 'minioadmin123';
    this.bucketName = process.env.MINIO_BUCKET_NAME || 'nwa-uploads';

    this.client = new Client({
      endPoint: endpoint,
      port: port,
      useSSL: process.env.MINIO_USE_SSL === 'true',
      accessKey: accessKey,
      secretKey: secretKey,
    });

    // Ensure bucket exists
    this.ensureBucketExists();
  }

  /**
   * Ensure the bucket exists, creating it if necessary
   */
  private async ensureBucketExists(): Promise<void> {
    try {
      const exists = await this.client.bucketExists(this.bucketName);
      if (!exists) {
        await this.client.makeBucket(this.bucketName, 'us-east-1');
      }
    } catch (error) {
      console.error('Error ensuring bucket exists:', error);
      throw new Error('Failed to ensure file storage bucket exists');
    }
  }

  /**
   * Upload a file to MinIO
   */
  async uploadFile(fileBuffer: Buffer, originalName: string, contentType: string): Promise<string> {
    try {
      // Generate a unique filename
      const extension = originalName.split('.').pop() || '';
      const uniqueName = `${uuidv4()}.${extension}`;
      
      // Upload the file
      await this.client.putObject(
        this.bucketName,
        uniqueName,
        fileBuffer,
        fileBuffer.length,
        { 'Content-Type': contentType }
      );
      
      // Return the public URL
      return `/api/files/${uniqueName}`;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw new Error('Failed to upload file');
    }
  }

  /**
   * Get a presigned URL for a file
   */
  async getPresignedUrl(objectName: string): Promise<string> {
    try {
      return await this.client.presignedGetObject(this.bucketName, objectName, 24 * 60 * 60); // 24 hours
    } catch (error) {
      console.error('Error generating presigned URL:', error);
      throw new Error('Failed to generate file access URL');
    }
  }

  /**
   * Delete a file from MinIO
   */
  async deleteFile(objectName: string): Promise<void> {
    try {
      await this.client.removeObject(this.bucketName, objectName);
    } catch (error) {
      console.error('Error deleting file:', error);
      throw new Error('Failed to delete file');
    }
  }
}