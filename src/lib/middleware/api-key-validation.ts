import { NextRequest, NextResponse } from 'next/server';
import { ApiKeyService } from '../services/api-key';
import { prisma } from '../prisma';

export interface ApiKeyValidationResult {
  isValid: boolean;
  project?: {
    id: string;
    name: string;
    allowedOrigins: string[];
    isActive: boolean;
  };
  error?: string;
}

export class ApiKeyValidationMiddleware {
  private apiKeyService: ApiKeyService;

  constructor() {
    this.apiKeyService = new ApiKeyService();
  }

  /**
   * Validates API key from request headers and returns project information
   */
  async validateApiKey(request: NextRequest): Promise<ApiKeyValidationResult> {
    try {
      // Extract API key from headers
      const apiKey = this.extractApiKey(request);
      
      if (!apiKey) {
        return {
          isValid: false,
          error: 'API key is required',
        };
      }

      // Validate API key format
      if (!this.apiKeyService.isValidApiKeyFormat(apiKey)) {
        return {
          isValid: false,
          error: 'Invalid API key format',
        };
      }

      // Find project by API key hash
      const project = await this.findProjectByApiKey(apiKey);
      
      if (!project) {
        return {
          isValid: false,
          error: 'Invalid API key',
        };
      }

      if (!project.isActive) {
        return {
          isValid: false,
          error: 'Project is inactive',
        };
      }

      // Validate API key against stored hash
      const isValidKey = await this.apiKeyService.validateApiKey(apiKey, project.apiKeyHash);
      
      if (!isValidKey) {
        return {
          isValid: false,
          error: 'Invalid API key',
        };
      }

      return {
        isValid: true,
        project: {
          id: project.id,
          name: project.name,
          allowedOrigins: project.allowedOrigins,
          isActive: project.isActive,
        },
      };
    } catch (error) {
      console.error('API key validation error:', error);
      return {
        isValid: false,
        error: 'Authentication failed',
      };
    }
  }

  /**
   * Middleware function for Next.js API routes
   */
  async middleware(request: NextRequest): Promise<NextResponse | null> {
    const result = await this.validateApiKey(request);
    
    if (!result.isValid) {
      return NextResponse.json(
        {
          error: 'Unauthorized',
          message: result.error,
        },
        { status: 401 }
      );
    }

    // Add project information to request headers for downstream middleware
    const response = NextResponse.next();
    response.headers.set('x-project-id', result.project!.id);
    response.headers.set('x-project-name', result.project!.name);
    response.headers.set('x-allowed-origins', JSON.stringify(result.project!.allowedOrigins));
    
    return response;
  }

  /**
   * Creates a middleware function with custom error handling
   */
  createMiddleware(options?: {
    onError?: (error: string, request: NextRequest) => NextResponse;
    onSuccess?: (project: any, request: NextRequest) => void;
  }) {
    return async (request: NextRequest): Promise<NextResponse | null> => {
      const result = await this.validateApiKey(request);
      
      if (!result.isValid) {
        if (options?.onError) {
          return options.onError(result.error!, request);
        }
        
        return NextResponse.json(
          {
            error: 'Unauthorized',
            message: result.error,
          },
          { status: 401 }
        );
      }

      if (options?.onSuccess) {
        options.onSuccess(result.project, request);
      }

      // Continue to next middleware
      return null;
    };
  }

  /**
   * Extracts API key from request headers
   */
  private extractApiKey(request: NextRequest): string | null {
    // Check x-api-key header (preferred)
    const apiKeyHeader = request.headers.get('x-api-key');
    if (apiKeyHeader) {
      return apiKeyHeader.trim();
    }

    // Check authorization header as fallback (API-Key scheme)
    const authHeader = request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('API-Key ')) {
      return authHeader.slice(8).trim(); // Remove 'API-Key ' prefix
    }

    return null;
  }

  /**
   * Finds project by API key hash
   */
  private async findProjectByApiKey(apiKey: string) {
    try {
      const apiKeyHash = this.apiKeyService.hashApiKey(apiKey);
      
      const project = await prisma.project.findFirst({
        where: {
          apiKeyHash,
        },
        select: {
          id: true,
          name: true,
          apiKeyHash: true,
          allowedOrigins: true,
          isActive: true,
        },
      });

      return project;
    } catch (error) {
      console.error('Error finding project by API key:', error);
      return null;
    }
  }

  /**
   * Validates API key for a specific project (utility method)
   */
  async validateApiKeyForProject(apiKey: string, projectId: string): Promise<boolean> {
    try {
      if (!this.apiKeyService.isValidApiKeyFormat(apiKey)) {
        return false;
      }

      const project = await prisma.project.findUnique({
        where: { id: projectId },
        select: {
          apiKeyHash: true,
          isActive: true,
        },
      });

      if (!project || !project.isActive) {
        return false;
      }

      return await this.apiKeyService.validateApiKey(apiKey, project.apiKeyHash);
    } catch (error) {
      console.error('Error validating API key for project:', error);
      return false;
    }
  }

  /**
   * Gets project information from validated API key
   */
  async getProjectFromApiKey(apiKey: string) {
    const result = await this.validateApiKey({
      headers: new Headers({ 'x-api-key': apiKey }),
    } as NextRequest);

    return result.isValid ? result.project : null;
  }
}

// Export singleton instance
export const apiKeyValidationMiddleware = new ApiKeyValidationMiddleware();

// Export utility functions
export const validateApiKey = (request: NextRequest) => 
  apiKeyValidationMiddleware.validateApiKey(request);

export const createApiKeyMiddleware = (options?: Parameters<ApiKeyValidationMiddleware['createMiddleware']>[0]) =>
  apiKeyValidationMiddleware.createMiddleware(options);