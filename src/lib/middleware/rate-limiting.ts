import { NextRequest, NextResponse } from 'next/server';
import { RateLimiterRedis } from 'rate-limiter-flexible';
import Redis from 'ioredis';

// Add extensive debugging to understand environment variable loading
console.log('=== RATE LIMITING MIDDLEWARE INITIALIZATION ===');
console.log('Process environment check:');
console.log('- NODE_ENV:', process.env.NODE_ENV);
console.log('- REDIS_URL:', process.env.REDIS_URL);
console.log('- Has REDIS_URL:', !!process.env.REDIS_URL);
console.log('- Type of REDIS_URL:', typeof process.env.REDIS_URL);

// Check if we're in standalone mode
console.log('- Working directory:', process.cwd());
console.log('- Process argv:', process.argv);

// List all environment variables that might be relevant
const relevantEnvVars = Object.keys(process.env).filter(key => 
  key.includes('REDIS') || key.includes('NODE') || key.includes('ENV')
).reduce((obj, key) => {
  obj[key] = process.env[key];
  return obj;
}, {} as Record<string, string | undefined>);

console.log('- Relevant environment variables:', relevantEnvVars);

// Create Redis client with connection options
const redisUrl = process.env.REDIS_URL || 'redis://localhost:6379';
console.log('Using Redis URL:', redisUrl);

const redisClient = new Redis(redisUrl, {
  connectTimeout: 30000, // 30 second connection timeout
  lazyConnect: true, // Don't connect immediately
});

// Add connection event listeners with more debugging
redisClient.on('connect', () => {
  console.log('Successfully connected to Redis at:', redisUrl);
});

redisClient.on('error', (err) => {
  console.error('Redis connection error for URL:', redisUrl);
  console.error('Full error details:', err);
});

// Handle Redis connection errors
redisClient.on('error', (err) => {
  console.error('Redis connection error:', err);
});

redisClient.on('connect', () => {
  console.log('Connected to Redis successfully');
});

interface RateLimitConfig {
  windowMs?: number;
  maxRequests?: number;
  keyGenerator?: (req: NextRequest) => string;
}

interface RateLimitResult {
  allowed: boolean;
  limit: number;
  remaining: number;
  resetTime: number;
  retryAfter: number;
  headers?: Headers;
}

/**
 * Create rate limiting middleware for API routes
 */
export function createRateLimitingMiddleware(config: RateLimitConfig = {}) {
  const { windowMs = 60 * 1000, maxRequests = 100, keyGenerator } = config;
  
  // Create rate limiter instance with error handling
  let limiter: RateLimiterRedis | null = null;
  try {
    limiter = new RateLimiterRedis({
      storeClient: redisClient,
      keyPrefix: 'middleware',
      points: maxRequests,
      duration: Math.floor(windowMs / 1000),
    });
  } catch (error) {
    console.error('Failed to create rate limiter:', error);
    limiter = null;
  }
  
  return async function rateLimitingMiddleware(req: NextRequest) {
    // If limiter is not available, allow the request
    if (!limiter) {
      console.log('Rate limiter not available, allowing request');
      return {
        allowed: true,
        limit: maxRequests,
        remaining: maxRequests,
        resetTime: Date.now() + windowMs,
        retryAfter: 0,
      };
    }
    
    try {
      // Generate key for rate limiting
      const key = keyGenerator 
        ? keyGenerator(req) 
        : `${req.headers.get('x-forwarded-for') || req.headers.get('x-real-ip') || 'unknown'}:${req.url}`;
      
      try {
        const rateLimitRes = await limiter.consume(key);
        
        // Add rate limit headers to response
        const headers = new Headers();
        headers.set('X-RateLimit-Limit', maxRequests.toString());
        headers.set('X-RateLimit-Remaining', rateLimitRes.remainingPoints.toString());
        headers.set('X-RateLimit-Reset', new Date(Date.now() + rateLimitRes.msBeforeNext).toISOString());
        
        return {
          allowed: true,
          limit: maxRequests,
          remaining: rateLimitRes.remainingPoints,
          resetTime: Date.now() + rateLimitRes.msBeforeNext,
          retryAfter: 0,
          headers,
        };
      } catch (rateLimiterRes: any) {
        if (rateLimiterRes instanceof Error) {
          console.error('Rate limiter error:', rateLimiterRes);
          // Allow request if rate limiter fails
          return {
            allowed: true,
            limit: maxRequests,
            remaining: maxRequests,
            resetTime: Date.now() + windowMs,
            retryAfter: 0,
          };
        }
        
        // Rate limit exceeded
        const headers = new Headers();
        headers.set('X-RateLimit-Limit', maxRequests.toString());
        headers.set('X-RateLimit-Remaining', '0');
        headers.set('X-RateLimit-Reset', new Date(Date.now() + rateLimiterRes.msBeforeNext).toISOString());
        headers.set('Retry-After', Math.floor(rateLimiterRes.msBeforeNext / 1000).toString());
        
        return { 
          allowed: false,
          limit: maxRequests,
          remaining: 0,
          resetTime: Date.now() + rateLimiterRes.msBeforeNext,
          retryAfter: Math.floor(rateLimiterRes.msBeforeNext / 1000),
          headers,
        };
      }
    } catch (error) {
      console.error('Rate limiting error:', error);
      // Allow request if rate limiting fails
      return {
        allowed: true,
        limit: maxRequests,
        remaining: maxRequests,
        resetTime: Date.now() + windowMs,
        retryAfter: 0,
      };
    }
  };
}

/**
 * Check rate limit for a request
 */
export async function checkRateLimit(
  req: NextRequest, 
  config: RateLimitConfig = {}
): Promise<RateLimitResult> {
  try {
    const rateLimiterMiddleware = createRateLimitingMiddleware(config);
    const result = await rateLimiterMiddleware(req);
    return result;
  } catch (error) {
    console.error('Rate limiting check error:', error);
    // Allow request if rate limiting check fails
    const { windowMs = 60 * 1000, maxRequests = 100 } = config;
    return {
      allowed: true,
      limit: maxRequests,
      remaining: maxRequests,
      resetTime: Date.now() + windowMs,
      retryAfter: 0,
    };
  }
}

/**
 * Predefined rate limiters for common use cases
 */
export const predefinedRateLimiters = {
  // Strict rate limiting for authentication endpoints
  auth: createRateLimitingMiddleware({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 10, // 10 requests per minute
  }),
  
  // Standard rate limiting for API endpoints
  api: createRateLimitingMiddleware({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 100, // 100 requests per minute
  }),
  
  // Very strict rate limiting for sensitive endpoints
  strict: createRateLimitingMiddleware({
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5, // 5 requests per minute
  }),
  
  // Rate limiting for signup endpoints
  signup: createRateLimitingMiddleware({
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 5, // 5 requests per 5 minutes
  }),
  
  // Rate limiting for password reset endpoints
  passwordReset: createRateLimitingMiddleware({
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3, // 3 requests per hour
  }),
};