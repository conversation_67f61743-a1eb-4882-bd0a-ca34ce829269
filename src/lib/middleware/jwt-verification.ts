import { NextRequest, NextResponse } from 'next/server';
import { JwtService, DecodedJwtPayload } from '../services/jwt';

export interface JwtVerificationResult {
  isValid: boolean;
  payload?: DecodedJwtPayload;
  error?: string;
}

export class JwtVerificationMiddleware {
  private jwtService: JwtService;

  constructor() {
    this.jwtService = new JwtService();
  }

  /**
   * Verifies JWT token from request headers
   */
  async verifyToken(request: NextRequest): Promise<JwtVerificationResult> {
    try {
      // Extract JWT token from headers
      const token = this.extractToken(request);
      
      if (!token) {
        return {
          isValid: false,
          error: 'JWT token is required',
        };
      }

      // Verify and decode the token
      const payload = await this.jwtService.verifyToken(token);
      
      return {
        isValid: true,
        payload,
      };
    } catch (error: any) {
      console.error('JWT verification error:', error);
      
      // Return specific error messages for different JWT errors
      let errorMessage = 'Invalid token';
      
      if (error.message && error.message.includes('expired')) {
        errorMessage = 'Token has expired';
      } else if (error.message && error.message.includes('not active')) {
        errorMessage = 'Token is not active yet';
      } else if (error.message && error.message.includes('Invalid JWT token')) {
        errorMessage = 'Invalid token format';
      }
      
      return {
        isValid: false,
        error: errorMessage,
      };
    }
  }

  /**
   * Middleware function for Next.js API routes
   */
  async middleware(request: NextRequest): Promise<NextResponse | null> {
    const result = await this.verifyToken(request);
    
    if (!result.isValid) {
      return NextResponse.json(
        {
          error: 'Unauthorized',
          message: result.error,
        },
        { status: 401 }
      );
    }

    // Add token payload to request headers for downstream middleware
    const response = NextResponse.next();
    response.headers.set('x-user-id', result.payload!.userId || '');
    response.headers.set('x-project-id', result.payload!.projectId);
    response.headers.set('x-scopes', JSON.stringify(result.payload!.scopes));
    response.headers.set('x-token-iat', result.payload!.iat.toString());
    response.headers.set('x-token-exp', result.payload!.exp.toString());
    
    return response;
  }

  /**
   * Creates a middleware function with custom options
   */
  createMiddleware(options?: {
    optional?: boolean; // Allow requests without JWT tokens
    onError?: (error: string, request: NextRequest) => NextResponse;
    onSuccess?: (payload: DecodedJwtPayload, request: NextRequest) => void;
    validatePayload?: (payload: DecodedJwtPayload) => boolean;
  }) {
    return async (request: NextRequest): Promise<NextResponse | null> => {
      const result = await this.verifyToken(request);
      
      if (!result.isValid) {
        // If token is optional and missing, continue
        if (options?.optional && result.error === 'JWT token is required') {
          return null;
        }
        
        if (options?.onError) {
          return options.onError(result.error!, request);
        }
        
        return NextResponse.json(
          {
            error: 'Unauthorized',
            message: result.error,
          },
          { status: 401 }
        );
      }

      // Additional payload validation
      if (options?.validatePayload && !options.validatePayload(result.payload!)) {
        const error = 'Token payload validation failed';
        
        if (options.onError) {
          return options.onError(error, request);
        }
        
        return NextResponse.json(
          {
            error: 'Unauthorized',
            message: error,
          },
          { status: 401 }
        );
      }

      if (options?.onSuccess) {
        options.onSuccess(result.payload!, request);
      }

      // Continue to next middleware
      return null;
    };
  }

  /**
   * Verifies token and validates project match
   */
  async verifyTokenForProject(request: NextRequest, expectedProjectId: string): Promise<JwtVerificationResult> {
    const result = await this.verifyToken(request);
    
    if (!result.isValid) {
      return result;
    }

    if (result.payload!.projectId !== expectedProjectId) {
      return {
        isValid: false,
        error: 'Token project mismatch',
      };
    }

    return result;
  }

  /**
   * Checks if token is expired without full verification
   */
  async isTokenExpired(request: NextRequest): Promise<boolean> {
    try {
      const token = this.extractToken(request);
      
      if (!token) {
        return true; // Consider missing token as expired
      }

      return await this.jwtService.isTokenExpired(token);
    } catch (error: any) {
      return true; // Consider invalid tokens as expired
    }
  }

  /**
   * Extracts payload without verification (for debugging/logging)
   */
  extractPayloadUnsafe(request: NextRequest): DecodedJwtPayload | null {
    try {
      const token = this.extractToken(request);
      
      if (!token) {
        return null;
      }

      return this.jwtService.extractPayload(token);
    } catch (error: any) {
      return null;
    }
  }

  /**
   * Validates token scopes against required scopes
   */
  async validateTokenScopes(
    request: NextRequest, 
    requiredScopes: string[]
  ): Promise<{ isValid: boolean; grantedScopes: string[]; missingScopes: string[] }> {
    const result = await this.verifyToken(request);
    
    if (!result.isValid) {
      return {
        isValid: false,
        grantedScopes: [],
        missingScopes: requiredScopes,
      };
    }

    const tokenScopes = result.payload!.scopes;
    const grantedScopes: string[] = [];
    const missingScopes: string[] = [];

    for (const requiredScope of requiredScopes) {
      if (tokenScopes.includes(requiredScope)) {
        grantedScopes.push(requiredScope);
      } else {
        missingScopes.push(requiredScope);
      }
    }

    return {
      isValid: missingScopes.length === 0,
      grantedScopes,
      missingScopes,
    };
  }

  /**
   * Extracts JWT token from request headers
   */
  private extractToken(request: NextRequest): string | null {
    // Check authorization header (Bearer scheme)
    const authHeader = request.headers.get('authorization');
    if (authHeader && authHeader.startsWith('Bearer ')) {
      return authHeader.slice(7).trim(); // Remove 'Bearer ' prefix
    }

    // Check x-jwt-token header as fallback
    const jwtHeader = request.headers.get('x-jwt-token');
    if (jwtHeader) {
      return jwtHeader.trim();
    }

    return null;
  }

  /**
   * Creates a user-specific middleware that requires userId in token
   */
  createUserMiddleware(options?: {
    onError?: (error: string, request: NextRequest) => NextResponse;
    onSuccess?: (payload: DecodedJwtPayload, request: NextRequest) => void;
  }) {
    return this.createMiddleware({
      ...options,
      validatePayload: (payload) => {
        // Require userId for user-specific endpoints
        return !!payload.userId;
      },
    });
  }

  /**
   * Creates a service-specific middleware that allows tokens without userId
   */
  createServiceMiddleware(options?: {
    onError?: (error: string, request: NextRequest) => NextResponse;
    onSuccess?: (payload: DecodedJwtPayload, request: NextRequest) => void;
  }) {
    return this.createMiddleware({
      ...options,
      validatePayload: (payload) => {
        // Service tokens may not have userId
        return !!payload.projectId && Array.isArray(payload.scopes);
      },
    });
  }
}

// Export singleton instance
export const jwtVerificationMiddleware = new JwtVerificationMiddleware();

// Export utility functions
export const verifyToken = (request: NextRequest) => 
  jwtVerificationMiddleware.verifyToken(request);

export const createJwtMiddleware = (options?: Parameters<JwtVerificationMiddleware['createMiddleware']>[0]) =>
  jwtVerificationMiddleware.createMiddleware(options);

export const createUserJwtMiddleware = (options?: Parameters<JwtVerificationMiddleware['createUserMiddleware']>[0]) =>
  jwtVerificationMiddleware.createUserMiddleware(options);

export const createServiceJwtMiddleware = (options?: Parameters<JwtVerificationMiddleware['createServiceMiddleware']>[0]) =>
  jwtVerificationMiddleware.createServiceMiddleware(options);