import { NextRequest, NextResponse } from 'next/server';
import { CorsValidationService, CorsValidationResult } from '../services/cors-validation';

export interface CorsMiddlewareOptions {
  allowCredentials?: boolean;
  maxAge?: number;
  allowedMethods?: string[];
  allowedHeaders?: string[];
  exposedHeaders?: string[];
  onError?: (error: string, request: NextRequest) => NextResponse;
  onSuccess?: (result: CorsValidationResult, request: NextRequest) => void;
}

export class CorsValidationMiddleware {
  private corsService: CorsValidationService;
  private defaultOptions: Required<Omit<CorsMiddlewareOptions, 'onError' | 'onSuccess'>>;

  constructor() {
    this.corsService = new CorsValidationService();
    this.defaultOptions = {
      allowCredentials: true,
      maxAge: 86400, // 24 hours
      allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
      allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-API-Key',
        'X-JWT-Token',
        'X-Requested-With',
        'Accept',
        'Origin',
        'User-Agent',
      ],
      exposedHeaders: [
        'X-RateLimit-Limit',
        'X-RateLimit-Remaining',
        'X-RateLimit-Reset',
      ],
    };
  }

  /**
   * Validates CORS origin for a specific project
   */
  async validateOrigin(request: NextRequest, projectId: string): Promise<CorsValidationResult> {
    try {
      const origin = this.extractOrigin(request);
      
      if (!origin) {
        // Allow same-origin requests (no Origin header)
        return {
          isValid: true,
          matchedOrigin: null,
        };
      }

      return await this.corsService.validateOrigin(projectId, origin);
    } catch (error) {
      console.error('CORS validation error:', error);
      return {
        isValid: false,
        matchedOrigin: null,
        reason: 'CORS validation failed',
      };
    }
  }

  /**
   * Middleware function for Next.js API routes
   */
  async middleware(
    request: NextRequest, 
    projectId: string, 
    options: CorsMiddlewareOptions = {}
  ): Promise<NextResponse | null> {
    const mergedOptions = { ...this.defaultOptions, ...options };
    const result = await this.validateOrigin(request, projectId);
    
    if (!result.isValid) {
      if (options.onError) {
        return options.onError(result.reason || 'Origin not allowed', request);
      }
      
      return NextResponse.json(
        {
          error: 'Forbidden',
          message: result.reason || 'Origin not allowed',
        },
        { status: 403 }
      );
    }

    if (options.onSuccess) {
      options.onSuccess(result, request);
    }

    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      return this.handlePreflightRequest(request, result, mergedOptions);
    }

    // Add CORS headers to actual requests
    const response = NextResponse.next();
    this.addCorsHeaders(response, request, result, mergedOptions);
    
    return response;
  }

  /**
   * Creates a middleware function with project ID resolution
   */
  createMiddleware(options?: CorsMiddlewareOptions & {
    getProjectId?: (request: NextRequest) => string | Promise<string>;
  }) {
    return async (request: NextRequest): Promise<NextResponse | null> => {
      let projectId: string;
      
      if (options?.getProjectId) {
        projectId = await options.getProjectId(request);
      } else {
        // Try to get project ID from headers (set by previous middleware)
        projectId = request.headers.get('x-project-id') || '';
      }
      
      if (!projectId) {
        const error = 'Project ID is required for CORS validation';
        
        if (options?.onError) {
          return options.onError(error, request);
        }
        
        return NextResponse.json(
          {
            error: 'Bad Request',
            message: error,
          },
          { status: 400 }
        );
      }

      return this.middleware(request, projectId, options);
    };
  }

  /**
   * Validates multiple origins for a project
   */
  async validateMultipleOrigins(
    origins: string[], 
    projectId: string
  ): Promise<{ [origin: string]: CorsValidationResult }> {
    return await this.corsService.validateMultipleOrigins(projectId, origins);
  }

  /**
   * Gets allowed origins for a project
   */
  async getAllowedOrigins(projectId: string): Promise<string[]> {
    return await this.corsService.getAllowedOrigins(projectId);
  }

  /**
   * Handles preflight OPTIONS requests
   */
  private handlePreflightRequest(
    request: NextRequest,
    corsResult: CorsValidationResult,
    options: Required<Omit<CorsMiddlewareOptions, 'onError' | 'onSuccess'>>
  ): NextResponse {
    const response = new NextResponse(null, { status: 204 });
    
    this.addCorsHeaders(response, request, corsResult, options);
    
    // Add preflight-specific headers
    response.headers.set('Access-Control-Max-Age', options.maxAge.toString());
    
    const requestedMethod = request.headers.get('access-control-request-method');
    if (requestedMethod && options.allowedMethods.includes(requestedMethod)) {
      response.headers.set('Access-Control-Allow-Methods', options.allowedMethods.join(', '));
    }
    
    const requestedHeaders = request.headers.get('access-control-request-headers');
    if (requestedHeaders) {
      const requestedHeadersList = requestedHeaders.split(',').map(h => h.trim());
      const allowedRequestedHeaders = requestedHeadersList.filter(h => 
        options.allowedHeaders.some(allowed => 
          allowed.toLowerCase() === h.toLowerCase()
        )
      );
      
      if (allowedRequestedHeaders.length > 0) {
        response.headers.set('Access-Control-Allow-Headers', allowedRequestedHeaders.join(', '));
      }
    }
    
    return response;
  }

  /**
   * Adds CORS headers to response
   */
  private addCorsHeaders(
    response: NextResponse,
    request: NextRequest,
    corsResult: CorsValidationResult,
    options: Required<Omit<CorsMiddlewareOptions, 'onError' | 'onSuccess'>>
  ): void {
    const origin = this.extractOrigin(request);
    
    if (origin && corsResult.isValid) {
      response.headers.set('Access-Control-Allow-Origin', origin);
    }
    
    if (options.allowCredentials) {
      response.headers.set('Access-Control-Allow-Credentials', 'true');
    }
    
    if (options.exposedHeaders.length > 0) {
      response.headers.set('Access-Control-Expose-Headers', options.exposedHeaders.join(', '));
    }
    
    // Add Vary header to indicate that the response varies based on Origin
    const existingVary = response.headers.get('Vary');
    const varyHeaders = existingVary ? `${existingVary}, Origin` : 'Origin';
    response.headers.set('Vary', varyHeaders);
  }

  /**
   * Extracts origin from request headers
   */
  private extractOrigin(request: NextRequest): string | null {
    return request.headers.get('origin');
  }

  /**
   * Creates a permissive CORS middleware for development
   */
  createDevelopmentMiddleware(allowedOrigins: string[] = ['http://localhost:3000']): (request: NextRequest) => NextResponse {
    return (request: NextRequest): NextResponse => {
      const origin = this.extractOrigin(request);
      
      // Handle preflight requests
      if (request.method === 'OPTIONS') {
        const response = new NextResponse(null, { status: 204 });
        
        if (origin && allowedOrigins.includes(origin)) {
          response.headers.set('Access-Control-Allow-Origin', origin);
        }
        
        response.headers.set('Access-Control-Allow-Methods', this.defaultOptions.allowedMethods.join(', '));
        response.headers.set('Access-Control-Allow-Headers', this.defaultOptions.allowedHeaders.join(', '));
        response.headers.set('Access-Control-Allow-Credentials', 'true');
        response.headers.set('Access-Control-Max-Age', this.defaultOptions.maxAge.toString());
        
        return response;
      }
      
      // Add CORS headers to actual requests
      const response = NextResponse.next();
      
      if (origin && allowedOrigins.includes(origin)) {
        response.headers.set('Access-Control-Allow-Origin', origin);
        response.headers.set('Access-Control-Allow-Credentials', 'true');
      }
      
      response.headers.set('Vary', 'Origin');
      
      return response;
    };
  }

  /**
   * Validates origin format without database lookup
   */
  isValidOriginFormat(origin: string): boolean {
    return this.corsService.isValidOrigin(origin);
  }

  /**
   * Checks if origin matches wildcard pattern
   */
  matchesWildcard(pattern: string, origin: string): boolean {
    return this.corsService.matchesWildcard(pattern, origin);
  }

  /**
   * Normalizes origin for consistent comparison
   */
  normalizeOrigin(origin: string): string {
    return this.corsService.normalizeOrigin(origin);
  }
}

// Export singleton instance
export const corsValidationMiddleware = new CorsValidationMiddleware();

// Export utility functions
export const validateOrigin = (request: NextRequest, projectId: string) => 
  corsValidationMiddleware.validateOrigin(request, projectId);

export const createCorsMiddleware = (options?: Parameters<CorsValidationMiddleware['createMiddleware']>[0]) =>
  corsValidationMiddleware.createMiddleware(options);

export const createDevelopmentCorsMiddleware = (allowedOrigins?: string[]) =>
  corsValidationMiddleware.createDevelopmentMiddleware(allowedOrigins);