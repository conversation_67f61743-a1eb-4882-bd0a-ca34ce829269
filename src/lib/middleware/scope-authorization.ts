import { NextRequest, NextResponse } from 'next/server';
import { ScopeValidationService, ScopeValidationResult } from '../services/scope-validation';

export interface ScopeAuthorizationOptions {
  requiredScopes: string[];
  requireAll?: boolean; // If true, all scopes are required; if false, any scope is sufficient
  allowServiceTokens?: boolean; // Allow tokens without userId (service-to-service)
  onError?: (error: string, request: NextRequest, result?: ScopeValidationResult) => NextResponse;
  onSuccess?: (result: ScopeValidationResult, request: NextRequest) => void;
}

export class ScopeAuthorizationMiddleware {
  private scopeService: ScopeValidationService;

  constructor() {
    this.scopeService = new ScopeValidationService();
  }

  /**
   * Validates user scopes against required scopes
   */
  async validateScopes(
    request: NextRequest,
    options: ScopeAuthorizationOptions
  ): Promise<ScopeValidationResult> {
    try {
      const { requiredScopes, requireAll = true, allowServiceTokens = false } = options;
      
      // Extract user and project information from headers (set by previous middleware)
      const userId = request.headers.get('x-user-id');
      const projectId = request.headers.get('x-project-id');
      const tokenScopes = this.extractTokenScopes(request);

      if (!projectId) {
        throw new Error('Project ID is required for scope validation');
      }

      // Handle empty required scopes
      if (requiredScopes.length === 0) {
        return {
          isValid: true,
          grantedScopes: [],
          missingScopes: [],
        };
      }

      // Determine validation type based on token type
      if (userId) {
        // User token - validate user-specific scopes
        return await this.scopeService.validateUserScopes(userId, projectId, requiredScopes);
      } else if (allowServiceTokens) {
        // Service token - validate service-level scopes
        return await this.scopeService.validateServiceScopes(projectId, requiredScopes);
      } else {
        // User token required but not provided
        return {
          isValid: false,
          grantedScopes: [],
          missingScopes: requiredScopes,
        };
      }
    } catch (error) {
      console.error('Scope validation error:', error);
      return {
        isValid: false,
        grantedScopes: [],
        missingScopes: options.requiredScopes,
      };
    }
  }

  /**
   * Middleware function for Next.js API routes
   */
  async middleware(
    request: NextRequest,
    options: ScopeAuthorizationOptions
  ): Promise<NextResponse | null> {
    const result = await this.validateScopes(request, options);
    
    if (!result.isValid) {
      const errorMessage = this.buildErrorMessage(result, options);
      
      if (options.onError) {
        return options.onError(errorMessage, request, result);
      }
      
      return NextResponse.json(
        {
          error: 'Forbidden',
          message: errorMessage,
          details: {
            requiredScopes: options.requiredScopes,
            grantedScopes: result.grantedScopes,
            missingScopes: result.missingScopes,
          },
        },
        { status: 403 }
      );
    }

    if (options.onSuccess) {
      options.onSuccess(result, request);
    }

    // Add scope validation result to headers for downstream middleware
    const response = NextResponse.next();
    response.headers.set('x-granted-scopes', JSON.stringify(result.grantedScopes));
    response.headers.set('x-scope-validation', 'passed');
    
    return response;
  }

  /**
   * Creates a middleware function with predefined scope requirements
   */
  createMiddleware(options: ScopeAuthorizationOptions) {
    return async (request: NextRequest): Promise<NextResponse | null> => {
      return this.middleware(request, options);
    };
  }

  /**
   * Creates a middleware for specific resource access patterns
   */
  createResourceMiddleware(
    resource: string,
    actions: string[],
    options?: Omit<ScopeAuthorizationOptions, 'requiredScopes'>
  ) {
    const requiredScopes = actions.map(action => `${resource}:${action}`);
    
    return this.createMiddleware({
      ...options,
      requiredScopes,
    });
  }

  /**
   * Creates a middleware that requires any of the specified scopes
   */
  createAnyOfMiddleware(
    scopes: string[],
    options?: Omit<ScopeAuthorizationOptions, 'requiredScopes' | 'requireAll'>
  ) {
    return this.createMiddleware({
      ...options,
      requiredScopes: scopes,
      requireAll: false,
    });
  }

  /**
   * Creates a middleware that requires all of the specified scopes
   */
  createAllOfMiddleware(
    scopes: string[],
    options?: Omit<ScopeAuthorizationOptions, 'requiredScopes' | 'requireAll'>
  ) {
    return this.createMiddleware({
      ...options,
      requiredScopes: scopes,
      requireAll: true,
    });
  }

  /**
   * Validates scopes using token scopes directly (without database lookup)
   */
  async validateTokenScopes(
    request: NextRequest,
    requiredScopes: string[],
    requireAll: boolean = true
  ): Promise<ScopeValidationResult> {
    try {
      const tokenScopes = this.extractTokenScopes(request);
      
      if (!tokenScopes || tokenScopes.length === 0) {
        return {
          isValid: false,
          grantedScopes: [],
          missingScopes: requiredScopes,
        };
      }

      const grantedScopes: string[] = [];
      const missingScopes: string[] = [];

      for (const requiredScope of requiredScopes) {
        if (this.scopeService.hasPermission(tokenScopes, requiredScope)) {
          grantedScopes.push(requiredScope);
        } else {
          missingScopes.push(requiredScope);
        }
      }

      const isValid = requireAll 
        ? missingScopes.length === 0 
        : grantedScopes.length > 0;

      return {
        isValid,
        grantedScopes,
        missingScopes,
      };
    } catch (error) {
      console.error('Token scope validation error:', error);
      return {
        isValid: false,
        grantedScopes: [],
        missingScopes: requiredScopes,
      };
    }
  }

  /**
   * Checks if request has admin privileges
   */
  async hasAdminAccess(request: NextRequest): Promise<boolean> {
    const result = await this.validateTokenScopes(request, ['admin:*'], false);
    return result.isValid;
  }

  /**
   * Checks if request has read access to a resource
   */
  async hasReadAccess(request: NextRequest, resource: string): Promise<boolean> {
    const result = await this.validateTokenScopes(request, [`${resource}:read`, `${resource}:*`, 'admin:*'], false);
    return result.isValid;
  }

  /**
   * Checks if request has write access to a resource
   */
  async hasWriteAccess(request: NextRequest, resource: string): Promise<boolean> {
    const result = await this.validateTokenScopes(request, [`${resource}:write`, `${resource}:*`, 'admin:*'], false);
    return result.isValid;
  }

  /**
   * Extracts scopes from JWT token in request headers
   */
  private extractTokenScopes(request: NextRequest): string[] {
    try {
      const scopesHeader = request.headers.get('x-scopes');
      
      if (!scopesHeader) {
        return [];
      }

      const scopes = JSON.parse(scopesHeader);
      
      if (!Array.isArray(scopes)) {
        return [];
      }

      return scopes.filter(scope => typeof scope === 'string');
    } catch (error) {
      console.error('Error extracting token scopes:', error);
      return [];
    }
  }

  /**
   * Builds error message based on validation result
   */
  private buildErrorMessage(result: ScopeValidationResult, options: ScopeAuthorizationOptions): string {
    if (result.missingScopes.length === 0) {
      return 'Access denied';
    }

    if (options.requireAll !== false) {
      return `Missing required permissions: ${result.missingScopes.join(', ')}`;
    } else {
      return `Requires at least one of the following permissions: ${options.requiredScopes.join(', ')}`;
    }
  }

  /**
   * Creates middleware for common access patterns
   */
  static createCommonMiddleware() {
    const middleware = new ScopeAuthorizationMiddleware();
    
    return {
      // Admin access required
      requireAdmin: middleware.createMiddleware({
        requiredScopes: ['admin:*'],
        requireAll: false,
      }),
      
      // User management access
      requireUserManagement: middleware.createResourceMiddleware('users', ['read', 'write']),
      
      // Read-only access to users
      requireUserRead: middleware.createResourceMiddleware('users', ['read']),
      
      // Service-to-service access
      requireServiceAccess: middleware.createMiddleware({
        requiredScopes: ['service:access'],
        allowServiceTokens: true,
      }),
      
      // Project management access
      requireProjectManagement: middleware.createResourceMiddleware('projects', ['read', 'write']),
      
      // Scope management access
      requireScopeManagement: middleware.createResourceMiddleware('scopes', ['read', 'write']),
    };
  }
}

// Export singleton instance
export const scopeAuthorizationMiddleware = new ScopeAuthorizationMiddleware();

// Export utility functions
export const validateScopes = (request: NextRequest, options: ScopeAuthorizationOptions) => 
  scopeAuthorizationMiddleware.validateScopes(request, options);

export const createScopeMiddleware = (options: ScopeAuthorizationOptions) =>
  scopeAuthorizationMiddleware.createMiddleware(options);

export const createResourceMiddleware = (
  resource: string,
  actions: string[],
  options?: Omit<ScopeAuthorizationOptions, 'requiredScopes'>
) => scopeAuthorizationMiddleware.createResourceMiddleware(resource, actions, options);

// Export common middleware patterns
export const commonScopeMiddleware = ScopeAuthorizationMiddleware.createCommonMiddleware();