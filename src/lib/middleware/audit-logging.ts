import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '../prisma';

export interface AuditLogEntry {
  projectId: string;
  userId?: string;
  action: string;
  resource?: string;
  method: string;
  endpoint: string;
  success: boolean;
  statusCode?: number;
  errorMessage?: string;
  ipAddress: string;
  userAgent: string;
  requestId?: string;
  duration?: number;
  requestSize?: number;
  responseSize?: number;
  metadata?: Record<string, any>;
}

export interface AuditLoggingOptions {
  logSuccessfulRequests?: boolean;
  logFailedRequests?: boolean;
  logRequestBody?: boolean;
  logResponseBody?: boolean;
  excludeEndpoints?: string[];
  includeMetadata?: (request: NextRequest) => Record<string, any>;
  onLog?: (entry: AuditLogEntry) => void;
  onError?: (error: Error, entry: Partial<AuditLogEntry>) => void;
}

export class AuditLoggingMiddleware {
  private defaultOptions: Required<Omit<AuditLoggingOptions, 'includeMetadata' | 'onLog' | 'onError'>>;

  constructor() {
    this.defaultOptions = {
      logSuccessfulRequests: true,
      logFailedRequests: true,
      logRequestBody: false,
      logResponseBody: false,
      excludeEndpoints: ['/health', '/metrics'],
    };
  }

  /**
   * Middleware function for Next.js API routes
   */
  async middleware(
    request: NextRequest,
    options: AuditLoggingOptions = {}
  ): Promise<NextResponse | null> {
    const mergedOptions = { ...this.defaultOptions, ...options };
    const startTime = Date.now();
    
    // Generate request ID
    const requestId = this.generateRequestId();
    
    // Extract request information
    const projectId = request.headers.get('x-project-id') || '';
    const userId = request.headers.get('x-user-id') || undefined;
    const method = request.method;
    const endpoint = new URL(request.url).pathname;
    const ipAddress = this.getClientIP(request);
    const userAgent = request.headers.get('user-agent') || '';

    // Check if endpoint should be excluded
    if (mergedOptions.excludeEndpoints.some(excluded => endpoint.includes(excluded))) {
      return null;
    }

    // Continue to next middleware/handler
    const response = NextResponse.next();
    
    // Add request ID to response headers
    response.headers.set('x-request-id', requestId);
    
    // Log the request asynchronously (don't block the response)
    this.logRequestAsync({
      projectId,
      userId,
      action: this.determineAction(method, endpoint),
      resource: this.extractResource(endpoint),
      method,
      endpoint,
      success: true, // Will be updated based on response
      ipAddress,
      userAgent,
      requestId,
      duration: Date.now() - startTime,
      requestSize: await this.getRequestSize(request),
      metadata: options.includeMetadata ? options.includeMetadata(request) : undefined,
    }, mergedOptions);

    return response;
  }

  /**
   * Creates a middleware function with predefined options
   */
  createMiddleware(options: AuditLoggingOptions = {}) {
    return async (request: NextRequest): Promise<NextResponse | null> => {
      return this.middleware(request, options);
    };
  }

  /**
   * Logs authentication events
   */
  async logAuthenticationEvent(
    projectId: string,
    action: 'login' | 'logout' | 'token_refresh' | 'api_key_validation' | 'jwt_verification',
    success: boolean,
    request: NextRequest,
    options?: {
      userId?: string;
      errorMessage?: string;
      metadata?: Record<string, any>;
    }
  ): Promise<void> {
    const entry: AuditLogEntry = {
      projectId,
      userId: options?.userId,
      action,
      resource: 'authentication',
      method: request.method,
      endpoint: new URL(request.url).pathname,
      success,
      ipAddress: this.getClientIP(request),
      userAgent: request.headers.get('user-agent') || '',
      errorMessage: options?.errorMessage,
      metadata: options?.metadata,
    };

    await this.logEntry(entry);
  }

  /**
   * Logs scope validation events
   */
  async logScopeValidationEvent(
    projectId: string,
    userId: string | undefined,
    requiredScopes: string[],
    grantedScopes: string[],
    success: boolean,
    request: NextRequest,
    errorMessage?: string
  ): Promise<void> {
    const entry: AuditLogEntry = {
      projectId,
      userId,
      action: 'scope_validation',
      resource: 'authorization',
      method: request.method,
      endpoint: new URL(request.url).pathname,
      success,
      ipAddress: this.getClientIP(request),
      userAgent: request.headers.get('user-agent') || '',
      errorMessage,
      metadata: {
        requiredScopes,
        grantedScopes,
        missingScopes: requiredScopes.filter(scope => !grantedScopes.includes(scope)),
      },
    };

    await this.logEntry(entry);
  }

  /**
   * Logs CORS validation events
   */
  async logCorsValidationEvent(
    projectId: string,
    origin: string,
    success: boolean,
    request: NextRequest,
    matchedOrigin?: string,
    errorMessage?: string
  ): Promise<void> {
    const entry: AuditLogEntry = {
      projectId,
      action: 'cors_validation',
      resource: 'cors',
      method: request.method,
      endpoint: new URL(request.url).pathname,
      success,
      ipAddress: this.getClientIP(request),
      userAgent: request.headers.get('user-agent') || '',
      errorMessage,
      metadata: {
        origin,
        matchedOrigin,
      },
    };

    await this.logEntry(entry);
  }

  /**
   * Logs rate limiting events
   */
  async logRateLimitEvent(
    projectId: string,
    userId: string | undefined,
    success: boolean,
    request: NextRequest,
    rateLimitInfo?: {
      limit: number;
      remaining: number;
      resetTime: number;
    }
  ): Promise<void> {
    const entry: AuditLogEntry = {
      projectId,
      userId,
      action: 'rate_limit_check',
      resource: 'rate_limiting',
      method: request.method,
      endpoint: new URL(request.url).pathname,
      success,
      ipAddress: this.getClientIP(request),
      userAgent: request.headers.get('user-agent') || '',
      errorMessage: success ? undefined : 'Rate limit exceeded',
      metadata: rateLimitInfo,
    };

    await this.logEntry(entry);
  }

  /**
   * Logs API access events
   */
  async logApiAccess(
    projectId: string,
    userId: string | undefined,
    action: string,
    resource: string,
    success: boolean,
    request: NextRequest,
    options?: {
      statusCode?: number;
      errorMessage?: string;
      duration?: number;
      metadata?: Record<string, any>;
    }
  ): Promise<void> {
    const entry: AuditLogEntry = {
      projectId,
      userId,
      action,
      resource,
      method: request.method,
      endpoint: new URL(request.url).pathname,
      success,
      statusCode: options?.statusCode,
      errorMessage: options?.errorMessage,
      ipAddress: this.getClientIP(request),
      userAgent: request.headers.get('user-agent') || '',
      duration: options?.duration,
      metadata: options?.metadata,
    };

    await this.logEntry(entry);
  }

  /**
   * Gets audit logs for a project with pagination
   */
  async getAuditLogs(
    projectId: string,
    options?: {
      userId?: string;
      action?: string;
      resource?: string;
      success?: boolean;
      startDate?: Date;
      endDate?: Date;
      limit?: number;
      offset?: number;
    }
  ) {
    try {
      const where: any = { projectId };
      
      if (options?.userId) where.userId = options.userId;
      if (options?.action) where.action = options.action;
      if (options?.resource) where.resource = options.resource;
      if (options?.success !== undefined) where.success = options.success;
      
      if (options?.startDate || options?.endDate) {
        where.timestamp = {};
        if (options.startDate) where.timestamp.gte = options.startDate;
        if (options.endDate) where.timestamp.lte = options.endDate;
      }

      const [logs, total] = await Promise.all([
        prisma.auditLog.findMany({
          where,
          orderBy: { timestamp: 'desc' },
          take: options?.limit || 100,
          skip: options?.offset || 0,
        }),
        prisma.auditLog.count({ where }),
      ]);

      return { logs, total };
    } catch (error) {
      console.error('Error fetching audit logs:', error);
      throw new Error('Failed to fetch audit logs');
    }
  }

  /**
   * Gets audit log statistics for a project
   */
  async getAuditStats(
    projectId: string,
    timeRange: { startDate: Date; endDate: Date }
  ) {
    try {
      const where = {
        projectId,
        timestamp: {
          gte: timeRange.startDate,
          lte: timeRange.endDate,
        },
      };

      const [
        totalRequests,
        successfulRequests,
        failedRequests,
        uniqueUsers,
        topActions,
        topResources,
      ] = await Promise.all([
        prisma.auditLog.count({ where }),
        prisma.auditLog.count({ where: { ...where, success: true } }),
        prisma.auditLog.count({ where: { ...where, success: false } }),
        prisma.auditLog.findMany({
          where: { ...where, userId: { not: undefined } },
          select: { userId: true },
          distinct: ['userId'],
        }),
        prisma.auditLog.groupBy({
          by: ['action'],
          where,
          _count: { action: true },
          orderBy: { _count: { action: 'desc' } },
          take: 10,
        }),
        prisma.auditLog.groupBy({
          by: ['resource'],
          where: { ...where, resource: { not: undefined } },
          _count: { resource: true },
          orderBy: { _count: { resource: 'desc' } },
          take: 10,
        }),
      ]);

      return {
        totalRequests,
        successfulRequests,
        failedRequests,
        successRate: totalRequests > 0 ? (successfulRequests / totalRequests) * 100 : 0,
        uniqueUsers: uniqueUsers.length,
        topActions: topActions.map(item => ({
          action: item.action,
          count: item._count.action,
        })),
        topResources: topResources.map(item => ({
          resource: item.resource,
          count: item._count.resource,
        })),
      };
    } catch (error) {
      console.error('Error fetching audit stats:', error);
      throw new Error('Failed to fetch audit statistics');
    }
  }

  /**
   * Logs an audit entry to the database
   */
  private async logEntry(entry: AuditLogEntry): Promise<void> {
    try {
      await prisma.auditLog.create({
        data: {
          projectId: entry.projectId,
          userId: entry.userId,
          action: entry.action,
          resource: entry.resource || '',
          requestMethod: entry.method,
          apiEndpoint: entry.endpoint,
          success: entry.success,
          statusCode: entry.statusCode,
          errorMessage: entry.errorMessage,
          ipAddress: entry.ipAddress,
          userAgent: entry.userAgent,
          requestId: entry.requestId,
          duration: entry.duration,
          requestSize: entry.requestSize,
          responseSize: entry.responseSize,
          metadata: entry.metadata ? entry.metadata : undefined,
        },
      });
    } catch (error) {
      console.error('Error logging audit entry:', error);
      // Don't throw error to avoid breaking the main request flow
    }
  }

  /**
   * Logs request asynchronously
   */
  private async logRequestAsync(
    entry: AuditLogEntry,
    options: Required<Omit<AuditLoggingOptions, 'includeMetadata' | 'onLog' | 'onError'>>
  ): Promise<void> {
    // Use setTimeout to make it truly async and not block the response
    setTimeout(async () => {
      try {
        await this.logEntry(entry);
      } catch (error) {
        console.error('Async audit logging error:', error);
      }
    }, 0);
  }

  /**
   * Generates a unique request ID
   */
  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Extracts client IP address from request
   */
  private getClientIP(request: NextRequest): string {
    const forwarded = request.headers.get('x-forwarded-for');
    if (forwarded) {
      return forwarded.split(',')[0].trim();
    }
    
    const realIP = request.headers.get('x-real-ip');
    if (realIP) {
      return realIP.trim();
    }
    
    const cfConnectingIP = request.headers.get('cf-connecting-ip');
    if (cfConnectingIP) {
      return cfConnectingIP.trim();
    }
    
    return 'unknown';
  }

  /**
   * Determines action based on HTTP method and endpoint
   */
  private determineAction(method: string, endpoint: string): string {
    const methodMap: { [key: string]: string } = {
      GET: 'read',
      POST: 'create',
      PUT: 'update',
      PATCH: 'update',
      DELETE: 'delete',
    };
    
    return methodMap[method] || method.toLowerCase();
  }

  /**
   * Extracts resource from endpoint path
   */
  private extractResource(endpoint: string): string {
    // Remove /api prefix and extract the first path segment
    const pathParts = endpoint.replace(/^\/api\//, '').split('/');
    return pathParts[0] || 'unknown';
  }

  /**
   * Gets request size in bytes
   */
  private async getRequestSize(request: NextRequest): Promise<number> {
    try {
      const contentLength = request.headers.get('content-length');
      if (contentLength) {
        return parseInt(contentLength, 10);
      }
      
      // For requests without content-length, estimate based on headers
      let size = 0;
      request.headers.forEach((value, key) => {
        size += key.length + value.length + 4; // +4 for ': ' and '\r\n'
      });
      
      return size;
    } catch (error) {
      return 0;
    }
  }
}

// Export singleton instance
export const auditLoggingMiddleware = new AuditLoggingMiddleware();

// Export utility functions
export const createAuditMiddleware = (options?: AuditLoggingOptions) =>
  auditLoggingMiddleware.createMiddleware(options);

export const logAuthenticationEvent = (
  projectId: string,
  action: Parameters<AuditLoggingMiddleware['logAuthenticationEvent']>[1],
  success: boolean,
  request: NextRequest,
  options?: Parameters<AuditLoggingMiddleware['logAuthenticationEvent']>[4]
) => auditLoggingMiddleware.logAuthenticationEvent(projectId, action, success, request, options);

export const logApiAccess = (
  projectId: string,
  userId: string | undefined,
  action: string,
  resource: string,
  success: boolean,
  request: NextRequest,
  options?: Parameters<AuditLoggingMiddleware['logApiAccess']>[6]
) => auditLoggingMiddleware.logApiAccess(projectId, userId, action, resource, success, request, options);