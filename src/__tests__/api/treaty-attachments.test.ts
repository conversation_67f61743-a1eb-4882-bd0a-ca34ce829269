import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import { createTestUser, deleteTestUser } from '../test-utils';

describe('Treaty File Attachment API', () => {
  let prisma: PrismaClient;
  let testUser: any;
  let testTreatyType: any;
  let testTreaty: any;
  let testFileAttachment: any;

  beforeAll(async () => {
    prisma = new PrismaClient();
    testUser = await createTestUser(prisma);
    
    // Create a test treaty type with a unique name
    const timestamp = Date.now();
    testTreatyType = await prisma.treatyType.create({
      data: {
        name: `Test Treaty Type ${timestamp}`,
        description: 'Test treaty type for file attachment testing',
        category: 'TEST',
      },
    });
  });

  afterAll(async () => {
    if (testUser) {
      await deleteTestUser(prisma, testUser.id);
    }
    if (testTreatyType) {
      await prisma.treatyType.delete({ where: { id: testTreatyType.id } });
    }
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Create a test treaty before each test
    testTreaty = await prisma.treaty.create({
      data: {
        userId: testUser.id,
        treatyTypeId: testTreatyType.id,
        status: 'ACTIVE',
        notes: 'Test treaty notes',
        signedDate: new Date(),
      },
    });
  });

  afterEach(async () => {
    // Delete the test file attachment if it exists
    if (testFileAttachment) {
      await prisma.fileAttachment.delete({ where: { id: testFileAttachment.id } });
      testFileAttachment = null;
    }
    
    // Delete the test treaty after each test
    if (testTreaty) {
      await prisma.treaty.delete({ where: { id: testTreaty.id } });
      testTreaty = null;
    }
  });

  it('should create a new file attachment', async () => {
    const fileAttachmentData = {
      treatyId: testTreaty.id,
      fileName: 'test-document.pdf',
      filePath: 'treaties/test-treaty/test-document.pdf',
      mimeType: 'application/pdf',
      fileSize: 1024,
    };

    // Create file attachment
    const fileAttachment = await prisma.fileAttachment.create({
      data: fileAttachmentData,
    });

    expect(fileAttachment).toBeDefined();
    expect(fileAttachment.treatyId).toBe(testTreaty.id);
    expect(fileAttachment.fileName).toBe(fileAttachmentData.fileName);
    expect(fileAttachment.filePath).toBe(fileAttachmentData.filePath);
    expect(fileAttachment.mimeType).toBe(fileAttachmentData.mimeType);
    expect(fileAttachment.fileSize).toBe(fileAttachmentData.fileSize);

    // Store for cleanup
    testFileAttachment = fileAttachment;
  });

  it('should retrieve file attachments for a treaty', async () => {
    // Create file attachments
    const fileAttachment1 = await prisma.fileAttachment.create({
      data: {
        treatyId: testTreaty.id,
        fileName: 'test-document-1.pdf',
        filePath: 'treaties/test-treaty/test-document-1.pdf',
        mimeType: 'application/pdf',
        fileSize: 1024,
      },
    });

    const fileAttachment2 = await prisma.fileAttachment.create({
      data: {
        treatyId: testTreaty.id,
        fileName: 'test-document-2.pdf',
        filePath: 'treaties/test-treaty/test-document-2.pdf',
        mimeType: 'application/pdf',
        fileSize: 2048,
      },
    });

    // Retrieve file attachments for the treaty
    const treatyWithAttachments = await prisma.treaty.findUnique({
      where: {
        id: testTreaty.id,
      },
      include: {
        fileAttachments: true,
      },
    });

    expect(treatyWithAttachments).toBeDefined();
    expect(treatyWithAttachments?.fileAttachments).toHaveLength(2);
    expect(treatyWithAttachments?.fileAttachments.some(f => f.id === fileAttachment1.id)).toBe(true);
    expect(treatyWithAttachments?.fileAttachments.some(f => f.id === fileAttachment2.id)).toBe(true);

    // Clean up
    await prisma.fileAttachment.delete({ where: { id: fileAttachment1.id } });
    await prisma.fileAttachment.delete({ where: { id: fileAttachment2.id } });
  });

  it('should delete a file attachment', async () => {
    // Create a file attachment
    const fileAttachment = await prisma.fileAttachment.create({
      data: {
        treatyId: testTreaty.id,
        fileName: 'test-document.pdf',
        filePath: 'treaties/test-treaty/test-document.pdf',
        mimeType: 'application/pdf',
        fileSize: 1024,
      },
    });

    // Delete the file attachment
    await prisma.fileAttachment.delete({
      where: {
        id: fileAttachment.id,
      },
    });

    // Try to find the file attachment
    const deletedFileAttachment = await prisma.fileAttachment.findUnique({
      where: {
        id: fileAttachment.id,
      },
    });

    expect(deletedFileAttachment).toBeNull();
  });
});