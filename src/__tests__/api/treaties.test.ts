import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import { createTestUser, deleteTestUser } from '../test-utils';

describe('Treaty Model', () => {
  let prisma: PrismaClient;
  let testUser: any;

  beforeAll(async () => {
    prisma = new PrismaClient();
    testUser = await createTestUser(prisma);
  });

  afterAll(async () => {
    if (testUser) {
      await deleteTestUser(prisma, testUser.id);
    }
    await prisma.$disconnect();
  });

  it('should create a new treaty', async () => {
    // First, create a treaty type with a unique name
    const timestamp = Date.now();
    const treatyType = await prisma.treatyType.create({
      data: {
        name: `Test Treaty Type ${timestamp}`,
        description: 'Test treaty type for testing',
        category: 'TEST',
      },
    });

    // Create a treaty
    const treaty = await prisma.treaty.create({
      data: {
        userId: testUser.id,
        treatyTypeId: treatyType.id,
        status: 'ACTIVE',
        notes: 'Test treaty notes',
        signedDate: new Date(),
      },
    });

    expect(treaty).toBeDefined();
    expect(treaty.userId).toBe(testUser.id);
    expect(treaty.treatyTypeId).toBe(treatyType.id);
    expect(treaty.status).toBe('ACTIVE');
    expect(treaty.notes).toBe('Test treaty notes');

    // Clean up
    await prisma.treaty.delete({ where: { id: treaty.id } });
    await prisma.treatyType.delete({ where: { id: treatyType.id } });
  });

  it('should retrieve treaties for a user', async () => {
    // Create a treaty type with a unique name
    const timestamp = Date.now();
    const treatyType = await prisma.treatyType.create({
      data: {
        name: `Test Treaty Type ${timestamp}`,
        description: 'Test treaty type for testing',
        category: 'TEST',
      },
    });

    // Create multiple treaties
    const treaty1 = await prisma.treaty.create({
      data: {
        userId: testUser.id,
        treatyTypeId: treatyType.id,
        status: 'ACTIVE',
        notes: 'Test treaty 1',
        signedDate: new Date(),
      },
    });

    const treaty2 = await prisma.treaty.create({
      data: {
        userId: testUser.id,
        treatyTypeId: treatyType.id,
        status: 'EXPIRED',
        notes: 'Test treaty 2',
        signedDate: new Date(),
        expirationDate: new Date(),
      },
    });

    // Create a treaty for another user
    const otherUser = await createTestUser(prisma, '<EMAIL>');
    const treaty3 = await prisma.treaty.create({
      data: {
        userId: otherUser.id,
        treatyTypeId: treatyType.id,
        status: 'ACTIVE',
        notes: 'Test treaty 3',
        signedDate: new Date(),
      },
    });

    // Retrieve treaties for test user
    const userTreaties = await prisma.treaty.findMany({
      where: {
        userId: testUser.id,
      },
      include: {
        treatyType: true,
      },
    });

    expect(userTreaties).toHaveLength(2);
    expect(userTreaties[0].id).toBe(treaty1.id);
    expect(userTreaties[1].id).toBe(treaty2.id);
    expect(userTreaties.some(t => t.id === treaty3.id)).toBe(false);

    // Clean up
    await prisma.treaty.delete({ where: { id: treaty1.id } });
    await prisma.treaty.delete({ where: { id: treaty2.id } });
    await prisma.treaty.delete({ where: { id: treaty3.id } });
    await prisma.treatyType.delete({ where: { id: treatyType.id } });
    await deleteTestUser(prisma, otherUser.id);
  });

  it('should update a treaty', async () => {
    // Create a treaty type with a unique name
    const timestamp = Date.now();
    const treatyType = await prisma.treatyType.create({
      data: {
        name: `Test Treaty Type ${timestamp}`,
        description: 'Test treaty type for testing',
        category: 'TEST',
      },
    });

    // Create a treaty
    const treaty = await prisma.treaty.create({
      data: {
        userId: testUser.id,
        treatyTypeId: treatyType.id,
        status: 'ACTIVE',
        notes: 'Test treaty notes',
        signedDate: new Date(),
      },
    });

    // Update the treaty
    const updatedTreaty = await prisma.treaty.update({
      where: {
        id: treaty.id,
      },
      data: {
        status: 'EXPIRED',
        expirationDate: new Date(),
        notes: 'Updated treaty notes',
      },
    });

    expect(updatedTreaty.status).toBe('EXPIRED');
    expect(updatedTreaty.notes).toBe('Updated treaty notes');
    expect(updatedTreaty.expirationDate).toBeDefined();

    // Clean up
    await prisma.treaty.delete({ where: { id: treaty.id } });
    await prisma.treatyType.delete({ where: { id: treatyType.id } });
  });

  it('should delete a treaty', async () => {
    // Create a treaty type with a unique name
    const timestamp = Date.now();
    const treatyType = await prisma.treatyType.create({
      data: {
        name: `Test Treaty Type ${timestamp}`,
        description: 'Test treaty type for testing',
        category: 'TEST',
      },
    });

    // Create a treaty
    const treaty = await prisma.treaty.create({
      data: {
        userId: testUser.id,
        treatyTypeId: treatyType.id,
        status: 'ACTIVE',
        notes: 'Test treaty notes',
        signedDate: new Date(),
      },
    });

    // Delete the treaty
    await prisma.treaty.delete({
      where: {
        id: treaty.id,
      },
    });

    // Try to find the treaty
    const deletedTreaty = await prisma.treaty.findUnique({
      where: {
        id: treaty.id,
      },
    });

    expect(deletedTreaty).toBeNull();

    // Clean up
    await prisma.treatyType.delete({ where: { id: treatyType.id } });
  });
});