import { POST as signupPOST } from '@/app/api/auth/signup/route';
import { POST as verifyEmailPOST } from '@/app/api/auth/verify-email/route';
import { POST as resetPasswordPOST } from '@/app/api/auth/reset-password/route';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';

// Mock the prisma client
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
      create: jest.fn(),
    },
    userProfile: {
      create: jest.fn(),
    },
    verificationToken: {
      create: jest.fn(),
      findUnique: jest.fn(),
      delete: jest.fn(),
    },
  },
}));

// Mock bcrypt
jest.mock('bcryptjs', () => ({
  hash: jest.fn(),
}));

// Mock EmailService
jest.mock('@/lib/services/email-service', () => ({
  EmailService: jest.fn().mockImplementation(() => ({
    sendVerificationEmail: jest.fn().mockResolvedValue(undefined),
  })),
}));

describe('Auth API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /api/auth/signup', () => {
    it('should create a new user successfully', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          email: '<EMAIL>',
          password: 'password123',
          name: 'Test User',
        }),
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);
      (bcrypt.hash as jest.Mock).mockResolvedValue('hashed-password');
      (prisma.user.create as jest.Mock).mockResolvedValue({
        id: 'user-id',
        email: '<EMAIL>',
        name: 'Test User',
      });
      (prisma.userProfile.create as jest.Mock).mockResolvedValue({});
      (prisma.verificationToken.create as jest.Mock).mockResolvedValue({});

      const response = await signupPOST(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('User created successfully. Please check your email to verify your account.');
      expect(prisma.user.create).toHaveBeenCalled();
      expect(prisma.userProfile.create).toHaveBeenCalled();
      expect(prisma.verificationToken.create).toHaveBeenCalled();
    });

    it('should return an error if user already exists', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          email: '<EMAIL>',
          password: 'password123',
          name: 'Test User',
        }),
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'existing-user-id',
        email: '<EMAIL>',
      });

      const response = await signupPOST(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('User already exists');
    });

    it('should return validation errors for invalid input', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          email: 'invalid-email',
          password: 'pass',
          name: '',
        }),
      };

      const response = await signupPOST(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Validation error');
    });
  });

  describe('POST /api/auth/verify-email', () => {
    it('should verify email successfully', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          token: 'valid-token',
        }),
      };

      (prisma.verificationToken.findUnique as jest.Mock).mockResolvedValue({
        identifier: '<EMAIL>',
        token: 'valid-token',
        expires: new Date(Date.now() + 10000),
      });
      (prisma.user.update as jest.Mock).mockResolvedValue({});
      (prisma.verificationToken.delete as jest.Mock).mockResolvedValue({});

      const response = await verifyEmailPOST(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Email verified successfully');
      expect(prisma.user.update).toHaveBeenCalled();
      expect(prisma.verificationToken.delete).toHaveBeenCalled();
    });

    it('should return error for invalid or expired token', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          token: 'invalid-token',
        }),
      };

      (prisma.verificationToken.findUnique as jest.Mock).mockResolvedValue(null);

      const response = await verifyEmailPOST(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid or expired token');
    });
  });

  describe('POST /api/auth/reset-password', () => {
    it('should send password reset email for existing user', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          email: '<EMAIL>',
        }),
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'user-id',
        email: '<EMAIL>',
      });
      (prisma.verificationToken.create as jest.Mock).mockResolvedValue({});

      const response = await resetPasswordPOST(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('If your email is registered, you will receive a password reset link');
      expect(prisma.verificationToken.create).toHaveBeenCalled();
    });

    it('should reset password with valid token', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          token: 'valid-token',
          newPassword: 'newpassword123',
        }),
      };

      (prisma.verificationToken.findUnique as jest.Mock).mockResolvedValue({
        identifier: '<EMAIL>',
        token: 'valid-token',
        expires: new Date(Date.now() + 10000),
      });
      (bcrypt.hash as jest.Mock).mockResolvedValue('new-hashed-password');
      (prisma.user.update as jest.Mock).mockResolvedValue({});
      (prisma.verificationToken.delete as jest.Mock).mockResolvedValue({});

      const response = await resetPasswordPOST(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Password reset successfully');
      expect(bcrypt.hash).toHaveBeenCalledWith('newpassword123', 12);
      expect(prisma.user.update).toHaveBeenCalled();
      expect(prisma.verificationToken.delete).toHaveBeenCalled();
    });

    it('should return error for invalid or expired reset token', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          token: 'invalid-token',
          newPassword: 'newpassword123',
        }),
      };

      (prisma.verificationToken.findUnique as jest.Mock).mockResolvedValue(null);

      const response = await resetPasswordPOST(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Invalid or expired token');
    });
  });
});