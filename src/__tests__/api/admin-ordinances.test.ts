import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import { createTestUser, deleteTestUser } from '../test-utils';

describe('Admin Ordinance API', () => {
  let prisma: PrismaClient;
  let testUser: any;
  let testAdminUser: any;
  let testOrdinanceType: any;
  let testOrdinance: any;
  let adminRole: any;

  beforeAll(async () => {
    prisma = new PrismaClient();
    testUser = await createTestUser(prisma);
    testAdminUser = await createTestUser(prisma, '<EMAIL>');
    
    // Create a test ordinance type with a unique name
    const timestamp = Date.now();
    testOrdinanceType = await prisma.ordinanceType.create({
      data: {
        name: `Test Ordinance Type ${timestamp}`,
        description: 'Test ordinance type for admin API testing',
        category: 'TEST',
      },
    });
    
    // Create admin role if it doesn't exist
    adminRole = await prisma.role.upsert({
      where: { name: 'ADMI<PERSON>' },
      update: {},
      create: {
        name: 'ADMI<PERSON>',
        description: 'Administrator role',
        isSystem: true,
      },
    });
    
    // Assign admin role to test admin user
    await prisma.userRole.create({
      data: {
        userId: testAdminUser.id,
        roleId: adminRole.id,
      },
    });
  });

  afterAll(async () => {
    if (testUser) {
      await deleteTestUser(prisma, testUser.id);
    }
    if (testAdminUser) {
      await deleteTestUser(prisma, testAdminUser.id);
    }
    if (testOrdinanceType) {
      await prisma.ordinanceType.delete({ where: { id: testOrdinanceType.id } });
    }
    if (adminRole) {
      // Remove the user role assignment but don't delete the role itself
      await prisma.userRole.deleteMany({
        where: {
          userId: testAdminUser.id,
          roleId: adminRole.id,
        },
      });
    }
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Create a test ordinance before each test
    testOrdinance = await prisma.ordinance.create({
      data: {
        userId: testUser.id,
        ordinanceTypeId: testOrdinanceType.id,
        status: 'PENDING',
        notes: 'Test ordinance notes',
      },
    });
  });

  afterEach(async () => {
    // Delete the test ordinance after each test
    if (testOrdinance) {
      await prisma.ordinance.delete({ where: { id: testOrdinance.id } });
      testOrdinance = null;
    }
  });

  it('should retrieve all ordinances for admin user', async () => {
    // Create another ordinance
    const anotherOrdinance = await prisma.ordinance.create({
      data: {
        userId: testUser.id,
        ordinanceTypeId: testOrdinanceType.id,
        status: 'COMPLETED',
        notes: 'Another test ordinance',
        completedDate: new Date(),
      },
    });

    // Retrieve ordinances as admin (filter by test user to avoid other test data)
    const adminOrdinances = await prisma.ordinance.findMany({
      where: {
        userId: testUser.id,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        ordinanceType: true,
      },
    });

    expect(adminOrdinances).toHaveLength(2);
    expect(adminOrdinances.some(o => o.id === testOrdinance.id)).toBe(true);
    expect(adminOrdinances.some(o => o.id === anotherOrdinance.id)).toBe(true);

    // Verify admin user data is included
    const ordinanceWithUser = adminOrdinances.find(o => o.id === testOrdinance.id);
    expect(ordinanceWithUser?.user?.id).toBe(testUser.id);
    expect(ordinanceWithUser?.user?.name).toBe('Test User');

    // Clean up
    await prisma.ordinance.delete({ where: { id: anotherOrdinance.id } });
  });

  it('should filter ordinances by user ID', async () => {
    // Create another user and ordinance
    const otherUser = await createTestUser(prisma, '<EMAIL>');
    const otherOrdinance = await prisma.ordinance.create({
      data: {
        userId: otherUser.id,
        ordinanceTypeId: testOrdinanceType.id,
        status: 'PENDING',
        notes: 'Other user ordinance',
      },
    });

    // Retrieve ordinances filtered by user ID
    const userOrdinances = await prisma.ordinance.findMany({
      where: {
        userId: testUser.id,
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        ordinanceType: true,
      },
    });

    expect(userOrdinances).toHaveLength(1);
    expect(userOrdinances[0].id).toBe(testOrdinance.id);
    expect(userOrdinances[0].userId).toBe(testUser.id);

    // Clean up
    await prisma.ordinance.delete({ where: { id: otherOrdinance.id } });
    await deleteTestUser(prisma, otherUser.id);
  });

  it('should filter ordinances by status', async () => {
    // Create another ordinance with different status
    const completedOrdinance = await prisma.ordinance.create({
      data: {
        userId: testUser.id,
        ordinanceTypeId: testOrdinanceType.id,
        status: 'COMPLETED',
        notes: 'Completed ordinance',
        completedDate: new Date(),
      },
    });

    // Retrieve ordinances filtered by status and user
    const pendingOrdinances = await prisma.ordinance.findMany({
      where: {
        userId: testUser.id,
        status: 'PENDING',
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
        ordinanceType: true,
      },
    });

    expect(pendingOrdinances).toHaveLength(1);
    expect(pendingOrdinances[0].id).toBe(testOrdinance.id);
    expect(pendingOrdinances[0].status).toBe('PENDING');

    // Clean up
    await prisma.ordinance.delete({ where: { id: completedOrdinance.id } });
  });
});