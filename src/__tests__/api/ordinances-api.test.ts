import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import { createTestUser, deleteTestUser } from '../test-utils';

describe('Ordinance API', () => {
  let prisma: PrismaClient;
  let testUser: any;
  let testOrdinanceType: any;
  let testOrdinance: any;

  beforeAll(async () => {
    prisma = new PrismaClient();
    testUser = await createTestUser(prisma);
    
    // Create a test ordinance type
    testOrdinanceType = await prisma.ordinanceType.create({
      data: {
        name: 'Test Ordinance Type',
        description: 'Test ordinance type for API testing',
        category: 'TEST',
      },
    });
  });

  afterAll(async () => {
    if (testUser) {
      await deleteTestUser(prisma, testUser.id);
    }
    if (testOrdinanceType) {
      await prisma.ordinanceType.delete({ where: { id: testOrdinanceType.id } });
    }
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Create a test ordinance before each test
    testOrdinance = await prisma.ordinance.create({
      data: {
        userId: testUser.id,
        ordinanceTypeId: testOrdinanceType.id,
        status: 'PENDING',
        notes: 'Test ordinance notes',
      },
    });
  });

  afterEach(async () => {
    // Delete the test ordinance after each test
    if (testOrdinance) {
      await prisma.ordinance.delete({ where: { id: testOrdinance.id } });
      testOrdinance = null;
    }
  });

  it('should create a new ordinance', async () => {
    const newOrdinanceData = {
      ordinanceTypeId: testOrdinanceType.id,
      notes: 'New test ordinance notes',
      status: 'PENDING',
    };

    // In a real test, we would make an actual API call here
    // For now, we'll test the database operation directly
    const ordinance = await prisma.ordinance.create({
      data: {
        userId: testUser.id,
        ...newOrdinanceData,
      },
    });

    expect(ordinance).toBeDefined();
    expect(ordinance.userId).toBe(testUser.id);
    expect(ordinance.ordinanceTypeId).toBe(testOrdinanceType.id);
    expect(ordinance.notes).toBe(newOrdinanceData.notes);
    expect(ordinance.status).toBe(newOrdinanceData.status);

    // Clean up
    await prisma.ordinance.delete({ where: { id: ordinance.id } });
  });

  it('should retrieve ordinances for a user', async () => {
    // Create another ordinance
    const anotherOrdinance = await prisma.ordinance.create({
      data: {
        userId: testUser.id,
        ordinanceTypeId: testOrdinanceType.id,
        status: 'COMPLETED',
        notes: 'Another test ordinance',
        completedDate: new Date(),
      },
    });

    // Retrieve ordinances for test user
    const userOrdinances = await prisma.ordinance.findMany({
      where: {
        userId: testUser.id,
      },
      include: {
        ordinanceType: true,
      },
    });

    expect(userOrdinances).toHaveLength(2);
    expect(userOrdinances.some(o => o.id === testOrdinance.id)).toBe(true);
    expect(userOrdinances.some(o => o.id === anotherOrdinance.id)).toBe(true);

    // Clean up
    await prisma.ordinance.delete({ where: { id: anotherOrdinance.id } });
  });

  it('should retrieve a specific ordinance by ID', async () => {
    // Retrieve the specific ordinance
    const ordinance = await prisma.ordinance.findUnique({
      where: {
        id: testOrdinance.id,
      },
      include: {
        ordinanceType: true,
      },
    });

    expect(ordinance).toBeDefined();
    expect(ordinance?.id).toBe(testOrdinance.id);
    expect(ordinance?.userId).toBe(testUser.id);
    expect(ordinance?.ordinanceTypeId).toBe(testOrdinanceType.id);
    expect(ordinance?.notes).toBe('Test ordinance notes');
    expect(ordinance?.status).toBe('PENDING');
  });

  it('should update an ordinance', async () => {
    // Update the ordinance
    const updatedOrdinance = await prisma.ordinance.update({
      where: {
        id: testOrdinance.id,
      },
      data: {
        status: 'COMPLETED',
        completedDate: new Date(),
        notes: 'Updated ordinance notes',
      },
    });

    expect(updatedOrdinance.status).toBe('COMPLETED');
    expect(updatedOrdinance.notes).toBe('Updated ordinance notes');
    expect(updatedOrdinance.completedDate).toBeDefined();
  });

  it('should delete an ordinance', async () => {
    // Delete the ordinance
    await prisma.ordinance.delete({
      where: {
        id: testOrdinance.id,
      },
    });

    // Try to find the ordinance
    const deletedOrdinance = await prisma.ordinance.findUnique({
      where: {
        id: testOrdinance.id,
      },
    });

    expect(deletedOrdinance).toBeNull();
    
    // Reset testOrdinance so afterEach doesn't try to delete it again
    testOrdinance = null;
  });
});