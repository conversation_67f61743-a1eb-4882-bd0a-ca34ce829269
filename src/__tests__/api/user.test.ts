import { POST as emailPOST } from '@/app/api/user/email/route';
import { POST as passwordPUT } from '@/app/api/user/password/route';
import { POST as twoFAPOST, DELETE as twoFADELETE } from '@/app/api/user/2fa/route';
import { prisma } from '@/lib/prisma';
import bcrypt from 'bcryptjs';
import { getServerSession } from 'next-auth/next';

// Mock the prisma client
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
      update: jest.fn(),
    },
    verificationToken: {
      create: jest.fn(),
      findUnique: jest.fn(),
      delete: jest.fn(),
    },
  },
}));

// Mock next-auth
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn(),
}));

// Mock bcrypt
jest.mock('bcryptjs', () => ({
  hash: jest.fn(),
  compare: jest.fn(),
}));

// Mock EmailService
jest.mock('@/lib/services/email-service', () => ({
  EmailService: jest.fn().mockImplementation(() => ({
    sendVerificationEmail: jest.fn().mockResolvedValue(undefined),
  })),
}));

describe('User API', () => {
  const mockSession = {
    user: {
      id: 'user-id',
      email: '<EMAIL>',
      name: 'Test User',
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (getServerSession as jest.Mock).mockResolvedValue(mockSession);
  });

  describe('POST /api/user/email', () => {
    it('should send verification email for email update', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          newEmail: '<EMAIL>',
        }),
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);
      (prisma.verificationToken.create as jest.Mock).mockResolvedValue({});

      const response = await emailPOST(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Verification email sent. Please check your inbox.');
      expect(prisma.verificationToken.create).toHaveBeenCalled();
    });

    it('should update email with valid verification token', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          token: 'valid-token',
        }),
      };

      (prisma.verificationToken.findUnique as jest.Mock).mockResolvedValue({
        identifier: '<EMAIL>',
        token: 'valid-token',
        expires: new Date(Date.now() + 10000),
      });
      (prisma.user.update as jest.Mock).mockResolvedValue({});
      (prisma.verificationToken.delete as jest.Mock).mockResolvedValue({});

      const response = await emailPOST(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Email updated successfully');
      expect(prisma.user.update).toHaveBeenCalled();
      expect(prisma.verificationToken.delete).toHaveBeenCalled();
    });

    it('should return error if email is already in use', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          newEmail: '<EMAIL>',
        }),
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'other-user-id',
        email: '<EMAIL>',
      });

      const response = await emailPOST(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Email is already in use');
    });
  });

  describe('PUT /api/user/password', () => {
    it('should update password with correct credentials', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          currentPassword: 'oldpassword',
          newPassword: 'newpassword123',
        }),
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'user-id',
        passwordHash: 'old-hashed-password',
      });
      (bcrypt.compare as jest.Mock).mockResolvedValue(true);
      (bcrypt.hash as jest.Mock).mockResolvedValue('new-hashed-password');
      (prisma.user.update as jest.Mock).mockResolvedValue({});

      const response = await passwordPUT(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('Password updated successfully');
      expect(bcrypt.compare).toHaveBeenCalledWith('oldpassword', 'old-hashed-password');
      expect(bcrypt.hash).toHaveBeenCalledWith('newpassword123', 12);
      expect(prisma.user.update).toHaveBeenCalled();
    });

    it('should return error for incorrect current password', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          currentPassword: 'wrongpassword',
          newPassword: 'newpassword123',
        }),
      };

      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'user-id',
        passwordHash: 'correct-hashed-password',
      });
      (bcrypt.compare as jest.Mock).mockResolvedValue(false);

      const response = await passwordPUT(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Current password is incorrect');
    });

    it('should return validation errors for weak passwords', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({
          currentPassword: 'oldpassword',
          newPassword: 'weak',
        }),
      };

      const response = await passwordPUT(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.error).toBe('Validation error');
    });
  });

  describe('POST /api/user/2fa', () => {
    it('should enable 2FA successfully', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({}),
      };

      (prisma.user.update as jest.Mock).mockResolvedValue({
        id: 'user-id',
        email: '<EMAIL>',
      });

      const response = await twoFAPOST(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('2FA enabled');
      expect(data.secret).toBeDefined();
      expect(data.qrCodeUrl).toMatch(/^data:image\/png;base64,/);
      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: 'user-id' },
        data: {
          twoFactorSecret: expect.any(String),
          twoFactorEnabled: true,
        },
      });
    });
  });

  describe('DELETE /api/user/2fa', () => {
    it('should disable 2FA successfully', async () => {
      const mockRequest = {
        json: jest.fn().mockResolvedValue({}),
      };

      (prisma.user.update as jest.Mock).mockResolvedValue({});

      const response = await twoFADELETE(mockRequest as any);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.message).toBe('2FA disabled');
      expect(prisma.user.update).toHaveBeenCalledWith({
        where: { id: 'user-id' },
        data: {
          twoFactorSecret: null,
          twoFactorEnabled: false,
        },
      });
    });
  });
});