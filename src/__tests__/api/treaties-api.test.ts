import { describe, it, expect, beforeAll, afterAll, beforeEach, afterEach } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import { createTestUser, deleteTestUser } from '../test-utils';

describe('Treaty API', () => {
  let prisma: PrismaClient;
  let testUser: any;
  let testTreatyType: any;
  let testTreaty: any;

  beforeAll(async () => {
    prisma = new PrismaClient();
    testUser = await createTestUser(prisma);
    
    // Create a test treaty type with a unique name
    const timestamp = Date.now();
    testTreatyType = await prisma.treatyType.create({
      data: {
        name: `Test Treaty Type ${timestamp}`,
        description: 'Test treaty type for API testing',
        category: 'TEST',
      },
    });
  });

  afterAll(async () => {
    if (testUser) {
      await deleteTestUser(prisma, testUser.id);
    }
    if (testTreatyType) {
      await prisma.treatyType.delete({ where: { id: testTreatyType.id } });
    }
    await prisma.$disconnect();
  });

  beforeEach(async () => {
    // Create a test treaty before each test
    testTreaty = await prisma.treaty.create({
      data: {
        userId: testUser.id,
        treatyTypeId: testTreatyType.id,
        status: 'ACTIVE',
        notes: 'Test treaty notes',
        signedDate: new Date(),
      },
    });
  });

  afterEach(async () => {
    // Delete the test treaty after each test
    if (testTreaty) {
      await prisma.treaty.delete({ where: { id: testTreaty.id } });
      testTreaty = null;
    }
  });

  it('should create a new treaty', async () => {
    const newTreatyData = {
      treatyTypeId: testTreatyType.id,
      notes: 'New test treaty notes',
      status: 'ACTIVE',
      signedDate: new Date().toISOString(),
    };

    // In a real test, we would make an actual API call here
    // For now, we'll test the database operation directly
    const treaty = await prisma.treaty.create({
      data: {
        userId: testUser.id,
        ...newTreatyData,
        signedDate: new Date(newTreatyData.signedDate),
      },
    });

    expect(treaty).toBeDefined();
    expect(treaty.userId).toBe(testUser.id);
    expect(treaty.treatyTypeId).toBe(testTreatyType.id);
    expect(treaty.notes).toBe(newTreatyData.notes);
    expect(treaty.status).toBe(newTreatyData.status);

    // Clean up
    await prisma.treaty.delete({ where: { id: treaty.id } });
  });

  it('should retrieve treaties for a user', async () => {
    // Create another treaty
    const anotherTreaty = await prisma.treaty.create({
      data: {
        userId: testUser.id,
        treatyTypeId: testTreatyType.id,
        status: 'EXPIRED',
        notes: 'Another test treaty',
        signedDate: new Date(),
        expirationDate: new Date(),
      },
    });

    // Retrieve treaties for test user
    const userTreaties = await prisma.treaty.findMany({
      where: {
        userId: testUser.id,
      },
      include: {
        treatyType: true,
      },
    });

    expect(userTreaties).toHaveLength(2);
    expect(userTreaties.some(t => t.id === testTreaty.id)).toBe(true);
    expect(userTreaties.some(t => t.id === anotherTreaty.id)).toBe(true);

    // Clean up
    await prisma.treaty.delete({ where: { id: anotherTreaty.id } });
  });

  it('should retrieve a specific treaty by ID', async () => {
    // Retrieve the specific treaty
    const treaty = await prisma.treaty.findUnique({
      where: {
        id: testTreaty.id,
      },
      include: {
        treatyType: true,
      },
    });

    expect(treaty).toBeDefined();
    expect(treaty?.id).toBe(testTreaty.id);
    expect(treaty?.userId).toBe(testUser.id);
    expect(treaty?.treatyTypeId).toBe(testTreatyType.id);
    expect(treaty?.notes).toBe('Test treaty notes');
    expect(treaty?.status).toBe('ACTIVE');
  });

  it('should update a treaty', async () => {
    // Update the treaty
    const updatedTreaty = await prisma.treaty.update({
      where: {
        id: testTreaty.id,
      },
      data: {
        status: 'EXPIRED',
        expirationDate: new Date(),
        notes: 'Updated treaty notes',
      },
    });

    expect(updatedTreaty.status).toBe('EXPIRED');
    expect(updatedTreaty.notes).toBe('Updated treaty notes');
    expect(updatedTreaty.expirationDate).toBeDefined();
  });

  it('should delete a treaty', async () => {
    // Delete the treaty
    await prisma.treaty.delete({
      where: {
        id: testTreaty.id,
      },
    });

    // Try to find the treaty
    const deletedTreaty = await prisma.treaty.findUnique({
      where: {
        id: testTreaty.id,
      },
    });

    expect(deletedTreaty).toBeNull();
    
    // Reset testTreaty so afterEach doesn't try to delete it again
    testTreaty = null;
  });
});