import { describe, it, expect, beforeAll, afterAll } from '@jest/globals';
import { PrismaClient } from '@prisma/client';
import { createTestUser, deleteTestUser } from '../test-utils';

describe('Ordinance Model', () => {
  let prisma: PrismaClient;
  let testUser: any;

  beforeAll(async () => {
    prisma = new PrismaClient();
    testUser = await createTestUser(prisma);
  });

  afterAll(async () => {
    if (testUser) {
      await deleteTestUser(prisma, testUser.id);
    }
    await prisma.$disconnect();
  });

  it('should create a new ordinance', async () => {
    // First, create an ordinance type with a unique name
    const timestamp = Date.now();
    const ordinanceType = await prisma.ordinanceType.create({
      data: {
        name: `Test Ordinance Type ${timestamp}`,
        description: 'Test ordinance type for testing',
        category: 'TEST',
      },
    });

    // Create an ordinance
    const ordinance = await prisma.ordinance.create({
      data: {
        userId: testUser.id,
        ordinanceTypeId: ordinanceType.id,
        status: 'PENDING',
        notes: 'Test ordinance notes',
      },
    });

    expect(ordinance).toBeDefined();
    expect(ordinance.userId).toBe(testUser.id);
    expect(ordinance.ordinanceTypeId).toBe(ordinanceType.id);
    expect(ordinance.status).toBe('PENDING');
    expect(ordinance.notes).toBe('Test ordinance notes');

    // Clean up
    await prisma.ordinance.delete({ where: { id: ordinance.id } });
    await prisma.ordinanceType.delete({ where: { id: ordinanceType.id } });
  });

  it('should retrieve ordinances for a user', async () => {
    // Create an ordinance type with a unique name
    const timestamp = Date.now();
    const ordinanceType = await prisma.ordinanceType.create({
      data: {
        name: `Test Ordinance Type ${timestamp}`,
        description: 'Test ordinance type for testing',
        category: 'TEST',
      },
    });

    // Create multiple ordinances
    const ordinance1 = await prisma.ordinance.create({
      data: {
        userId: testUser.id,
        ordinanceTypeId: ordinanceType.id,
        status: 'PENDING',
        notes: 'Test ordinance 1',
      },
    });

    const ordinance2 = await prisma.ordinance.create({
      data: {
        userId: testUser.id,
        ordinanceTypeId: ordinanceType.id,
        status: 'COMPLETED',
        notes: 'Test ordinance 2',
        completedDate: new Date(),
      },
    });

    // Create an ordinance for another user
    const otherUser = await createTestUser(prisma, '<EMAIL>');
    const ordinance3 = await prisma.ordinance.create({
      data: {
        userId: otherUser.id,
        ordinanceTypeId: ordinanceType.id,
        status: 'PENDING',
        notes: 'Test ordinance 3',
      },
    });

    // Retrieve ordinances for test user
    const userOrdinances = await prisma.ordinance.findMany({
      where: {
        userId: testUser.id,
      },
      include: {
        ordinanceType: true,
      },
    });

    expect(userOrdinances).toHaveLength(2);
    expect(userOrdinances[0].id).toBe(ordinance1.id);
    expect(userOrdinances[1].id).toBe(ordinance2.id);
    expect(userOrdinances.some(o => o.id === ordinance3.id)).toBe(false);

    // Clean up
    await prisma.ordinance.delete({ where: { id: ordinance1.id } });
    await prisma.ordinance.delete({ where: { id: ordinance2.id } });
    await prisma.ordinance.delete({ where: { id: ordinance3.id } });
    await prisma.ordinanceType.delete({ where: { id: ordinanceType.id } });
    await deleteTestUser(prisma, otherUser.id);
  });

  it('should update an ordinance', async () => {
    // Create an ordinance type with a unique name
    const timestamp = Date.now();
    const ordinanceType = await prisma.ordinanceType.create({
      data: {
        name: `Test Ordinance Type ${timestamp}`,
        description: 'Test ordinance type for testing',
        category: 'TEST',
      },
    });

    // Create an ordinance
    const ordinance = await prisma.ordinance.create({
      data: {
        userId: testUser.id,
        ordinanceTypeId: ordinanceType.id,
        status: 'PENDING',
        notes: 'Test ordinance notes',
      },
    });

    // Update the ordinance
    const updatedOrdinance = await prisma.ordinance.update({
      where: {
        id: ordinance.id,
      },
      data: {
        status: 'COMPLETED',
        completedDate: new Date(),
        notes: 'Updated ordinance notes',
      },
    });

    expect(updatedOrdinance.status).toBe('COMPLETED');
    expect(updatedOrdinance.notes).toBe('Updated ordinance notes');
    expect(updatedOrdinance.completedDate).toBeDefined();

    // Clean up
    await prisma.ordinance.delete({ where: { id: ordinance.id } });
    await prisma.ordinanceType.delete({ where: { id: ordinanceType.id } });
  });

  it('should delete an ordinance', async () => {
    // Create an ordinance type with a unique name
    const timestamp = Date.now();
    const ordinanceType = await prisma.ordinanceType.create({
      data: {
        name: `Test Ordinance Type ${timestamp}`,
        description: 'Test ordinance type for testing',
        category: 'TEST',
      },
    });

    // Create an ordinance
    const ordinance = await prisma.ordinance.create({
      data: {
        userId: testUser.id,
        ordinanceTypeId: ordinanceType.id,
        status: 'PENDING',
        notes: 'Test ordinance notes',
      },
    });

    // Delete the ordinance
    await prisma.ordinance.delete({
      where: {
        id: ordinance.id,
      },
    });

    // Try to find the ordinance
    const deletedOrdinance = await prisma.ordinance.findUnique({
      where: {
        id: ordinance.id,
      },
    });

    expect(deletedOrdinance).toBeNull();

    // Clean up
    await prisma.ordinanceType.delete({ where: { id: ordinanceType.id } });
  });
});