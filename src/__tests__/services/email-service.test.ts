import { EmailService } from '@/lib/services/email-service';

describe('EmailService', () => {
  let emailService: EmailService;

  beforeEach(() => {
    emailService = new EmailService();
  });

  describe('sendEmail', () => {
    it('should send an email successfully', async () => {
      // Mock the transporter
      const mockInfo = { messageId: 'test-message-id' };
      emailService['transporter'] = {
        verify: jest.fn().mockResolvedValue(true),
        sendMail: jest.fn().mockResolvedValue(mockInfo),
      } as any;

      const options = {
        to: '<EMAIL>',
        subject: 'Test Subject',
        text: 'Test Body',
      };

      await expect(emailService.sendEmail(options)).resolves.toBeUndefined();
      expect(emailService['transporter'].sendMail).toHaveBeenCalledWith({
        from: expect.any(String),
        to: '<EMAIL>',
        subject: 'Test Subject',
        text: 'Test Body',
        html: undefined,
      });
    });

    it('should handle email sending errors', async () => {
      // Mock the transporter to throw an error
      emailService['transporter'] = {
        verify: jest.fn().mockResolvedValue(true),
        sendMail: jest.fn().mockRejectedValue(new Error('SMTP Error')),
      } as any;

      const options = {
        to: '<EMAIL>',
        subject: 'Test Subject',
        text: 'Test Body',
      };

      await expect(emailService.sendEmail(options)).rejects.toThrow('Failed to send email');
    });
  });

  describe('sendVerificationEmail', () => {
    it('should send a verification email with the correct content', async () => {
      // Mock the transporter
      const mockInfo = { messageId: 'test-message-id' };
      emailService['transporter'] = {
        verify: jest.fn().mockResolvedValue(true),
        sendMail: jest.fn().mockResolvedValue(mockInfo),
      } as any;

      const email = '<EMAIL>';
      const token = 'test-token';

      await emailService.sendVerificationEmail(email, token);

      expect(emailService['transporter'].sendMail).toHaveBeenCalled();
      const callArgs = (emailService['transporter'].sendMail as jest.Mock).mock.calls[0][0];
      expect(callArgs.to).toBe(email);
      expect(callArgs.subject).toBe('Verify Your Email Address - NWA Portal');
      expect(callArgs.text).toContain(token);
      expect(callArgs.html).toContain(token);
    });
  });

  describe('sendPasswordResetEmail', () => {
    it('should send a password reset email with the correct content', async () => {
      // Mock the transporter
      const mockInfo = { messageId: 'test-message-id' };
      emailService['transporter'] = {
        verify: jest.fn().mockResolvedValue(true),
        sendMail: jest.fn().mockResolvedValue(mockInfo),
      } as any;

      const email = '<EMAIL>';
      const token = 'test-token';

      await emailService.sendPasswordResetEmail(email, token);

      expect(emailService['transporter'].sendMail).toHaveBeenCalled();
      const callArgs = (emailService['transporter'].sendMail as jest.Mock).mock.calls[0][0];
      expect(callArgs.to).toBe(email);
      expect(callArgs.subject).toBe('Password Reset - NWA Portal');
      expect(callArgs.text).toContain(token);
      expect(callArgs.html).toContain(token);
    });
  });
});