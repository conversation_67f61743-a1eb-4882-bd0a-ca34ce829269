import { PrismaClient } from '@prisma/client';

// Generate a unique email address using a timestamp
function generateUniqueEmail(baseEmail: string = '<EMAIL>'): string {
  const timestamp = Date.now();
  const random = Math.floor(Math.random() * 10000);
  const [username, domain] = baseEmail.split('@');
  return `${username}+${timestamp}${random}@${domain}`;
}

export async function createTestUser(prisma: PrismaClient, email: string = '<EMAIL>') {
  // Generate a unique email to avoid conflicts
  const uniqueEmail = generateUniqueEmail(email);
  
  // Create a test user
  const user = await prisma.user.create({
    data: {
      email: uniqueEmail,
      name: 'Test User',
      passwordHash: 'test-password-hash', // This is just for testing, not a real hash
    },
  });

  return user;
}

export async function deleteTestUser(prisma: PrismaClient, userId: string) {
  // Delete the test user
  await prisma.user.delete({
    where: {
      id: userId,
    },
  });
}