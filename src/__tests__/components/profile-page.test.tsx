import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { useSession } from 'next-auth/react';
import ProfilePage from '@/app/profile/page';
import * as nextNavigation from 'next/navigation';
import * as sonner from 'sonner';

// Mock next-auth
jest.mock('next-auth/react', () => ({
  useSession: jest.fn(),
  signIn: jest.fn(),
  signOut: jest.fn(),
}));

// Mock next/navigation
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}));

// Mock sonner
jest.mock('sonner', () => ({
  toast: {
    success: jest.fn(),
    error: jest.fn(),
  },
}));

// Mock fetch
global.fetch = jest.fn();

describe('ProfilePage', () => {
  const mockSession = {
    status: 'authenticated',
    data: {
      user: {
        id: 'user-id',
        name: 'Test User',
        email: '<EMAIL>',
      },
    },
  };

  const mockRouter = {
    push: jest.fn(),
    refresh: jest.fn(),
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (useSession as jest.Mock).mockReturnValue(mockSession);
    (nextNavigation.useRouter as jest.Mock).mockReturnValue(mockRouter);
    (nextNavigation.useSearchParams as jest.Mock).mockReturnValue({
      get: jest.fn().mockReturnValue(null),
    });
    (global.fetch as jest.Mock).mockImplementation((url, options) => {
      if (url === '/api/user') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            id: 'user-id',
            name: 'Test User',
            email: '<EMAIL>',
            nwaEmail: '<EMAIL>',
            bio: 'Test bio',
            phone: '+1234567890',
            dateOfBirth: '1990-01-01',
            country: 'USA',
            city: 'New York',
            image: null,
            twoFactorEnabled: false,
          }),
        });
      }
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ message: 'Success' }),
      });
    });
  });

  it('should render loading state initially', () => {
    (useSession as jest.Mock).mockReturnValue({
      status: 'loading',
    });

    render(<ProfilePage />);
    
    expect(screen.getByText(/loading/i)).toBeInTheDocument();
  });

  it('should render profile form when authenticated', async () => {
    render(<ProfilePage />);
    
    // Wait for the profile data to load
    await waitFor(() => {
      expect(screen.getByLabelText(/full name/i)).toBeInTheDocument();
    });
    
    expect(screen.getByLabelText(/full name/i)).toHaveValue('Test User');
    expect(screen.getByLabelText(/email/i)).toHaveValue('<EMAIL>');
    expect(screen.getByLabelText(/nwa email/i)).toHaveValue('<EMAIL>');
    expect(screen.getByLabelText(/bio/i)).toHaveValue('Test bio');
  });

  it('should handle form input changes', async () => {
    render(<ProfilePage />);
    
    await waitFor(() => {
      expect(screen.getByLabelText(/full name/i)).toBeInTheDocument();
    });
    
    const nameInput = screen.getByLabelText(/full name/i);
    fireEvent.change(nameInput, { target: { value: 'Updated Name' } });
    
    expect(nameInput).toHaveValue('Updated Name');
  });

  it('should submit profile updates successfully', async () => {
    render(<ProfilePage />);
    
    await waitFor(() => {
      expect(screen.getByLabelText(/full name/i)).toBeInTheDocument();
    });
    
    const saveButton = screen.getByRole('button', { name: /save changes/i });
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(sonner.toast.success).toHaveBeenCalledWith('Profile updated successfully');
    });
  });

  it('should handle password updates', async () => {
    render(<ProfilePage />);
    
    await waitFor(() => {
      expect(screen.getByLabelText(/current password/i)).toBeInTheDocument();
    });
    
    const currentPasswordInput = screen.getByLabelText(/current password/i);
    const newPasswordInput = screen.getByLabelText(/new password/i);
    const confirmPasswordInput = screen.getByLabelText(/confirm new password/i);
    
    fireEvent.change(currentPasswordInput, { target: { value: 'oldpassword' } });
    fireEvent.change(newPasswordInput, { target: { value: 'newpassword123' } });
    fireEvent.change(confirmPasswordInput, { target: { value: 'newpassword123' } });
    
    const saveButton = screen.getByRole('button', { name: /save changes/i });
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(sonner.toast.success).toHaveBeenCalledWith('Password updated successfully');
    });
  });

  it('should enable 2FA successfully', async () => {
    (global.fetch as jest.Mock).mockImplementation((url, options) => {
      if (url === '/api/user/2fa' && options.method === 'POST') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            message: '2FA enabled',
            secret: 'test-secret',
            qrCodeUrl: 'data:image/png;base64,test',
          }),
        });
      }
      if (url === '/api/user') {
        return Promise.resolve({
          ok: true,
          json: () => Promise.resolve({
            id: 'user-id',
            name: 'Test User',
            email: '<EMAIL>',
            nwaEmail: '<EMAIL>',
            bio: 'Test bio',
            phone: '+1234567890',
            dateOfBirth: '1990-01-01',
            country: 'USA',
            city: 'New York',
            image: null,
            twoFactorEnabled: false,
          }),
        });
      }
      return Promise.resolve({
        ok: true,
        json: () => Promise.resolve({ message: 'Success' }),
      });
    });

    render(<ProfilePage />);
    
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /enable/i })).toBeInTheDocument();
    });
    
    const enable2FAButton = screen.getByRole('button', { name: /enable/i });
    fireEvent.click(enable2FAButton);
    
    await waitFor(() => {
      expect(sonner.toast.success).toHaveBeenCalledWith('2FA enabled! Scan the QR code with your authenticator app.');
    });
  });

  it('should handle API errors gracefully', async () => {
    (global.fetch as jest.Mock).mockImplementation((url, options) => {
      return Promise.resolve({
        ok: false,
        json: () => Promise.resolve({ error: 'Something went wrong' }),
      });
    });

    render(<ProfilePage />);
    
    const saveButton = screen.getByRole('button', { name: /save changes/i });
    fireEvent.click(saveButton);
    
    await waitFor(() => {
      expect(sonner.toast.error).toHaveBeenCalledWith('Failed to update profile');
    });
  });
});