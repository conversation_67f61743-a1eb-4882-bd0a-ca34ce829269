'use client';

import React from 'react';
import { useSession } from 'next-auth/react';

export default function UserProfile() {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return <div>Loading...</div>;
  }

  if (status === "unauthenticated") {
    return <div>You are not logged in</div>;
  }

  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
      <h2 className="text-xl font-semibold text-gray-800 dark:text-white mb-4">
        User Profile
      </h2>
      <div className="space-y-3">
        <div>
          <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Name
          </label>
          <p className="text-gray-900 dark:text-white">
            {session?.user?.name || 'N/A'}
          </p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
            Email
          </label>
          <p className="text-gray-900 dark:text-white">
            {session?.user?.email || 'N/A'}
          </p>
        </div>
        <div>
          <label className="text-sm font-medium text-gray-500 dark:text-gray-400">
            NWA Email
          </label>
          <p className="text-gray-900 dark:text-white">
            {(session?.user as any)?.nwaEmail || 'N/A'}
          </p>
        </div>
      </div>
    </div>
  );
}