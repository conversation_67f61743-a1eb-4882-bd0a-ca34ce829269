'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import {
  Server,
  Plus,
  Trash2,
  Save,
  TestTube,
  AlertCircle,
  RefreshCw,
  Users,
  Key,
  Eye,
  EyeOff
} from 'lucide-react'
import {
  getAllRemoteServers,
  getRemoteServerById,
  updateRemoteServerConfig,
  deleteRemoteServerById
} from '@/app/actions/remote-servers-oauth'
import {
  fetchRemoteServerPermissions,
  assignUserPermissions,
  removeUserPermissions,
  getUserPermissions
} from '@/app/actions/remote-servers'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import { ApiKeyDisplay } from '@/components/ui/api-key-display'

interface RemoteServer {
  id: string
  name: string
  url: string
  apiKey: string
  description: string | null
  isActive: boolean
  createdAt: Date
  updatedAt: Date
  clientId: string | null
  clientSecret: string | null
  redirectUris: string[] | null
}

interface Permission {
  name: string
  description: string
}

interface Role {
  name: string
  description: string
  permissions: string[]
}

export function RemoteServersTab() {
  const [servers, setServers] = useState<RemoteServer[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [newServer, setNewServer] = useState<Omit<RemoteServer, 'id' | 'apiKey' | 'isActive' | 'createdAt' | 'updatedAt' | 'clientId' | 'clientSecret' | 'redirectUris'>>({
    name: '',
    url: '',
    description: null
  })
  const [editingServerId, setEditingServerId] = useState<string | null>(null)
  const [editServerData, setEditServerData] = useState<Partial<RemoteServer>>({})
  const [serverPermissions, setServerPermissions] = useState<Record<string, { permissions: Permission[]; roles: Role[] }>>({})
  const [permissionsLoading, setPermissionsLoading] = useState<Record<string, boolean>>({})
  const [showClientSecret, setShowClientSecret] = useState<Record<string, boolean>>({})
  const [showApiKey, setShowApiKey] = useState<Record<string, boolean>>({})

  useEffect(() => {
    fetchServers()
  }, [])

  const fetchServers = async () => {
    try {
      setLoading(true)
      console.log('Fetching servers...')
      const result = await getAllRemoteServers()
      console.log('Server fetch result:', result)
      
      if (result.success && result.data) {
        console.log('Raw server data:', result.data)
        console.log('Number of servers found:', result.data.length)
        
        // Convert string dates to Date objects
        const serversWithDates = result.data.map((server: any) => ({
          ...server,
          createdAt: new Date(server.createdAt),
          updatedAt: new Date(server.updatedAt)
        }))
        console.log('Processed server data:', serversWithDates)
        setServers(serversWithDates)
        setError(null)
      } else {
        console.error('Failed to fetch servers:', result.error)
        setError(result.error || 'Failed to fetch servers')
      }
    } catch (err) {
      console.error('Exception in fetchServers:', err)
      setError('An unexpected error occurred')
    } finally {
      setLoading(false)
    }
  }

  const handleAddServer = async () => {
    if (!newServer.name || !newServer.url) {
      setError('Please fill in all required fields')
      return
    }

    try {
      // For now, we'll just refresh the list since we don't have a create endpoint yet
      // In a real implementation, we would call a create action
      setError(null)
      fetchServers() // Refresh the list
    } catch (err) {
      setError('An unexpected error occurred')
    }
  }

  const handleUpdateServer = async (id: string) => {
    try {
      // Convert null values to undefined for the API
      const updateData = {
        ...editServerData,
        description: editServerData.description === null ? undefined : editServerData.description,
        clientId: editServerData.clientId === null ? undefined : editServerData.clientId,
        clientSecret: editServerData.clientSecret === null ? undefined : editServerData.clientSecret,
        redirectUris: editServerData.redirectUris === null ? undefined : editServerData.redirectUris
      }

      const result = await updateRemoteServerConfig(id, updateData)
      if (result.success) {
        setEditingServerId(null)
        setEditServerData({})
        setError(null)
        fetchServers() // Refresh the list
      } else {
        setError(result.error || 'Failed to update server')
      }
    } catch (err) {
      setError('An unexpected error occurred')
    }
  }

  const handleRemoveServer = async (id: string) => {
    try {
      const result = await deleteRemoteServerById(id)
      if (result.success) {
        setError(null)
        fetchServers() // Refresh the list
      } else {
        setError(result.error || 'Failed to delete server')
      }
    } catch (err) {
      setError('An unexpected error occurred')
    }
  }

  const handleTestConnection = async (url: string, apiKey: string) => {
    try {
      // In a real implementation, we would test the connection here
      // For now, we'll simulate a successful connection
      console.log(`Testing connection to ${url}`)
      
      // Simulate network delay
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      alert(`Connection successful to ${url}`)
    } catch (err) {
      alert('An unexpected error occurred while testing connection')
    }
  }

  const handleFetchPermissions = async (serverId: string) => {
    try {
      console.log('=== FETCHING PERMISSIONS FROM FRONTEND ===')
      console.log('Server ID:', serverId)
      
      // Set loading state for this server
      setPermissionsLoading(prev => ({ ...prev, [serverId]: true }))

      const result = await fetchRemoteServerPermissions(serverId)
      console.log('Fetch permissions result:', result)
      
      if (result.success && result.data) {
        setServerPermissions(prev => ({
          ...prev,
          [serverId]: result.data
        }))
        alert('Permissions fetched successfully')
      } else {
        alert(`Failed to fetch permissions: ${result.error}`)
      }
    } catch (err) {
      alert('An unexpected error occurred while fetching permissions')
    } finally {
      // Clear loading state for this server
      setPermissionsLoading(prev => ({ ...prev, [serverId]: false }))
    }
  }

  const toggleClientSecretVisibility = (serverId: string) => {
    setShowClientSecret(prev => ({
      ...prev,
      [serverId]: !prev[serverId]
    }))
  }

  const toggleApiKeyVisibility = (serverId: string) => {
    setShowApiKey(prev => ({
      ...prev,
      [serverId]: !prev[serverId]
    }))
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading remote servers...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Remote Server Configuration</h2>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Plus className="w-5 h-5 mr-2 text-green-500" />
            Add New Server
          </CardTitle>
          <p className="text-sm text-gray-500">
            Fill out the form below to add a new remote server. Once saved, it will appear in the list below.
          </p>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="server-name" className="text-sm font-medium text-slate-800" style={{ color: '#1e293b' }}>Server Name *</Label>
            <Input
              id="server-name"
              placeholder="Enter server name"
              value={newServer.name}
              onChange={(e) => setNewServer({ ...newServer, name: e.target.value })}
              style={{ color: '#0f172a' }}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="server-url" className="text-sm font-medium text-slate-800" style={{ color: '#1e293b' }}>Server URL *</Label>
            <Input
              id="server-url"
              placeholder="https://example.com"
              value={newServer.url}
              onChange={(e) => setNewServer({ ...newServer, url: e.target.value })}
              style={{ color: '#0f172a' }}
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="description" className="text-sm font-medium text-slate-800" style={{ color: '#1e293b' }}>Description</Label>
            <Textarea
              id="description"
              placeholder="Enter a description for this server"
              value={newServer.description || ''}
              onChange={(e) => setNewServer({ ...newServer, description: e.target.value || null })}
              style={{ color: '#0f172a' }}
            />
          </div>

          <div className="flex justify-end">
            <Button onClick={handleAddServer}>
              <Save className="w-4 h-4 mr-2" />
              Save Server
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <h3 className="text-xl font-semibold">Configured Servers</h3>
          <span className="text-sm text-gray-500">{servers.length} server(s) configured</span>
        </div>
        {servers.length === 0 ? (
          <div className="text-center py-8 border-2 border-dashed border-gray-200 rounded-lg">
            <Server className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No servers configured</h3>
            <p className="mt-1 text-sm text-gray-500">
              Get started by adding a new remote server above.
            </p>
          </div>
        ) : (
          servers.map((server) => (
            <Card key={server.id} className="border border-border">
              <CardHeader>
                <div className="flex justify-between items-center">
                  <CardTitle className="flex items-center">
                    <Server className="w-5 h-5 mr-2" />
                    {editingServerId === server.id ? (
                      <Input
                        value={editServerData.name || server.name}
                        onChange={(e) => setEditServerData({ ...editServerData, name: e.target.value })}
                      />
                    ) : (
                      server.name
                    )}
                  </CardTitle>
                  <div className="flex flex-wrap gap-2">
                    {editingServerId === server.id ? (
                      <Button
                        size="sm"
                        onClick={() => handleUpdateServer(server.id)}
                      >
                        Save
                      </Button>
                    ) : (
                      <>
                        <Button
                          size="sm"
                          onClick={() => handleFetchPermissions(server.id)}
                          disabled={permissionsLoading[server.id]}
                        >
                          <RefreshCw className={`w-4 h-4 mr-1 ${permissionsLoading[server.id] ? 'animate-spin' : ''}`} />
                          {permissionsLoading[server.id] ? 'Fetching...' : 'Get Permissions'}
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => {
                            setEditingServerId(server.id)
                            setEditServerData({
                              name: server.name,
                              url: server.url,
                              description: server.description || '',
                              isActive: server.isActive
                            })
                          }}
                        >
                          Edit
                        </Button>
                        <Button
                          size="sm"
                          onClick={() => handleRemoveServer(server.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-slate-800" style={{ color: '#1e293b' }}>URL</Label>
                    {editingServerId === server.id ? (
                      <Input
                        value={editServerData.url || server.url}
                        onChange={(e) => setEditServerData({ ...editServerData, url: e.target.value })}
                      />
                    ) : (
                      <p className="font-mono text-sm break-all" style={{ color: '#0f172a' }}>{server.url}</p>
                    )}
                  </div>
                  <div>
                    <Label className="text-sm font-medium text-slate-800" style={{ color: '#1e293b' }}>API Key</Label>
                    {editingServerId === server.id ? (
                      <div className="flex space-x-2">
                        <Input
                          type={showApiKey[server.id] ? "text" : "password"}
                          value={editServerData.apiKey || server.apiKey || ''}
                          onChange={(e) => setEditServerData({ ...editServerData, apiKey: e.target.value })}
                          className="flex-1 font-mono text-sm"
                          style={{ color: '#0f172a' }}
                        />
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => toggleApiKeyVisibility(server.id)}
                          className="flex-shrink-0"
                        >
                          {showApiKey[server.id] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                        </Button>
                      </div>
                    ) : (
                      <p className="font-mono text-sm break-all" style={{ color: '#0f172a' }}>****************************************</p>
                    )}
                  </div>
                  
                  {/* OAuth Configuration Section */}
                  <div className="lg:col-span-2 mt-4 pt-4 border-t border-gray-200">
                    <div className="flex items-center mb-3">
                      <Key className="w-5 h-5 mr-2 text-blue-500" />
                      <h4 className="text-lg font-medium">OAuth Configuration</h4>
                    </div>
                    
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                      <div>
                        <Label className="text-sm font-medium text-slate-800" style={{ color: '#1e293b' }}>Client ID</Label>
                        {editingServerId === server.id ? (
                          <Input
                            value={editServerData.clientId || server.clientId || ''}
                            onChange={(e) => setEditServerData({ ...editServerData, clientId: e.target.value })}
                            style={{ color: '#0f172a' }}
                          />
                        ) : (
                          <p className="font-mono text-sm break-all" style={{ color: '#0f172a' }}>
                            {server.clientId || 'Not configured'}
                          </p>
                        )}
                      </div>
                      
                      <div>
                        <Label className="text-sm font-medium text-slate-800" style={{ color: '#1e293b' }}>Client Secret</Label>
                        {editingServerId === server.id ? (
                          <div className="flex space-x-2">
                            <Input
                              type={showClientSecret[server.id] ? "text" : "password"}
                              value={editServerData.clientSecret || server.clientSecret || ''}
                              onChange={(e) => setEditServerData({ ...editServerData, clientSecret: e.target.value })}
                              className="flex-1 font-mono text-sm"
                              style={{ color: '#0f172a' }}
                            />
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => toggleClientSecretVisibility(server.id)}
                              className="flex-shrink-0"
                            >
                              {showClientSecret[server.id] ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                            </Button>
                          </div>
                        ) : (
                          <p className="font-mono text-sm break-all" style={{ color: '#0f172a' }}>
                            {server.clientSecret ? '****************************************' : 'Not configured'}
                          </p>
                        )}
                      </div>
                      
                      <div className="lg:col-span-2">
                        <Label className="text-sm font-medium text-slate-800" style={{ color: '#1e293b' }}>Callback URL</Label>
                        {editingServerId === server.id ? (
                          <Input
                            value={(editServerData.redirectUris && editServerData.redirectUris.length > 0) ? editServerData.redirectUris[0] : (server.redirectUris && server.redirectUris.length > 0 ? server.redirectUris[0] : '')}
                            onChange={(e) => setEditServerData({ ...editServerData, redirectUris: e.target.value ? [e.target.value] : [] })}
                            placeholder="Enter callback URL (e.g., https://example.com/oauth/callback)"
                            style={{ color: '#0f172a' }}
                          />
                        ) : (
                          <p className="font-mono text-sm break-all" style={{ color: '#0f172a' }}>
                            {(server.redirectUris && server.redirectUris.length > 0) ? server.redirectUris[0] : 'Not configured'}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="md:col-span-2">
                    <Label className="text-sm font-medium text-slate-800" style={{ color: '#1e293b' }}>Description</Label>
                    {editingServerId === server.id ? (
                      <Textarea
                        value={editServerData.description || server.description || ''}
                        onChange={(e) => setEditServerData({ ...editServerData, description: e.target.value })}
                        style={{ color: '#0f172a' }}
                      />
                    ) : (
                      <p style={{ color: '#0f172a' }}>{server.description || 'No description provided'}</p>
                    )}
                  </div>
                  <div className="md:col-span-2">
                    <Label className="text-sm font-medium text-slate-800" style={{ color: '#1e293b' }}>Status</Label>
                    {editingServerId === server.id ? (
                      <div className="flex items-center">
                        <input
                          type="checkbox"
                          id={`active-${server.id}`}
                          checked={editServerData.isActive ?? server.isActive}
                          onChange={(e) => setEditServerData({ ...editServerData, isActive: e.target.checked })}
                          className="mr-2"
                        />
                        <label htmlFor={`active-${server.id}`}>
                          {editServerData.isActive ?? server.isActive ? 'Active' : 'Inactive'}
                        </label>
                      </div>
                    ) : (
                      <p className={server.isActive ? "text-green-600" : "text-red-600"}>
                        {server.isActive ? "Active" : "Inactive"}
                      </p>
                    )}
                  </div>

                  {/* Permissions Section */}
                  <div className="md:col-span-2 mt-4 pt-4 border-t border-gray-200">
                    <div className="flex items-center mb-3">
                      <Key className="w-5 h-5 mr-2 text-blue-500" />
                      <h4 className="text-lg font-medium">Permissions & Roles</h4>
                    </div>

                    {serverPermissions[server.id] ? (
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                        <div>
                          <h5 className="font-medium mb-2 text-slate-800" style={{ color: '#1e293b' }}>Available Permissions</h5>
                          <ul className="space-y-1">
                            {serverPermissions[server.id].permissions.map((permission) => (
                              <li key={permission.name} className="text-sm">
                                <span className="font-mono bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                                  {permission.name}
                                </span>
                                <span className="text-gray-500 ml-2">{permission.description}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                        <div>
                          <h5 className="font-medium mb-2 text-slate-800" style={{ color: '#1e293b' }}>Available Roles</h5>
                          <ul className="space-y-2">
                            {serverPermissions[server.id].roles.map((role) => (
                              <li key={role.name} className="border border-gray-200 dark:border-gray-700 rounded p-2">
                                <div className="font-medium">{role.name}</div>
                                <div className="text-sm text-gray-500">{role.description}</div>
                                <div className="text-xs mt-1">
                                  <span className="text-gray-500">Permissions:</span>
                                  <span className="ml-1">
                                    {role.permissions.join(', ')}
                                  </span>
                                </div>
                              </li>
                            ))}
                          </ul>
                        </div>
                      </div>
                    ) : (
                      <p className="text-gray-500 text-sm">
                        Click &quot;Get Permissions&quot; to fetch available permissions and roles from this remote server.
                      </p>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  )
}