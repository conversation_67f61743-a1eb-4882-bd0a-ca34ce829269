'use client'

import React, { useState, useEffect } from 'react'
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import {
  Shield,
  Save,
  AlertCircle,
  Key,
  Lock,
  Timer,
  Network
} from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import {
  getSystemSettings,
  updateSystemSettings
} from '@/app/actions/system-security'

interface SystemSettings {
  id: string
  maxLoginAttempts: number
  lockoutDuration: number
  sessionTimeout: number
  passwordMinLength: number
  passwordRequireUppercase: boolean
  passwordRequireLowercase: boolean
  passwordRequireNumbers: boolean
  passwordRequireSpecialChars: boolean
  twoFactorAuthRequired: boolean
  ipWhitelistEnabled: boolean
  ipWhitelist: string
  auditLogRetention: number
  apiRateLimit: number
  corsOrigins: string
  emailVerificationRequired: boolean
  passwordResetTokenExpiry: number
  createdAt: Date
  updatedAt: Date
}

export function SystemSettingsTab() {
  const [settings, setSettings] = useState<SystemSettings | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)

  useEffect(() => {
    fetchSystemSettings()
  }, [])

  const fetchSystemSettings = async () => {
    try {
      setLoading(true)

      const result = await getSystemSettings()

      if (result.success && result.data) {
        // Convert timestamp strings to Date objects
        const settingsData = {
          ...result.data,
          createdAt: new Date(result.data.createdAt),
          updatedAt: new Date(result.data.updatedAt)
        }
        setSettings(settingsData)
        setError(null)
      } else {
        setError(result.error || 'Failed to fetch system settings')
      }
    } catch (err) {
      setError('Failed to fetch system settings')
    } finally {
      setLoading(false)
    }
  }

  const handleSaveSettings = async () => {
    if (!settings) return

    try {
      setSaving(true)
      setSuccess(null)
      setError(null)

      const result = await updateSystemSettings({
        maxLoginAttempts: settings.maxLoginAttempts,
        lockoutDuration: settings.lockoutDuration,
        sessionTimeout: settings.sessionTimeout,
        passwordMinLength: settings.passwordMinLength,
        passwordRequireUppercase: settings.passwordRequireUppercase,
        passwordRequireLowercase: settings.passwordRequireLowercase,
        passwordRequireNumbers: settings.passwordRequireNumbers,
        passwordRequireSpecialChars: settings.passwordRequireSpecialChars,
        twoFactorAuthRequired: settings.twoFactorAuthRequired,
        ipWhitelistEnabled: settings.ipWhitelistEnabled,
        ipWhitelist: settings.ipWhitelist,
        auditLogRetention: settings.auditLogRetention,
        apiRateLimit: settings.apiRateLimit,
        corsOrigins: settings.corsOrigins,
        emailVerificationRequired: settings.emailVerificationRequired,
        passwordResetTokenExpiry: settings.passwordResetTokenExpiry
      })

      if (result.success && result.data) {
        // Update local state with saved data
        const updatedSettings = {
          ...result.data,
          createdAt: new Date(result.data.createdAt),
          updatedAt: new Date(result.data.updatedAt)
        }
        setSettings(updatedSettings)
        setSuccess('System settings saved successfully')

        // Clear success message after 3 seconds
        setTimeout(() => setSuccess(null), 3000)
      } else {
        setError(result.error || 'Failed to save system settings')
      }
    } catch (err) {
      setError('Failed to save system settings')
    } finally {
      setSaving(false)
    }
  }

  const handleInputChange = (field: keyof SystemSettings, value: string | number | boolean) => {
    if (!settings) return

    setSettings({
      ...settings,
      [field]: value
    })
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading system settings...</p>
        </div>
      </div>
    )
  }

  if (!settings) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="mx-auto h-12 w-12 text-gray-400" />
        <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No settings found</h3>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Unable to load system settings.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {success && (
        <Alert variant="default" className="bg-green-50 dark:bg-green-900/30 border-green-200 dark:border-green-800">
          <AlertCircle className="h-4 w-4 text-green-600 dark:text-green-300" />
          <AlertTitle className="text-green-800 dark:text-green-200">Success</AlertTitle>
          <AlertDescription className="text-green-700 dark:text-green-300">{success}</AlertDescription>
        </Alert>
      )}

      <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
        {/* Authentication Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Lock className="w-5 h-5 mr-2 text-blue-500" />
              Authentication Security
            </CardTitle>
            <p className="text-sm text-gray-500">
              Configure login security and authentication policies
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="maxLoginAttempts">Maximum Login Attempts</Label>
              <Input
                id="maxLoginAttempts"
                type="number"
                min="1"
                max="20"
                value={settings.maxLoginAttempts}
                onChange={(e) => handleInputChange('maxLoginAttempts', parseInt(e.target.value) || 5)}
              />
              <p className="text-xs text-gray-500">
                Number of failed login attempts before account lockout
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="lockoutDuration">Lockout Duration (minutes)</Label>
              <Input
                id="lockoutDuration"
                type="number"
                min="1"
                max="1440"
                value={settings.lockoutDuration}
                onChange={(e) => handleInputChange('lockoutDuration', parseInt(e.target.value) || 30)}
              />
              <p className="text-xs text-gray-500">
                Duration for which accounts are locked after failed attempts
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="sessionTimeout">Session Timeout (minutes)</Label>
              <Input
                id="sessionTimeout"
                type="number"
                min="1"
                max="1440"
                value={settings.sessionTimeout}
                onChange={(e) => handleInputChange('sessionTimeout', parseInt(e.target.value) || 60)}
              />
              <p className="text-xs text-gray-500">
                Time after which inactive sessions expire
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="twoFactorAuthRequired">Require Two-Factor Authentication</Label>
                <p className="text-xs text-gray-500">
                  Enforce 2FA for all users
                </p>
              </div>
              <Switch
                id="twoFactorAuthRequired"
                checked={settings.twoFactorAuthRequired}
                onCheckedChange={(checked) => handleInputChange('twoFactorAuthRequired', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="emailVerificationRequired">Require Email Verification</Label>
                <p className="text-xs text-gray-500">
                  Require users to verify email addresses
                </p>
              </div>
              <Switch
                id="emailVerificationRequired"
                checked={settings.emailVerificationRequired}
                onCheckedChange={(checked) => handleInputChange('emailVerificationRequired', checked)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Password Policy */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Key className="w-5 h-5 mr-2 text-green-500" />
              Password Policy
            </CardTitle>
            <p className="text-sm text-gray-500">
              Configure password strength requirements
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="passwordMinLength">Minimum Password Length</Label>
              <Input
                id="passwordMinLength"
                type="number"
                min="4"
                max="128"
                value={settings.passwordMinLength}
                onChange={(e) => handleInputChange('passwordMinLength', parseInt(e.target.value) || 8)}
              />
              <p className="text-xs text-gray-500">
                Minimum number of characters required for passwords
              </p>
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="passwordRequireUppercase">Require Uppercase Letters</Label>
                <p className="text-xs text-gray-500">
                  Passwords must contain uppercase letters (A-Z)
                </p>
              </div>
              <Switch
                id="passwordRequireUppercase"
                checked={settings.passwordRequireUppercase}
                onCheckedChange={(checked) => handleInputChange('passwordRequireUppercase', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="passwordRequireLowercase">Require Lowercase Letters</Label>
                <p className="text-xs text-gray-500">
                  Passwords must contain lowercase letters (a-z)
                </p>
              </div>
              <Switch
                id="passwordRequireLowercase"
                checked={settings.passwordRequireLowercase}
                onCheckedChange={(checked) => handleInputChange('passwordRequireLowercase', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="passwordRequireNumbers">Require Numbers</Label>
                <p className="text-xs text-gray-500">
                  Passwords must contain numbers (0-9)
                </p>
              </div>
              <Switch
                id="passwordRequireNumbers"
                checked={settings.passwordRequireNumbers}
                onCheckedChange={(checked) => handleInputChange('passwordRequireNumbers', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="passwordRequireSpecialChars">Require Special Characters</Label>
                <p className="text-xs text-gray-500">
                  Passwords must contain special characters (!@#$%^&*)
                </p>
              </div>
              <Switch
                id="passwordRequireSpecialChars"
                checked={settings.passwordRequireSpecialChars}
                onCheckedChange={(checked) => handleInputChange('passwordRequireSpecialChars', checked)}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="passwordResetTokenExpiry">Password Reset Token Expiry (hours)</Label>
              <Input
                id="passwordResetTokenExpiry"
                type="number"
                min="1"
                max="168"
                value={settings.passwordResetTokenExpiry}
                onChange={(e) => handleInputChange('passwordResetTokenExpiry', parseInt(e.target.value) || 24)}
              />
              <p className="text-xs text-gray-500">
                Time after which password reset tokens expire
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Network Security */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Network className="w-5 h-5 mr-2 text-purple-500" />
              Network Security
            </CardTitle>
            <p className="text-sm text-gray-500">
              Configure network-level security settings
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="ipWhitelistEnabled">Enable IP Whitelisting</Label>
                <p className="text-xs text-gray-500">
                  Restrict access to specific IP addresses
                </p>
              </div>
              <Switch
                id="ipWhitelistEnabled"
                checked={settings.ipWhitelistEnabled}
                onCheckedChange={(checked) => handleInputChange('ipWhitelistEnabled', checked)}
              />
            </div>

            {settings.ipWhitelistEnabled && (
              <div className="space-y-2">
                <Label htmlFor="ipWhitelist">IP Whitelist</Label>
                <Textarea
                  id="ipWhitelist"
                  placeholder="Enter IP addresses, one per line&#10;*************&#10;10.0.0.0/8"
                  value={settings.ipWhitelist}
                  onChange={(e) => handleInputChange('ipWhitelist', e.target.value)}
                  rows={4}
                />
                <p className="text-xs text-gray-500">
                  Enter allowed IP addresses or CIDR blocks, one per line
                </p>
              </div>
            )}

            <div className="space-y-2">
              <Label htmlFor="corsOrigins">Allowed CORS Origins</Label>
              <Textarea
                id="corsOrigins"
                placeholder="https://example.com&#10;https://app.example.com"
                value={settings.corsOrigins}
                onChange={(e) => handleInputChange('corsOrigins', e.target.value)}
                rows={3}
              />
              <p className="text-xs text-gray-500">
                Enter allowed origins for CORS requests, one per line
              </p>
            </div>
          </CardContent>
        </Card>

        {/* API & Logging */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Timer className="w-5 h-5 mr-2 text-orange-500" />
              API & Logging
            </CardTitle>
            <p className="text-sm text-gray-500">
              Configure API limits and audit logging
            </p>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="apiRateLimit">API Rate Limit (requests/hour)</Label>
              <Input
                id="apiRateLimit"
                type="number"
                min="1"
                max="10000"
                value={settings.apiRateLimit}
                onChange={(e) => handleInputChange('apiRateLimit', parseInt(e.target.value) || 100)}
              />
              <p className="text-xs text-gray-500">
                Maximum API requests allowed per hour per user
              </p>
            </div>

            <div className="space-y-2">
              <Label htmlFor="auditLogRetention">Audit Log Retention (days)</Label>
              <Input
                id="auditLogRetention"
                type="number"
                min="1"
                max="3650"
                value={settings.auditLogRetention}
                onChange={(e) => handleInputChange('auditLogRetention', parseInt(e.target.value) || 90)}
              />
              <p className="text-xs text-gray-500">
                Number of days to retain audit logs
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      <div className="flex justify-end">
        <Button onClick={handleSaveSettings} disabled={saving}>
          <Save className="w-4 h-4 mr-2" />
          {saving ? 'Saving...' : 'Save Settings'}
        </Button>
      </div>
    </div>
  )
}