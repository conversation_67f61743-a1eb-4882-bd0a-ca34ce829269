'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Activity,
  Search,
  Filter,
  Download,
  AlertCircle,
  CheckCircle,
  XCircle,
  Clock,
  Server,
  User
} from 'lucide-react'
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert'
import {
  getAuditLogs,
  exportAuditLogs
} from '@/app/actions/system-security'

interface AuditLog {
  id: string
  userId: string | null
  action: string
  resource: string
  resourceId: string | null
  timestamp: Date
  success: boolean
  statusCode: number | null
  errorMessage: string | null
  ipAddress: string | null
  userAgent: string | null
  projectId: string | null
  remoteServerId?: string | null
  duration: number | null
}

export function SecurityAuditTab() {
  const [auditLogs, setAuditLogs] = useState<AuditLog[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [filter, setFilter] = useState({
    search: '',
    action: '',
    resource: '',
    startDate: '',
    endDate: '',
    success: 'all'
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [logsPerPage] = useState(10)

  const fetchAuditLogs = useCallback(async () => {
    try {
      setLoading(true)

      const result = await getAuditLogs({
        search: filter.search,
        action: filter.action || undefined,
        resource: filter.resource || undefined,
        success: filter.success === 'all' ? undefined : filter.success === 'success'
      })

      if (result.success && result.data) {
        // Convert timestamp strings to Date objects
        const logs = result.data.logs.map(log => ({
          ...log,
          timestamp: new Date(log.timestamp)
        }))
        setAuditLogs(logs)
        setError(null)
      } else {
        setError(result.error || 'Failed to fetch audit logs')
      }
    } catch (err) {
      setError('Failed to fetch audit logs')
    } finally {
      setLoading(false)
    }
  }, [filter])

  useEffect(() => {
    fetchAuditLogs()
  }, [fetchAuditLogs])

  const handleFilterChange = (key: string, value: string) => {
    setFilter(prev => ({ ...prev, [key]: value }))
    setCurrentPage(1)
  }

  const handleExport = () => {
    // In a real implementation, this would export the filtered logs
    alert('Export functionality would be implemented here')
  }

  const filteredLogs = auditLogs.filter(log => {
    if (filter.search &&
      !log.action.toLowerCase().includes(filter.search.toLowerCase()) &&
      !log.resource.toLowerCase().includes(filter.search.toLowerCase())) {
      return false
    }

    if (filter.action && log.action !== filter.action) {
      return false
    }

    if (filter.resource && log.resource !== filter.resource) {
      return false
    }

    if (filter.success !== 'all') {
      if (filter.success === 'success' && !log.success) {
        return false
      }
      if (filter.success === 'failure' && log.success) {
        return false
      }
    }

    return true
  })

  const indexOfLastLog = currentPage * logsPerPage
  const indexOfFirstLog = indexOfLastLog - logsPerPage
  const currentLogs = filteredLogs.slice(indexOfFirstLog, indexOfLastLog)
  const totalPages = Math.ceil(filteredLogs.length / logsPerPage)

  const getStatusIcon = (success: boolean) => {
    if (success) {
      return <CheckCircle className="h-4 w-4 text-green-500" />
    } else {
      return <XCircle className="h-4 w-4 text-red-500" />
    }
  }

  const getStatusText = (success: boolean) => {
    return success ? 'Success' : 'Failure'
  }

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-green-500 mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading audit logs...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Activity className="w-5 h-5 mr-2 text-blue-500" />
            Security Audit Logs
          </CardTitle>
          <p className="text-sm text-gray-500">
            Monitor all system activities and security events
          </p>
        </CardHeader>
        <CardContent>
          {/* Filter Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            <div>
              <Label htmlFor="search">Search</Label>
              <div className="relative">
                <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="search"
                  placeholder="Search actions or resources..."
                  className="pl-10"
                  value={filter.search}
                  onChange={(e) => handleFilterChange('search', e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="action">Action</Label>
              <select
                id="action"
                className="w-full rounded-md border border-gray-300 bg-white py-2 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filter.action}
                onChange={(e) => handleFilterChange('action', e.target.value)}
              >
                <option value="">All Actions</option>
                <option value="LOGIN">Login</option>
                <option value="LOGOUT">Logout</option>
                <option value="API_REQUEST">API Request</option>
                <option value="CREATE">Create</option>
                <option value="UPDATE">Update</option>
                <option value="DELETE">Delete</option>
                <option value="TOKEN_REFRESH">Token Refresh</option>
              </select>
            </div>

            <div>
              <Label htmlFor="success">Status</Label>
              <select
                id="success"
                className="w-full rounded-md border border-gray-300 bg-white py-2 px-3 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={filter.success}
                onChange={(e) => handleFilterChange('success', e.target.value)}
              >
                <option value="all">All Statuses</option>
                <option value="success">Success</option>
                <option value="failure">Failure</option>
              </select>
            </div>

            <div className="flex items-end">
              <Button onClick={handleExport} className="w-full">
                <Download className="w-4 h-4 mr-2" />
                Export Logs
              </Button>
            </div>
          </div>

          {/* Stats Summary */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-lg">
              <div className="flex items-center">
                <Activity className="h-8 w-8 text-blue-500" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-blue-600 dark:text-blue-300">Total Events</p>
                  <p className="text-2xl font-semibold">{auditLogs.length}</p>
                </div>
              </div>
            </div>

            <div className="bg-green-50 dark:bg-green-900/30 p-4 rounded-lg">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-500" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-green-600 dark:text-green-300">Successful</p>
                  <p className="text-2xl font-semibold">{auditLogs.filter(log => log.success).length}</p>
                </div>
              </div>
            </div>

            <div className="bg-red-50 dark:bg-red-900/30 p-4 rounded-lg">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-500" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-red-600 dark:text-red-300">Failed</p>
                  <p className="text-2xl font-semibold">{auditLogs.filter(log => !log.success).length}</p>
                </div>
              </div>
            </div>

            <div className="bg-purple-50 dark:bg-purple-900/30 p-4 rounded-lg">
              <div className="flex items-center">
                <Server className="h-8 w-8 text-purple-500" />
                <div className="ml-3">
                  <p className="text-sm font-medium text-purple-600 dark:text-purple-300">Remote Servers</p>
                  <p className="text-2xl font-semibold">{auditLogs.filter(log => log.remoteServerId).length}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Audit Logs Table */}
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
              <thead className="bg-gray-50 dark:bg-gray-800">
                <tr>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Status</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Action</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Resource</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">User/IP</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Time</th>
                  <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">Duration</th>
                </tr>
              </thead>
              <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                {currentLogs.length === 0 ? (
                  <tr>
                    <td colSpan={6} className="px-6 py-4 text-center text-gray-500 dark:text-gray-400">
                      No audit logs found matching your filters
                    </td>
                  </tr>
                ) : (
                  currentLogs.map((log) => (
                    <tr key={log.id} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          {getStatusIcon(log.success)}
                          <span className="ml-2 text-sm">{getStatusText(log.success)}</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">{log.action}</div>
                        {log.statusCode && (
                          <div className="text-sm text-gray-500 dark:text-gray-400">Status: {log.statusCode}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-gray-900 dark:text-white">{log.resource}</div>
                        {log.remoteServerId && (
                          <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
                            <Server className="h-3 w-3 mr-1" />
                            Remote Server
                          </div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {log.userId ? (
                          <div className="flex items-center text-sm text-gray-900 dark:text-white">
                            <User className="h-3 w-3 mr-1" />
                            {log.userId.substring(0, 8)}...
                          </div>
                        ) : (
                          <div className="text-sm text-gray-500 dark:text-gray-400">System</div>
                        )}
                        {log.ipAddress && (
                          <div className="text-xs text-gray-500 dark:text-gray-400">{log.ipAddress}</div>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {log.timestamp.toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                        {log.duration ? `${log.duration}ms` : 'N/A'}
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          {filteredLogs.length > logsPerPage && (
            <div className="flex items-center justify-between border-t border-gray-200 dark:border-gray-700 px-4 py-3 sm:px-6">
              <div className="flex flex-1 justify-between sm:hidden">
                <Button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
              <div className="hidden sm:flex sm:flex-1 sm:items-center sm:justify-between">
                <div>
                  <p className="text-sm text-gray-700 dark:text-gray-300">
                    Showing <span className="font-medium">{indexOfFirstLog + 1}</span> to{' '}
                    <span className="font-medium">{Math.min(indexOfLastLog, filteredLogs.length)}</span> of{' '}
                    <span className="font-medium">{filteredLogs.length}</span> results
                  </p>
                </div>
                <div>
                  <nav className="isolate inline-flex -space-x-px rounded-md shadow-sm" aria-label="Pagination">
                    <Button
                      onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                      disabled={currentPage === 1}
                      variant="outline"
                      size="sm"
                      className="relative inline-flex items-center rounded-l-md"
                    >
                      Previous
                    </Button>
                    {[...Array(totalPages)].map((_, i) => (
                      <Button
                        key={i + 1}
                        onClick={() => setCurrentPage(i + 1)}
                        variant={currentPage === i + 1 ? "default" : "outline"}
                        size="sm"
                        className="relative inline-flex items-center"
                      >
                        {i + 1}
                      </Button>
                    ))}
                    <Button
                      onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                      disabled={currentPage === totalPages}
                      variant="outline"
                      size="sm"
                      className="relative inline-flex items-center rounded-r-md"
                    >
                      Next
                    </Button>
                  </nav>
                </div>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}