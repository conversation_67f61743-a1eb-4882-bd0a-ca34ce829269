'use client';

import React, { useState, useRef, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar, 
  FileText, 
  BookOpen,
  IdCard, 
  Briefcase, 
  Crown 
} from 'lucide-react';

interface Title {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
}

interface Position {
  id: string;
  title: string;
  description: string | null;
  level: number;
  parentId: string | null;
  isActive: boolean;
}

export const CreateUserTab: React.FC = () => {
  const [isBulkUpload, setIsBulkUpload] = useState(false);
  const [activeCreateUserTab, setActiveCreateUserTab] = useState<'user' | 'treaty' | 'ordinance'>('user');
  const [singleUser, setSingleUser] = useState({
    firstName: '',
    surname: '',
    dob: '',
    email: '',
    phone: '',
    country: '',
    city: '',
    license: '',
    passport: '',
    bio: '',
    titleId: '',
    positionId: '',
  });
  
  // Treaty and ordinance state
  const [selectedTreaties, setSelectedTreaties] = useState<string[]>([]);
  const [selectedOrdinances, setSelectedOrdinances] = useState<string[]>([]);
  const [availableTreaties, setAvailableTreaties] = useState<Array<{id: string, name: string, description: string}>>([]);
  const [availableOrdinances, setAvailableOrdinances] = useState<Array<{id: string, name: string, description: string}>>([]);
  const [csvFile, setCsvFile] = useState<File | null>(null);
  const [csvFileName, setCsvFileName] = useState('');
  const [bulkDataPreview, setBulkDataPreview] = useState<any[]>([]);
  const [isPreviewMode, setIsPreviewMode] = useState(false);
  const [adminConfirmation, setAdminConfirmation] = useState('');
  const [titles, setTitles] = useState<Title[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [filteredPositions, setFilteredPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(true);
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Fetch titles, positions, treaties, and ordinances on component mount
  useEffect(() => {
    Promise.all([
      fetchTitles(),
      fetchPositions(),
      fetchTreaties(),
      fetchOrdinances()
    ]).finally(() => {
      setLoading(false);
    });
  }, []);

  const fetchTitles = async () => {
    try {
      const response = await fetch('/api/positions/titles');
      
      if (!response.ok) {
        throw new Error('Failed to fetch titles');
      }
      
      const data = await response.json();
      setTitles(data);
    } catch (error) {
      console.error('Error fetching titles:', error);
      toast.error('Failed to load titles');
    }
  };

  const fetchPositions = async () => {
    try {
      const response = await fetch('/api/positions/positions');
      
      if (!response.ok) {
        throw new Error('Failed to fetch positions');
      }
      
      const data = await response.json();
      setPositions(data);
    } catch (error) {
      console.error('Error fetching positions:', error);
      toast.error('Failed to load positions');
    }
  };

  const fetchTreaties = async () => {
    try {
      // Mock data for treaties - in a real implementation, this would come from an API
      const mockTreaties = [
        { id: '1', name: 'Treaty of Mutual Defense', description: 'Defense cooperation agreement' },
        { id: '2', name: 'Trade Partnership Agreement', description: 'Economic and trade cooperation' },
        { id: '3', name: 'Cultural Exchange Treaty', description: 'Cultural and educational cooperation' },
        { id: '4', name: 'Environmental Protection Accord', description: 'Joint environmental initiatives' },
      ];
      setAvailableTreaties(mockTreaties);
    } catch (error) {
      console.error('Error fetching treaties:', error);
      toast.error('Failed to load treaties');
    }
  };

  const fetchOrdinances = async () => {
    try {
      // Mock data for ordinances - in a real implementation, this would come from an API
      const mockOrdinances = [
        { id: '1', name: 'Security Ordinance', description: 'Member security protocols' },
        { id: '2', name: 'Code of Conduct', description: 'Behavioral guidelines for members' },
        { id: '3', name: 'Financial Compliance', description: 'Financial reporting requirements' },
        { id: '4', name: 'Training Requirements', description: 'Mandatory training programs' },
      ];
      setAvailableOrdinances(mockOrdinances);
    } catch (error) {
      console.error('Error fetching ordinances:', error);
      toast.error('Failed to load ordinances');
    }
  };

  const handleTreatyToggle = (treatyId: string) => {
    setSelectedTreaties(prev => 
      prev.includes(treatyId) 
        ? prev.filter(id => id !== treatyId) 
        : [...prev, treatyId]
    );
  };

  const handleOrdinanceToggle = (ordinanceId: string) => {
    setSelectedOrdinances(prev => 
      prev.includes(ordinanceId) 
        ? prev.filter(id => id !== ordinanceId) 
        : [...prev, ordinanceId]
    );
  };

  // Fetch positions associated with a specific title
  const fetchPositionsForTitle = async (titleId: string) => {
    try {
      const response = await fetch(`/api/positions/positions?titleId=${titleId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch positions for title');
      }
      
      const data = await response.json();
      setFilteredPositions(data);
      // Reset position selection when title changes
      setSingleUser(prev => ({ ...prev, positionId: '' }));
    } catch (error) {
      console.error('Error fetching positions for title:', error);
      toast.error('Failed to load positions for selected title');
    }
  };

  const handleSingleUserChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    // If changing the title, fetch associated positions
    if (name === 'titleId') {
      setSingleUser(prev => ({ ...prev, [name]: value, positionId: '' }));
      if (value) {
        fetchPositionsForTitle(value);
      } else {
        setFilteredPositions([]);
      }
    } else {
      setSingleUser(prev => ({ ...prev, [name]: value }));
    }
  };

  const handleCsvFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Check file type
      if (selectedFile.type !== 'text/csv' && !selectedFile.name.endsWith('.csv')) {
        toast.error('Invalid file type. Please upload a CSV file.');
        return;
      }
      
      // Check file size (10MB limit)
      if (selectedFile.size > 10 * 1024 * 1024) {
        toast.error('File size exceeds 10MB limit.');
        return;
      }
      
      setCsvFile(selectedFile);
      setCsvFileName(selectedFile.name);
      
      // In a real implementation, you would parse the CSV file and show a preview
      // For now, we'll just simulate this
      setBulkDataPreview([
        { id: 1, firstName: 'John', surname: 'Doe', email: '<EMAIL>', country: 'USA' },
        { id: 2, firstName: 'Jane', surname: 'Smith', email: '<EMAIL>', country: 'Canada' },
        { id: 3, firstName: 'Bob', surname: 'Johnson', email: '<EMAIL>', country: 'UK' },
      ]);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const removeCsvFile = () => {
    setCsvFile(null);
    setCsvFileName('');
    setBulkDataPreview([]);
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSingleUserSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validate required fields
    if (!singleUser.firstName || !singleUser.surname || !singleUser.email) {
      toast.error('Please fill in all required fields (First Name, Surname, Email).');
      return;
    }
    
    try {
      // In a real implementation, you would send the data to the API
      // For now, we'll just show a success message
      toast.success('User created successfully!');
      
      // Reset form
      setSingleUser({
        firstName: '',
        surname: '',
        dob: '',
        email: '',
        phone: '',
        country: '',
        city: '',
        license: '',
        passport: '',
        bio: '',
        titleId: '',
        positionId: '',
      });
      setFilteredPositions([]);
    } catch (error: any) {
      toast.error(error.message || 'Failed to create user');
      console.error('Error creating user:', error);
    }
  };

  const handleBulkUploadSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!csvFile) {
      toast.error('Please select a CSV file to upload.');
      return;
    }
    
    // Show preview mode
    setIsPreviewMode(true);
  };

  const handleConfirmBulkUpload = async () => {
    if (adminConfirmation.toLowerCase() !== 'insert data') {
      toast.error('Please type "insert data" to confirm.');
      return;
    }
    
    try {
      // In a real implementation, you would send the bulk data to the API
      // For now, we'll just show a success message
      toast.success('Bulk users uploaded successfully!');
      
      // Reset form
      setCsvFile(null);
      setCsvFileName('');
      setBulkDataPreview([]);
      setIsPreviewMode(false);
      setAdminConfirmation('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    } catch (error: any) {
      toast.error(error.message || 'Failed to upload bulk users');
      console.error('Error uploading bulk users:', error);
    }
  };

  const handleCancelBulkUpload = () => {
    setIsPreviewMode(false);
    setAdminConfirmation('');
  };

  // Get positions to display (filtered by title or all positions)
  const displayPositions = singleUser.titleId ? filteredPositions : positions;

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex space-x-4 mb-6">
        <button
          onClick={() => setIsBulkUpload(false)}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
            !isBulkUpload
              ? 'bg-slate-700 text-white hover:bg-slate-800'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-slate-700 dark:text-slate-300 dark:hover:bg-slate-600'
          }`}
        >
          Single User
        </button>
        <button
          onClick={() => setIsBulkUpload(true)}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors duration-200 ${
            isBulkUpload
              ? 'bg-slate-700 text-white hover:bg-slate-800'
              : 'bg-gray-200 text-gray-700 hover:bg-gray-300 dark:bg-slate-700 dark:text-slate-300 dark:hover:bg-slate-600'
          }`}
        >
          Bulk Upload (CSV)
        </button>
      </div>

      {!isBulkUpload ? (
        // Single User Form
        <div>
          {/* Internal Tabs for Create User Section */}
          <div className="border-b border-slate-200 dark:border-slate-700 mb-6">
            <nav className="flex space-x-8">
              <button
                onClick={() => setActiveCreateUserTab('user')}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200 ${
                  activeCreateUserTab === 'user'
                    ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                    : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                }`}
              >
                <User className="h-5 w-5 mr-2 text-slate-600 dark:text-slate-400" />
                <span>Create User</span>
              </button>
              <button
                onClick={() => setActiveCreateUserTab('treaty')}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200 ${
                  activeCreateUserTab === 'treaty'
                    ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                    : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                }`}
              >
                <FileText className="h-5 w-5 mr-2 text-slate-600 dark:text-slate-400" />
                <span>Member Treaty</span>
              </button>
              <button
                onClick={() => setActiveCreateUserTab('ordinance')}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200 ${
                  activeCreateUserTab === 'ordinance'
                    ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                    : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
                }`}
              >
                <BookOpen className="h-5 w-5 mr-2 text-slate-600 dark:text-slate-400" />
                <span>Member Ordinance</span>
              </button>
            </nav>
          </div>

          <form onSubmit={handleSingleUserSubmit} className="space-y-6">
            {activeCreateUserTab === 'user' ? (
              <>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label htmlFor="firstName" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                      First Name <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="h-5 w-5 text-slate-400" />
                      </div>
                      <input
                        type="text"
                        id="firstName"
                        name="firstName"
                        value={singleUser.firstName}
                        onChange={handleSingleUserChange}
                        className="w-full pl-10 pr-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="surname" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
                      Surname <span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <User className="h-5 w-5 text-slate-400" />
                      </div>
                      <input
                        type="text"
                        id="surname"
                        name="surname"
                        value={singleUser.surname}
                        onChange={handleSingleUserChange}
                        className="w-full pl-10 pr-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
                        required
                      />
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="dob" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Date of Birth
                    </label>
                    <input
                      type="date"
                      id="dob"
                      name="dob"
                      value={singleUser.dob}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Email <span className="text-red-500">*</span>
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={singleUser.email}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                      required
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Phone
                    </label>
                    <input
                      type="tel"
                      id="phone"
                      name="phone"
                      value={singleUser.phone}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="country" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Country
                    </label>
                    <input
                      type="text"
                      id="country"
                      name="country"
                      value={singleUser.country}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="city" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      City
                    </label>
                    <input
                      type="text"
                      id="city"
                      name="city"
                      value={singleUser.city}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="license" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      License
                    </label>
                    <input
                      type="text"
                      id="license"
                      name="license"
                      value={singleUser.license}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="passport" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Passport
                    </label>
                    <input
                      type="text"
                      id="passport"
                      name="passport"
                      value={singleUser.passport}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  
                  <div className="md:col-span-2">
                    <label htmlFor="bio" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Bio
                    </label>
                    <textarea
                      id="bio"
                      name="bio"
                      value={singleUser.bio}
                      onChange={handleSingleUserChange}
                      rows={3}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    />
                  </div>
                  
                  <div>
                    <label htmlFor="titleId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Title
                    </label>
                    <select
                      id="titleId"
                      name="titleId"
                      value={singleUser.titleId}
                      onChange={handleSingleUserChange}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    >
                      <option value="">Select Title</option>
                      {titles
                        .filter(title => title.isActive)
                        .map(title => (
                          <option key={title.id} value={title.id}>
                            {title.name}
                          </option>
                        ))}
                    </select>
                  </div>
                  
                  <div>
                    <label htmlFor="positionId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                      Position
                    </label>
                    <select
                      id="positionId"
                      name="positionId"
                      value={singleUser.positionId}
                      onChange={handleSingleUserChange}
                      disabled={!singleUser.titleId}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white disabled:opacity-50"
                    >
                      <option value="">{singleUser.titleId ? 'Select Position' : 'Select a title first'}</option>
                      {displayPositions
                        .filter(position => position.isActive)
                        .map(position => (
                          <option key={position.id} value={position.id}>
                            {position.title}
                          </option>
                        ))}
                    </select>
                    {singleUser.titleId && displayPositions.length === 0 && (
                      <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                        No positions are associated with the selected title.
                      </p>
                    )}
                  </div>
                </div>
                
                <div className="flex justify-end">
                  <button
                    type="submit"
                    className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                  >
                    Create User
                  </button>
                </div>
              </>
            ) : activeCreateUserTab === 'treaty' ? (
              /* Member Treaty Tab Content */
              <div className="space-y-6">
                <div className="grid grid-cols-1 gap-6">
                  <div>
                    <label htmlFor="treatySelect" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Select Treaty
                    </label>
                    <div className="flex space-x-2">
                      <select
                        id="treatySelect"
                        className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                        value=""
                        onChange={(e) => {
                          const treatyId = e.target.value;
                          if (treatyId) {
                            // Check if treaty is already selected
                            if (!selectedTreaties.includes(treatyId)) {
                              setSelectedTreaties([...selectedTreaties, treatyId]);
                              toast.success('Treaty added successfully!');
                            }
                          }
                        }}
                      >
                        <option value="">Select a treaty</option>
                        {availableTreaties.map((treaty) => (
                          <option key={treaty.id} value={treaty.id}>
                            {treaty.name}
                          </option>
                        ))}
                      </select>
                      <button
                        type="button"
                        className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                        onClick={() => {
                          const selectElement = document.getElementById('treatySelect') as HTMLSelectElement;
                          const treatyId = selectElement.value;
                          if (treatyId) {
                            // Check if treaty is already selected
                            if (!selectedTreaties.includes(treatyId)) {
                              setSelectedTreaties([...selectedTreaties, treatyId]);
                              toast.success('Treaty added successfully!');
                              // Reset the select to default
                              selectElement.value = '';
                            }
                          }
                        }}
                      >
                        Add Treaty
                      </button>
                    </div>
                  </div>
                  
                  {/* Display selected treaties */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 dark:text-white mb-2">Selected Treaties</h4>
                    <div className="border border-gray-300 dark:border-gray-600 rounded-md p-4 min-h-32">
                      {selectedTreaties.length > 0 ? (
                        <ul className="space-y-2">
                          {selectedTreaties.map((treatyId) => {
                            const treaty = availableTreaties.find(t => t.id === treatyId);
                            return treaty ? (
                              <li key={treatyId} className="flex justify-between items-center bg-slate-100 dark:bg-slate-700 p-2 rounded">
                                <span>{treaty.name}</span>
                                <button
                                  type="button"
                                  className="text-red-500 hover:text-red-700"
                                  onClick={() => {
                                    setSelectedTreaties(selectedTreaties.filter(id => id !== treatyId));
                                    toast.success('Treaty removed!');
                                  }}
                                >
                                  Remove
                                </button>
                              </li>
                            ) : null;
                          })}
                        </ul>
                      ) : (
                        <p className="text-gray-500 dark:text-gray-400 text-sm">No treaties selected</p>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setActiveCreateUserTab('user')}
                    className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                  >
                    Back to User Info
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      // In a real implementation, you would save the treaty selections to the database
                      toast.success(`Saved ${selectedTreaties.length} treaties!`);
                      setActiveCreateUserTab('user');
                    }}
                    className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                  >
                    Save Treaties
                  </button>
                </div>
              </div>
            ) : (
              /* Member Ordinance Tab Content */
              <div className="space-y-6">
                <div className="grid grid-cols-1 gap-6">
                  <div>
                    <label htmlFor="ordinanceSelect" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Select Ordinance
                    </label>
                    <div className="flex space-x-2">
                      <select
                        id="ordinanceSelect"
                        className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                        value=""
                        onChange={(e) => {
                          const ordinanceId = e.target.value;
                          if (ordinanceId) {
                            // Check if ordinance is already selected
                            if (!selectedOrdinances.includes(ordinanceId)) {
                              setSelectedOrdinances([...selectedOrdinances, ordinanceId]);
                              toast.success('Ordinance added successfully!');
                            }
                          }
                        }}
                      >
                        <option value="">Select an ordinance</option>
                        {availableOrdinances.map((ordinance) => (
                          <option key={ordinance.id} value={ordinance.id}>
                            {ordinance.name}
                          </option>
                        ))}
                      </select>
                      <button
                        type="button"
                        className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                        onClick={() => {
                          const selectElement = document.getElementById('ordinanceSelect') as HTMLSelectElement;
                          const ordinanceId = selectElement.value;
                          if (ordinanceId) {
                            // Check if ordinance is already selected
                            if (!selectedOrdinances.includes(ordinanceId)) {
                              setSelectedOrdinances([...selectedOrdinances, ordinanceId]);
                              toast.success('Ordinance added successfully!');
                              // Reset the select to default
                              selectElement.value = '';
                            }
                          }
                        }}
                      >
                        Add Ordinance
                      </button>
                    </div>
                  </div>
                  
                  {/* Display selected ordinances */}
                  <div>
                    <h4 className="text-md font-medium text-gray-900 dark:text-white mb-2">Selected Ordinances</h4>
                    <div className="border border-gray-300 dark:border-gray-600 rounded-md p-4 min-h-32">
                      {selectedOrdinances.length > 0 ? (
                        <ul className="space-y-2">
                          {selectedOrdinances.map((ordinanceId) => {
                            const ordinance = availableOrdinances.find(o => o.id === ordinanceId);
                            return ordinance ? (
                              <li key={ordinanceId} className="flex justify-between items-center bg-slate-100 dark:bg-slate-700 p-2 rounded">
                                <span>{ordinance.name}</span>
                                <button
                                  type="button"
                                  className="text-red-500 hover:text-red-700"
                                  onClick={() => {
                                    setSelectedOrdinances(selectedOrdinances.filter(id => id !== ordinanceId));
                                    toast.success('Ordinance removed!');
                                  }}
                                >
                                  Remove
                                </button>
                              </li>
                            ) : null;
                          })}
                        </ul>
                      ) : (
                        <p className="text-gray-500 dark:text-gray-400 text-sm">No ordinances selected</p>
                      )}
                    </div>
                  </div>
                </div>
                
                <div className="flex justify-end space-x-3">
                  <button
                    type="button"
                    onClick={() => setActiveCreateUserTab('user')}
                    className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                  >
                    Back to User Info
                  </button>
                  <button
                    type="button"
                    onClick={() => {
                      // In a real implementation, you would save the ordinance selections to the database
                      toast.success(`Saved ${selectedOrdinances.length} ordinances!`);
                      setActiveCreateUserTab('user');
                    }}
                    className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                  >
                    Save Ordinances
                  </button>
                </div>
              </div>
            )}
          </form>
        </div>
      ) : (
        // Bulk Upload Form
        <div>
          {!isPreviewMode ? (
            <form onSubmit={handleBulkUploadSubmit} className="space-y-6">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  CSV File <span className="text-red-500">*</span>
                </label>
                <div className="flex items-center space-x-4">
                  <input
                    type="file"
                    ref={fileInputRef}
                    onChange={handleCsvFileChange}
                    accept=".csv,text/csv"
                    className="hidden"
                  />
                  <button
                    type="button"
                    onClick={triggerFileInput}
                    className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 text-sm transition-colors duration-200"
                  >
                    Choose File
                  </button>
                  {csvFileName && (
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-600 dark:text-gray-400 truncate max-w-xs">
                        {csvFileName}
                      </span>
                      <button
                        type="button"
                        onClick={removeCsvFile}
                        className="text-red-500 hover:text-red-700"
                      >
                        Remove
                      </button>
                    </div>
                  )}
                </div>
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  CSV files only. Max 10MB. Required columns: firstName, surname, email
                </p>
              </div>
              
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                  CSV Format Requirements
                </h3>
                <ul className="text-xs text-blue-700 dark:text-blue-300 list-disc pl-5 space-y-1">
                  <li>Required columns: firstName, surname, email</li>
                  <li>Optional columns: dob, phone, country, city, license, passport, bio, titleId, positionId</li>
                  <li>Date format: YYYY-MM-DD</li>
                  <li>Maximum 1000 rows per upload</li>
                </ul>
              </div>
              
              <div className="flex justify-end">
                <button
                  type="submit"
                  disabled={!csvFile}
                  className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 transition-colors duration-200"
                >
                  Preview & Upload
                </button>
              </div>
            </form>
          ) : (
            // Preview Mode
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Bulk Upload Preview</h3>
                <div className="overflow-x-auto">
                  <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead className="bg-gray-50 dark:bg-gray-700">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          First Name
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Surname
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Email
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                          Country
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                      {bulkDataPreview.map((user) => (
                        <tr key={user.id}>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {user.firstName}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {user.surname}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {user.email}
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                            {user.country}
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                <p className="mt-2 text-sm text-gray-500 dark:text-gray-400">
                  Showing {bulkDataPreview.length} of {bulkDataPreview.length} records
                </p>
              </div>
              
              <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                <h3 className="text-sm font-medium text-blue-800 dark:text-blue-200 mb-2">
                  Admin Confirmation Required
                </h3>
                <p className="text-sm text-blue-700 dark:text-blue-300 mb-4">
                  Please type &quot;insert data&quot; in the field below to confirm the bulk upload.
                  This action cannot be undone.
                </p>
                <div>
                  <label htmlFor="adminConfirmation" className="block text-sm font-medium text-blue-800 dark:text-blue-200 mb-1">
                    Confirmation Text
                  </label>
                  <input
                    type="text"
                    id="adminConfirmation"
                    value={adminConfirmation}
                    onChange={(e) => setAdminConfirmation(e.target.value)}
                    className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
                    placeholder="Type 'insert data' to confirm"
                  />
                </div>
              </div>
              
              <div className="flex justify-end space-x-3">
                <button
                  type="button"
                  onClick={handleCancelBulkUpload}
                  className="px-6 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500"
                >
                  Cancel
                </button>
                <button
                  type="button"
                  onClick={handleConfirmBulkUpload}
                  disabled={adminConfirmation.toLowerCase() !== 'insert data'}
                  className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 transition-colors duration-200"
                >
                  Confirm & Insert Data
                </button>
              </div>
            </div>
          )}
        </div>
      )}
    </div>
  );
};