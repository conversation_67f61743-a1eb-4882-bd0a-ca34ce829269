'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { Search, X } from 'lucide-react';

interface Title {
  id: string;
  name: string;
  description: string | null;
  isActive: boolean;
}

interface Position {
  id: string;
  title: string;
  description: string | null;
  level: number;
  parentId: string | null;
  isActive: boolean;
}

export const UpdateUserTab: React.FC = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const [suggestions, setSuggestions] = useState<any[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedUser, setSelectedUser] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [updating, setUpdating] = useState(false);
  const [titles, setTitles] = useState<Title[]>([]);
  const [positions, setPositions] = useState<Position[]>([]);
  const [filteredPositions, setFilteredPositions] = useState<Position[]>([]);
  const [initialLoading, setInitialLoading] = useState(true);

  // Fetch titles and positions on component mount
  useEffect(() => {
    Promise.all([
      fetchTitles(),
      fetchPositions()
    ]).finally(() => {
      setInitialLoading(false);
    });
  }, []);

  const fetchTitles = async () => {
    try {
      const response = await fetch('/api/positions/titles');
      
      if (!response.ok) {
        throw new Error('Failed to fetch titles');
      }
      
      const data = await response.json();
      setTitles(data);
    } catch (error) {
      console.error('Error fetching titles:', error);
      toast.error('Failed to load titles');
    }
  };

  const fetchPositions = async () => {
    try {
      const response = await fetch('/api/positions/positions');
      
      if (!response.ok) {
        throw new Error('Failed to fetch positions');
      }
      
      const data = await response.json();
      setPositions(data);
    } catch (error) {
      console.error('Error fetching positions:', error);
      toast.error('Failed to load positions');
    }
  };

  // Fetch positions associated with a specific title
  const fetchPositionsForTitle = async (titleId: string) => {
    try {
      const response = await fetch(`/api/positions/positions?titleId=${titleId}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch positions for title');
      }
      
      const data = await response.json();
      setFilteredPositions(data);
    } catch (error) {
      console.error('Error fetching positions for title:', error);
      toast.error('Failed to load positions for selected title');
    }
  };

  // Simulate fetching user suggestions (in a real app, this would come from an API)
  useEffect(() => {
    if (searchQuery.length > 2) {
      // Mock suggestions - in a real app, you would fetch these from an API
      const mockSuggestions = [
        { id: 1, name: 'John Doe', email: '<EMAIL>', position: 'Administrator' },
        { id: 2, name: 'Jane Smith', email: '<EMAIL>', position: 'Manager' },
        { id: 3, name: 'Bob Johnson', email: '<EMAIL>', position: 'Officer' },
        { id: 4, name: 'Alice Brown', email: '<EMAIL>', position: 'Specialist' },
        { id: 5, name: 'Charlie Wilson', email: '<EMAIL>', position: 'Director' },
      ].filter(user => 
        user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email.toLowerCase().includes(searchQuery.toLowerCase())
      );
      
      setSuggestions(mockSuggestions);
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
    }
  }, [searchQuery]);

  const handleSearch = (searchQuery: string = '') => {
    setSearchQuery(searchQuery);
    setShowSuggestions(false);
    
    if (searchQuery) {
      // In a real implementation, you would fetch the user details
      // For now, we'll just select the first suggestion as an example
      const foundUser = suggestions.find(user => 
        user.name.toLowerCase() === searchQuery.toLowerCase() ||
        user.email.toLowerCase() === searchQuery.toLowerCase()
      );
      
      if (foundUser) {
        const userWithTitles = {
          id: foundUser.id,
          firstName: foundUser.name.split(' ')[0],
          surname: foundUser.name.split(' ')[1] || '',
          email: foundUser.email,
          position: foundUser.position,
          country: 'USA',
          city: 'New York',
          phone: '****** 567 8900',
          dob: '1985-06-15',
          license: 'LIC-12345',
          passport: 'P-67890',
          bio: 'Experienced professional with 10+ years in the industry.',
          titleId: '1',
          positionId: '1',
        };
        
        setSelectedUser(userWithTitles);
        
        // Fetch positions for the user's title
        fetchPositionsForTitle(userWithTitles.titleId);
      }
    }
  };

  const handleSuggestionClick = (user: any) => {
    setSearchQuery(user.name);
    handleSearch(user.name);
  };

  const handleClear = () => {
    setSearchQuery('');
    setSelectedUser(null);
    setSuggestions([]);
    setShowSuggestions(false);
  };

  const handleUserChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (selectedUser) {
      // If changing the title, fetch associated positions
      if (name === 'titleId') {
        setSelectedUser({
          ...selectedUser,
          [name]: value,
          positionId: ''
        });
        if (value) {
          fetchPositionsForTitle(value);
        } else {
          setFilteredPositions([]);
        }
      } else {
        setSelectedUser({
          ...selectedUser,
          [name]: value
        });
      }
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedUser) {
      toast.error('Please select a user to update.');
      return;
    }
    
    setUpdating(true);
    
    try {
      // In a real implementation, you would send the updated data to the API
      // For now, we'll just show a success message
      toast.success('User updated successfully!');
    } catch (error: any) {
      toast.error(error.message || 'Failed to update user');
      console.error('Error updating user:', error);
    } finally {
      setUpdating(false);
    }
  };

  const handleSubmitForVerification = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedUser) {
      toast.error('Please select a user to update.');
      return;
    }
    
    try {
      // In a real implementation, you would send the update request for admin verification
      // For now, we'll just show a success message
      toast.success('Update request submitted for admin verification!');
    } catch (error: any) {
      toast.error(error.message || 'Failed to submit update request');
      console.error('Error submitting update request:', error);
    }
  };

  // Get positions to display (filtered by title or all positions)
  const displayPositions = selectedUser?.titleId ? filteredPositions : positions;

  if (initialLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="mb-6">
        <label htmlFor="userSearch" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Search User
        </label>
        <div className="relative">
          <form onSubmit={(e) => { e.preventDefault(); handleSearch(); }} className="relative">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <Search className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              id="userSearch"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              onFocus={() => searchQuery.length > 2 && setShowSuggestions(true)}
              onBlur={() => setTimeout(() => setShowSuggestions(false), 200)}
              className="block w-full pl-10 pr-10 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              placeholder="Search by name or email..."
            />
            {searchQuery && (
              <button
                type="button"
                onClick={handleClear}
                className="absolute inset-y-0 right-0 pr-3 flex items-center"
              >
                <X className="h-5 w-5 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300" />
              </button>
            )}
          </form>

          {showSuggestions && suggestions.length > 0 && (
            <div className="absolute z-10 mt-1 w-full bg-white dark:bg-gray-800 shadow-lg rounded-md overflow-hidden">
              <ul className="py-1">
                {suggestions.map((user) => (
                  <li
                    key={user.id}
                    onMouseDown={() => handleSuggestionClick(user)}
                    className="px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer"
                  >
                    <div className="flex justify-between">
                      <span className="font-medium">{user.name}</span>
                      <span className="text-gray-500 dark:text-gray-400">{user.position}</span>
                    </div>
                    <div className="text-gray-500 dark:text-gray-400">{user.email}</div>
                  </li>
                ))}
              </ul>
            </div>
          )}
        </div>
      </div>

      {selectedUser && (
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="border-b border-gray-200 dark:border-gray-700 pb-4">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Update User: {selectedUser.firstName} {selectedUser.surname}
            </h3>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              ID: {selectedUser.id} | Position: {selectedUser.position}
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label htmlFor="firstName" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                First Name
              </label>
              <input
                type="text"
                id="firstName"
                name="firstName"
                value={selectedUser.firstName}
                onChange={handleUserChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            <div>
              <label htmlFor="surname" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Surname
              </label>
              <input
                type="text"
                id="surname"
                name="surname"
                value={selectedUser.surname}
                onChange={handleUserChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            <div>
              <label htmlFor="dob" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Date of Birth
              </label>
              <input
                type="date"
                id="dob"
                name="dob"
                value={selectedUser.dob}
                onChange={handleUserChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            <div>
              <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Email
              </label>
              <input
                type="email"
                id="email"
                name="email"
                value={selectedUser.email}
                onChange={handleUserChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            <div>
              <label htmlFor="phone" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Phone
              </label>
              <input
                type="tel"
                id="phone"
                name="phone"
                value={selectedUser.phone}
                onChange={handleUserChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            <div>
              <label htmlFor="country" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Country
              </label>
              <input
                type="text"
                id="country"
                name="country"
                value={selectedUser.country}
                onChange={handleUserChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            <div>
              <label htmlFor="city" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                City
              </label>
              <input
                type="text"
                id="city"
                name="city"
                value={selectedUser.city}
                onChange={handleUserChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            <div>
              <label htmlFor="license" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                License
              </label>
              <input
                type="text"
                id="license"
                name="license"
                value={selectedUser.license}
                onChange={handleUserChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            <div>
              <label htmlFor="passport" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Passport
              </label>
              <input
                type="text"
                id="passport"
                name="passport"
                value={selectedUser.passport}
                onChange={handleUserChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            <div className="md:col-span-2">
              <label htmlFor="bio" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Bio
              </label>
              <textarea
                id="bio"
                name="bio"
                value={selectedUser.bio}
                onChange={handleUserChange}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              />
            </div>
            
            <div>
              <label htmlFor="titleId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Title
              </label>
              <select
                id="titleId"
                name="titleId"
                value={selectedUser.titleId}
                onChange={handleUserChange}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white"
              >
                <option value="">Select Title</option>
                {titles
                  .filter(title => title.isActive)
                  .map(title => (
                    <option key={title.id} value={title.id}>
                      {title.name}
                    </option>
                  ))}
              </select>
            </div>
            
            <div>
              <label htmlFor="positionId" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Position
              </label>
              <select
                id="positionId"
                name="positionId"
                value={selectedUser.positionId}
                onChange={handleUserChange}
                disabled={!selectedUser.titleId}
                className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-gray-700 dark:text-white disabled:opacity-50"
              >
                <option value="">{selectedUser.titleId ? 'Select Position' : 'Select a title first'}</option>
                {displayPositions
                  .filter(position => position.isActive)
                  .map(position => (
                    <option key={position.id} value={position.id}>
                      {position.title}
                    </option>
                  ))}
              </select>
              {selectedUser.titleId && displayPositions.length === 0 && (
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  No positions are associated with the selected title.
                </p>
              )}
            </div>
          </div>
          
          <div className="flex justify-end space-x-3">
            <button
              type="button"
              onClick={handleSubmitForVerification}
              className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
            >
              Submit for Verification
            </button>
            <button
              type="submit"
              disabled={updating}
              className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 transition-colors duration-200"
            >
              {updating ? 'Updating...' : 'Update User'}
            </button>
          </div>
        </form>
      )}

      {!selectedUser && (
        <div className="text-center py-12">
          <svg xmlns="http://www.w3.org/2000/svg" className="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900 dark:text-white">No user selected</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
            Search for a user above to update their information.
          </p>
        </div>
      )}
    </div>
  );
};