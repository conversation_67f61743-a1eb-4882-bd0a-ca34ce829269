'use client';

import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { FileText, BookOpen, Search, User, Plus, X } from 'lucide-react';

interface Treaty {
  id: string;
  name: string;
  description: string;
}

interface Ordinance {
  id: string;
  name: string;
  description: string;
}

interface Member {
  id: string;
  firstName: string;
  surname: string;
  email: string;
}

export const MemberTreatyTab: React.FC = () => {
  const [activeTab, setActiveTab] = useState<'assign' | 'manage'>('assign');
  const [selectedMember, setSelectedMember] = useState<Member | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [members, setMembers] = useState<Member[]>([]);
  const [availableTreaties, setAvailableTreaties] = useState<Treaty[]>([]);
  const [availableOrdinances, setAvailableOrdinances] = useState<Ordinance[]>([]);
  const [selectedTreaties, setSelectedTreaties] = useState<string[]>([]);
  const [selectedOrdinances, setSelectedOrdinances] = useState<string[]>([]);
  const [assignedTreaties, setAssignedTreaties] = useState<Treaty[]>([]);
  const [assignedOrdinances, setAssignedOrdinances] = useState<Ordinance[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch data on component mount
  useEffect(() => {
    Promise.all([
      fetchMembers(),
      fetchTreaties(),
      fetchOrdinances()
    ]).finally(() => {
      setLoading(false);
    });
  }, []);

  const fetchMembers = async () => {
    try {
      // Mock data for members - in a real implementation, this would come from an API
      const mockMembers = [
        { id: '1', firstName: 'John', surname: 'Doe', email: '<EMAIL>' },
        { id: '2', firstName: 'Jane', surname: 'Smith', email: '<EMAIL>' },
        { id: '3', firstName: 'Bob', surname: 'Johnson', email: '<EMAIL>' },
        { id: '4', firstName: 'Alice', surname: 'Williams', email: '<EMAIL>' },
      ];
      setMembers(mockMembers);
    } catch (error) {
      console.error('Error fetching members:', error);
      toast.error('Failed to load members');
    }
  };

  const fetchTreaties = async () => {
    try {
      // Mock data for treaties - in a real implementation, this would come from an API
      const mockTreaties = [
        { id: '1', name: 'Treaty of Mutual Defense', description: 'Defense cooperation agreement' },
        { id: '2', name: 'Trade Partnership Agreement', description: 'Economic and trade cooperation' },
        { id: '3', name: 'Cultural Exchange Treaty', description: 'Cultural and educational cooperation' },
        { id: '4', name: 'Environmental Protection Accord', description: 'Joint environmental initiatives' },
      ];
      setAvailableTreaties(mockTreaties);
    } catch (error) {
      console.error('Error fetching treaties:', error);
      toast.error('Failed to load treaties');
    }
  };

  const fetchOrdinances = async () => {
    try {
      // Mock data for ordinances - in a real implementation, this would come from an API
      const mockOrdinances = [
        { id: '1', name: 'Security Ordinance', description: 'Member security protocols' },
        { id: '2', name: 'Code of Conduct', description: 'Behavioral guidelines for members' },
        { id: '3', name: 'Financial Compliance', description: 'Financial reporting requirements' },
        { id: '4', name: 'Training Requirements', description: 'Mandatory training programs' },
      ];
      setAvailableOrdinances(mockOrdinances);
    } catch (error) {
      console.error('Error fetching ordinances:', error);
      toast.error('Failed to load ordinances');
    }
  };

  const handleMemberSelect = (member: Member) => {
    setSelectedMember(member);
    // In a real implementation, you would fetch the member's assigned treaties and ordinances
    // For now, we'll just reset the selections
    setSelectedTreaties([]);
    setSelectedOrdinances([]);
    setAssignedTreaties([]);
    setAssignedOrdinances([]);
  };

  const handleTreatyToggle = (treatyId: string) => {
    setSelectedTreaties(prev => 
      prev.includes(treatyId) 
        ? prev.filter(id => id !== treatyId) 
        : [...prev, treatyId]
    );
  };

  const handleOrdinanceToggle = (ordinanceId: string) => {
    setSelectedOrdinances(prev => 
      prev.includes(ordinanceId) 
        ? prev.filter(id => id !== ordinanceId) 
        : [...prev, ordinanceId]
    );
  };

  const handleSaveAssignments = async () => {
    if (!selectedMember) {
      toast.error('Please select a member first.');
      return;
    }

    try {
      // In a real implementation, you would send the data to the API
      // For now, we'll just show a success message
      toast.success(`Treaties and ordinances assigned to ${selectedMember.firstName} ${selectedMember.surname} successfully!`);
      
      // Update the assigned lists
      const newAssignedTreaties = availableTreaties.filter(t => selectedTreaties.includes(t.id));
      const newAssignedOrdinances = availableOrdinances.filter(o => selectedOrdinances.includes(o.id));
      setAssignedTreaties(newAssignedTreaties);
      setAssignedOrdinances(newAssignedOrdinances);
    } catch (error: any) {
      toast.error(error.message || 'Failed to assign treaties and ordinances');
      console.error('Error assigning treaties and ordinances:', error);
    }
  };

  const filteredMembers = members.filter(member => 
    member.firstName.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.surname.toLowerCase().includes(searchQuery.toLowerCase()) ||
    member.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-slate-700"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="border-b border-slate-200 dark:border-slate-700 mb-6">
        <nav className="flex space-x-8">
          <button
            onClick={() => setActiveTab('assign')}
            className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200 ${
              activeTab === 'assign'
                ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
            }`}
          >
            <FileText className="h-5 w-5 mr-2 text-slate-600 dark:text-slate-400" />
            <span>Assign Treaties & Ordinances</span>
          </button>
          <button
            onClick={() => setActiveTab('manage')}
            className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center transition-colors duration-200 ${
              activeTab === 'manage'
                ? 'border-slate-700 text-slate-800 dark:text-slate-200 dark:border-slate-300'
                : 'border-transparent text-slate-500 hover:text-slate-700 hover:border-slate-300 dark:text-slate-400 dark:hover:text-slate-300'
            }`}
          >
            <BookOpen className="h-5 w-5 mr-2 text-slate-600 dark:text-slate-400" />
            <span>Manage Treaties & Ordinances</span>
          </button>
        </nav>
      </div>

      {activeTab === 'assign' ? (
        <div className="space-y-6">
          {/* Member Selection */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-1">
              <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-4">
                <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-4 flex items-center">
                  <User className="h-5 w-5 mr-2 text-slate-600 dark:text-slate-400" />
                  Select Member
                </h3>
                
                <div className="mb-4">
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <Search className="h-5 w-5 text-slate-400" />
                    </div>
                    <input
                      type="text"
                      placeholder="Search members..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full pl-10 pr-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
                    />
                  </div>
                </div>
                
                <div className="space-y-2 max-h-96 overflow-y-auto">
                  {filteredMembers.map((member) => (
                    <div
                      key={member.id}
                      onClick={() => handleMemberSelect(member)}
                      className={`p-3 rounded-md cursor-pointer transition-colors duration-200 ${
                        selectedMember?.id === member.id
                          ? 'bg-slate-100 dark:bg-slate-700 border border-slate-300 dark:border-slate-600'
                          : 'hover:bg-slate-50 dark:hover:bg-slate-700'
                      }`}
                    >
                      <div className="font-medium text-slate-800 dark:text-slate-200">
                        {member.firstName} {member.surname}
                      </div>
                      <div className="text-sm text-slate-600 dark:text-slate-400">
                        {member.email}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
            
            {/* Assignment Section */}
            <div className="lg:col-span-2">
              {selectedMember ? (
                <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
                  <div className="mb-6">
                    <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 mb-1">
                      Assign to: {selectedMember.firstName} {selectedMember.surname}
                    </h3>
                    <p className="text-sm text-slate-600 dark:text-slate-400">
                      {selectedMember.email}
                    </p>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Treaties */}
                    <div>
                      <h4 className="text-md font-medium text-slate-800 dark:text-slate-200 mb-3 flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-slate-600 dark:text-slate-400" />
                        Treaties
                      </h4>
                      <div className="border border-slate-300 dark:border-slate-600 rounded-md p-4">
                        <div className="space-y-3">
                          {availableTreaties.map((treaty) => (
                            <div key={treaty.id} className="flex items-start">
                              <div className="flex items-center h-5">
                                <input
                                  id={`treaty-${treaty.id}`}
                                  type="checkbox"
                                  checked={selectedTreaties.includes(treaty.id)}
                                  onChange={() => handleTreatyToggle(treaty.id)}
                                  className="h-4 w-4 text-slate-600 border-slate-300 rounded focus:ring-slate-500"
                                />
                              </div>
                              <div className="ml-3 text-sm">
                                <label htmlFor={`treaty-${treaty.id}`} className="font-medium text-slate-700 dark:text-slate-300">
                                  {treaty.name}
                                </label>
                                <p className="text-slate-500 dark:text-slate-400">
                                  {treaty.description}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                    
                    {/* Ordinances */}
                    <div>
                      <h4 className="text-md font-medium text-slate-800 dark:text-slate-200 mb-3 flex items-center">
                        <BookOpen className="h-4 w-4 mr-2 text-slate-600 dark:text-slate-400" />
                        Ordinances
                      </h4>
                      <div className="border border-slate-300 dark:border-slate-600 rounded-md p-4">
                        <div className="space-y-3">
                          {availableOrdinances.map((ordinance) => (
                            <div key={ordinance.id} className="flex items-start">
                              <div className="flex items-center h-5">
                                <input
                                  id={`ordinance-${ordinance.id}`}
                                  type="checkbox"
                                  checked={selectedOrdinances.includes(ordinance.id)}
                                  onChange={() => handleOrdinanceToggle(ordinance.id)}
                                  className="h-4 w-4 text-slate-600 border-slate-300 rounded focus:ring-slate-500"
                                />
                              </div>
                              <div className="ml-3 text-sm">
                                <label htmlFor={`ordinance-${ordinance.id}`} className="font-medium text-slate-700 dark:text-slate-300">
                                  {ordinance.name}
                                </label>
                                <p className="text-slate-500 dark:text-slate-400">
                                  {ordinance.description}
                                </p>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="mt-6 flex justify-end">
                    <button
                      onClick={handleSaveAssignments}
                      className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200"
                    >
                      Save Assignments
                    </button>
                  </div>
                </div>
              ) : (
                <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-12 text-center">
                  <FileText className="mx-auto h-12 w-12 text-slate-400" />
                  <h3 className="mt-2 text-lg font-medium text-slate-800 dark:text-slate-200">
                    Select a member
                  </h3>
                  <p className="mt-1 text-sm text-slate-500 dark:text-slate-400">
                    Choose a member from the list to assign treaties and ordinances
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      ) : (
        /* Manage Treaties & Ordinances Tab */
        <div className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Treaties Management */}
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 flex items-center">
                  <FileText className="h-5 w-5 mr-2 text-slate-600 dark:text-slate-400" />
                  Manage Treaties
                </h3>
                <button className="px-3 py-1 bg-slate-700 text-white rounded-md text-sm hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200 flex items-center">
                  <Plus className="h-4 w-4 mr-1" />
                  Add Treaty
                </button>
              </div>
              
              <div className="space-y-4">
                {availableTreaties.map((treaty) => (
                  <div key={treaty.id} className="border border-slate-300 dark:border-slate-600 rounded-md p-4">
                    <div className="flex justify-between">
                      <div>
                        <h4 className="font-medium text-slate-800 dark:text-slate-200">{treaty.name}</h4>
                        <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">{treaty.description}</p>
                      </div>
                      <div className="flex space-x-2">
                        <button className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300">
                          Edit
                        </button>
                        <button className="text-red-500 hover:text-red-700">
                          <X className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            
            {/* Ordinances Management */}
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-slate-800 dark:text-slate-200 flex items-center">
                  <BookOpen className="h-5 w-5 mr-2 text-slate-600 dark:text-slate-400" />
                  Manage Ordinances
                </h3>
                <button className="px-3 py-1 bg-slate-700 text-white rounded-md text-sm hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 transition-colors duration-200 flex items-center">
                  <Plus className="h-4 w-4 mr-1" />
                  Add Ordinance
                </button>
              </div>
              
              <div className="space-y-4">
                {availableOrdinances.map((ordinance) => (
                  <div key={ordinance.id} className="border border-slate-300 dark:border-slate-600 rounded-md p-4">
                    <div className="flex justify-between">
                      <div>
                        <h4 className="font-medium text-slate-800 dark:text-slate-200">{ordinance.name}</h4>
                        <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">{ordinance.description}</p>
                      </div>
                      <div className="flex space-x-2">
                        <button className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-300">
                          Edit
                        </button>
                        <button className="text-red-500 hover:text-red-700">
                          <X className="h-5 w-5" />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};