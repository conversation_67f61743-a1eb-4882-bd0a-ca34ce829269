"use client";

import React, { useState } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Eye, EyeOff, Heart, Globe2, Users } from "lucide-react";
import { signIn } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation";

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('password');
  const [showPassword, setShowPassword] = useState(false);
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get('callbackUrl') || '/dashboard';

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const result = await signIn("credentials", {
        email,
        password,
        redirect: false,
      });

      if (result?.error) {
        setError('Invalid email or password. Please try again.');
      } else {
        // Use the callbackUrl parameter if present and valid, otherwise default to dashboard
        const safeCallbackUrl = callbackUrl && typeof callbackUrl === 'string' && callbackUrl.trim() !== '' ? callbackUrl : '/dashboard';
        router.push(safeCallbackUrl);
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleOAuthLogin = async (provider: string) => {
    setLoading(true);
    setError('');
    
    try {
      // Use the callbackUrl parameter if present and valid, otherwise default to dashboard
      const safeCallbackUrl = callbackUrl && typeof callbackUrl === 'string' && callbackUrl.trim() !== '' ? callbackUrl : '/dashboard';
      await signIn(provider, { callbackUrl: safeCallbackUrl });
    } catch (err) {
      setError('Failed to initiate OAuth login');
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex w-full">
      {/* Left Panel - Green */}
      <div className="flex-1 bg-gradient-to-br from-emerald-600 via-emerald-700 to-emerald-800 relative overflow-hidden flex flex-col justify-center p-12">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute inset-0 bg-gradient-to-br from-white/5 to-transparent"></div>
        </div>
        
        {/* Logo and Brand */}
        <div className="relative z-10 mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <div className="bg-white/20 p-3 rounded-2xl backdrop-blur-sm">
              <Globe2 className="h-8 w-8 text-white" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">New World Alliance</h1>
              <p className="text-emerald-100 text-sm">Member Portal</p>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="relative z-10 max-w-lg">
          <h2 className="text-4xl font-bold text-white mb-6 leading-tight">
            Unlock the Power of<br />
            <span className="text-emerald-200">Global Unity</span>
          </h2>
          
          <p className="text-emerald-100 text-lg mb-8 leading-relaxed">
            Join our mission to serve humanity through peace, love, and prosperity. 
            Together, we&apos;re building a better world for all.
          </p>

          {/* Stats */}
          <div className="flex space-x-8 text-white">
            <div>
              <div className="text-2xl font-bold">15K+</div>
              <div className="text-emerald-200 text-sm">Members</div>
            </div>
            <div>
              <div className="text-2xl font-bold">89K+</div>
              <div className="text-emerald-200 text-sm">Actions</div>
            </div>
            <div>
              <div className="text-2xl font-bold">156</div>
              <div className="text-emerald-200 text-sm">Countries</div>
            </div>
          </div>

          {/* Mission Icons */}
          <div className="flex space-x-6 mt-8">
            <div className="flex items-center space-x-2 text-emerald-100">
              <Heart className="h-5 w-5" />
              <span className="text-sm">Love</span>
            </div>
            <div className="flex items-center space-x-2 text-emerald-100">
              <Globe2 className="h-5 w-5" />
              <span className="text-sm">Peace</span>
            </div>
            <div className="flex items-center space-x-2 text-emerald-100">
              <Users className="h-5 w-5" />
              <span className="text-sm">Prosperity</span>
            </div>
          </div>
        </div>
      </div>

      {/* Right Panel - Dark */}
      <div className="flex-1 bg-slate-900 flex items-center justify-center p-8 relative">
        {/* Background Elements */}
        <div className="absolute top-8 right-8">
          <div className="w-24 h-16 bg-slate-800 rounded-lg opacity-50"></div>
        </div>
        
        {/* Login Form */}
        <div className="w-full max-w-md">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold text-white mb-2">Welcome back</h3>
            <p className="text-slate-400">Sign in to access your member dashboard</p>
          </div>

          {error && (
            <div className="mb-4 p-3 bg-red-900/50 border border-red-700 rounded-lg text-red-200 text-sm">
              {error}
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label htmlFor="email" className="text-slate-300 text-sm">
                Email address
              </Label>
              <div className="relative">
                <Input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="h-12 bg-slate-800 border-slate-700 text-white placeholder:text-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg"
                  placeholder="Enter your email"
                  required
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="password" className="text-slate-300 text-sm">
                Password
              </Label>
              <div className="relative">
                <Input
                  id="password"
                  type={showPassword ? 'text' : 'password'}
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="h-12 bg-slate-800 border-slate-700 text-white placeholder:text-slate-500 focus:border-emerald-500 focus:ring-emerald-500/20 rounded-lg pr-10"
                  placeholder="Enter your password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-slate-500 hover:text-slate-300"
                >
                  {showPassword ? <EyeOff className="h-5 w-5" /> : <Eye className="h-5 w-5" />}
                </button>
              </div>
            </div>

            <Button
              type="submit"
              disabled={loading}
              className="w-full h-12 bg-emerald-600 hover:bg-emerald-700 text-white font-semibold rounded-lg transition-colors disabled:opacity-50"
            >
              {loading ? 'Signing in...' : 'Sign in to Dashboard'}
            </Button>
          </form>

          <div className="relative my-6">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-slate-700"></div>
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-2 bg-slate-900 text-slate-400">Or continue with</span>
            </div>
          </div>

          <Button
            onClick={() => handleOAuthLogin('member-portal')}
            disabled={loading}
            className="w-full h-12 bg-slate-800 hover:bg-slate-700 text-white font-semibold rounded-lg transition-colors disabled:opacity-50 border border-slate-700 flex items-center justify-center"
          >
            {loading ? 'Connecting...' : 'Sign in with Member Portal'}
          </Button>

          <p className="text-xs text-slate-500 text-center mt-6 leading-relaxed">
            By signing in, you agree to our mission of peace, love, and prosperity for all humanity.
          </p>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;