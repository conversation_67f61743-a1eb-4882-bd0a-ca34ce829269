'use client';

import React from 'react';

interface MainContentProps {
  children: React.ReactNode;
}

export const MainContent: React.FC<MainContentProps> = ({ children }) => {
  return (
    <div
      data-testid="main-content"
      className={`flex-1 overflow-y-auto p-4 transition-all duration-300 bg-white
                 sm:p-5
                 md:p-6
                 lg:p-8
                 xl:p-10
                 max-w-full`}
    >
      <div className="max-w-7xl mx-auto">
        {children}
      </div>
    </div>
  );
};