'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  LayoutDashboard,
  User,
  Users,
  FileText,
  BookOpen,
  Settings,
  Briefcase,
  LogOut,
  Menu,
  X,
  UserCircle
} from 'lucide-react';
import { signOut, useSession } from 'next-auth/react';

interface SidebarProps {
  isCollapsed: boolean;
  onToggle: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({ isCollapsed, onToggle }) => {
  const { data: session } = useSession();
  
  const navItems = [
    { icon: LayoutDashboard, label: 'Dashboard', href: '/' },
    { icon: User, label: 'Profile', href: '/profile' },
    { icon: Users, label: 'Members', href: '/members' },
    { icon: Briefcase, label: 'Positions', href: '/positions' },
    { icon: FileText, label: 'Ordinances', href: '/ordinances' },
    { icon: BookOpen, label: 'Treaties', href: '/treaties' },
    { icon: Settings, label: 'System Settings', href: '/settings' },
  ];

  const handleLogout = async () => {
    await signOut({ callbackUrl: '/login' });
  };

  return (
    <aside
      data-testid="sidebar"
      className={`bg-emerald-600 text-white shadow-lg transition-all duration-300 ease-in-out
                 ${isCollapsed ? 'w-20' : 'w-64'} 
                 flex flex-col`}
    >
      {/* Sidebar header with user info and toggle */}
      <div className={`flex items-center p-4 border-b border-emerald-700
                      ${isCollapsed ? 'justify-center' : 'justify-between'}`}>
        {!isCollapsed && session?.user?.name && (
          <div className="flex items-center">
            <UserCircle className="h-8 w-8 flex-shrink-0 text-white mr-2" />
            <span className="font-medium text-emerald-100 truncate max-w-[120px]">
              {session.user.name}
            </span>
          </div>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggle}
          className="text-white hover:bg-emerald-700 ml-auto"
          aria-label={isCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {isCollapsed ? <Menu className="h-5 w-5" /> : <X className="h-5 w-5" />}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 overflow-y-auto py-4">
        <ul className="space-y-1 px-2">
          {navItems.map((item) => {
            const Icon = item.icon;
            return (
              <li key={item.label}>
                <a
                  href={item.href}
                  className={`flex items-center p-3 rounded-lg transition-colors duration-200
                             text-emerald-100 hover:bg-emerald-700 hover:text-white
                             ${isCollapsed ? 'justify-center' : ''}`}
                >
                  <Icon className="h-5 w-5 flex-shrink-0" />
                  {!isCollapsed && (
                    <span className="ml-3 font-medium">{item.label}</span>
                  )}
                </a>
              </li>
            );
          })}
        </ul>
      </nav>

      {/* Sidebar footer with logout button */}
      <div className="p-4 border-t border-emerald-700">
        <button
          onClick={handleLogout}
          className={`flex items-center p-3 rounded-lg w-full text-left transition-colors duration-200
                     bg-emerald-700 text-white hover:bg-emerald-800
                     ${isCollapsed ? 'justify-center' : ''}`}
        >
          <LogOut className="h-5 w-5 flex-shrink-0 text-red-300" />
          {!isCollapsed && (
            <span className="ml-3 font-medium">Logout</span>
          )}
        </button>
      </div>
    </aside>
  );
};