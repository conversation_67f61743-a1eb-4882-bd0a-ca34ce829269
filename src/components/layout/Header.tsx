'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import {
  Bell,
  Search,
  Globe
} from 'lucide-react';
import { signOut } from 'next-auth/react';

interface HeaderProps {
}

export const Header: React.FC<HeaderProps> = () => {
  const handleLogout = async () => {
    await signOut({ callbackUrl: '/login' });
  };

  return (
    <header
      data-testid="header"
      className="bg-slate-800 text-white shadow-lg z-10 w-full"
    >
      <div className="flex items-center justify-between p-4">
        {/* Left side - Globe icon with colorful styling */}
        <div className="flex items-center">
          <Globe className="h-9 w-9 text-blue-400" />
          <span className="ml-2 text-xl font-bold text-white">
            NWA Members Portal
          </span>
        </div>

        {/* Right side - Search and Notifications */}
        <div className="flex items-center space-x-4">
          {/* Search bar */}
          <div className="flex items-center bg-white rounded-lg px-3 py-2 w-64">
            <Search className="h-4 w-4 text-slate-800 mr-2" />
            <input
              type="text"
              placeholder="Search..."
              className="bg-transparent border-none focus:outline-none w-full text-sm text-slate-800 placeholder:text-slate-500"
            />
          </div>

          <Button
            variant="ghost"
            size="icon"
            className="text-white hover:bg-slate-700 relative"
          >
            <Bell className="h-5 w-5" />
            <span className="absolute top-0 right-0 inline-flex items-center justify-center px-2 py-1 text-xs font-bold leading-none text-white transform translate-x-1/2 -translate-y-1/2 bg-red-500 rounded-full">
              3
            </span>
          </Button>
        </div>
      </div>
    </header>
  );
};