'use client';

import React, { useState, useRef } from 'react';
import { toast } from 'sonner';

interface OrdinanceUploadFormProps {
  onUploadSuccess?: () => void;
}

export const OrdinanceUploadForm: React.FC<OrdinanceUploadFormProps> = ({ onUploadSuccess }) => {
  const [title, setTitle] = useState('');
  const [description, setDescription] = useState('');
  const [file, setFile] = useState<File | null>(null);
  const [fileName, setFileName] = useState('');
  const [uploading, setUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFile = e.target.files?.[0];
    if (selectedFile) {
      // Check file type - only allow PDF files
      const allowedTypes = ['application/pdf'];
      if (!allowedTypes.includes(selectedFile.type)) {
        toast.error('Invalid file type. Please upload a PDF file.');
        return;
      }
      
      // Check file size (5MB limit)
      if (selectedFile.size > 5 * 1024 * 1024) {
        toast.error('File size exceeds 5MB limit.');
        return;
      }
      
      setFile(selectedFile);
      setFileName(selectedFile.name);
    }
  };

  const triggerFileInput = () => {
    fileInputRef.current?.click();
  };

  const removeFile = () => {
    setFile(null);
    setFileName('');
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!title.trim()) {
      toast.error('Please enter a title for the ordinance.');
      return;
    }
    
    if (!file) {
      toast.error('Please select a document to upload.');
      return;
    }
    
    setUploading(true);
    
    try {
      // First create the ordinance record with a default ordinance type
      const ordinanceResponse = await fetch('/api/ordinances', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          ordinanceTypeId: 'cmedvx2jg000ippco09c5b1qx', // Default Test Ordinance type
          notes: description,
        }),
      });
      
      if (!ordinanceResponse.ok) {
        const errorText = await ordinanceResponse.text();
        let errorMessage = 'Failed to create ordinance record';
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          errorMessage = errorText || errorMessage;
        }
        toast.error(errorMessage);
        setUploading(false);
        return;
      }
      
      const ordinanceData = await ordinanceResponse.json();
      const ordinanceId = ordinanceData.id;
      
      // Then upload the document file
      const formData = new FormData();
      formData.append('document', file);
      
      const uploadResponse = await fetch(`/api/ordinances/upload?ordinanceId=${ordinanceId}`, {
        method: 'POST',
        body: formData,
      });
      
      if (!uploadResponse.ok) {
        const errorText = await uploadResponse.text();
        let errorMessage = 'Failed to upload document';
        try {
          const errorData = JSON.parse(errorText);
          errorMessage = errorData.error || errorMessage;
        } catch (e) {
          errorMessage = errorText || errorMessage;
        }
        toast.error(errorMessage);
        setUploading(false);
        return;
      }
      
      // Success - show success message
      toast.success('Ordinance uploaded successfully!');
      
      // Reset form
      setTitle('');
      setDescription('');
      setFile(null);
      setFileName('');
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
      
      // Notify parent component
      if (onUploadSuccess) {
        onUploadSuccess();
      }
    } catch (error: any) {
      toast.error('An unexpected error occurred. Please try again.');
    } finally {
      setUploading(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label htmlFor="title" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
          Ordinance Title
        </label>
        <input
          type="text"
          id="title"
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
          required
        />
      </div>
      
      <div>
        <label htmlFor="description" className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-1">
          Description
        </label>
        <textarea
          id="description"
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-slate-300 dark:border-slate-600 rounded-md shadow-sm focus:outline-none focus:ring-slate-500 focus:border-slate-500 dark:bg-slate-700 dark:text-white"
        />
      </div>
      
      <div>
        <label className="block text-sm font-medium text-slate-700 dark:text-slate-300 mb-3">
          Document
        </label>
        <div className="flex items-center space-x-4 mb-3 mt-3">
          <input
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".pdf,application/pdf"
            className="hidden"
          />
          <button
            type="button"
            onClick={triggerFileInput}
            className="px-4 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 text-sm transition-colors duration-200"
          >
            Choose File
          </button>
          {fileName && (
            <div className="flex items-center space-x-2">
              <span className="text-sm text-slate-600 dark:text-slate-400 truncate max-w-xs">
                {fileName}
              </span>
              <button
                type="button"
                onClick={removeFile}
                className="text-red-500 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
              >
                Remove
              </button>
            </div>
          )}
        </div>
        <p className="mt-1 text-xs text-slate-500 dark:text-slate-400">
          PDF files only. Max 5MB.
        </p>
      </div>
      
      <div className="flex justify-end">
        <button
          type="submit"
          disabled={uploading}
          className="px-6 py-2 bg-slate-700 text-white rounded-md hover:bg-slate-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-slate-500 disabled:opacity-50 transition-colors duration-200"
        >
          {uploading ? 'Saving...' : 'Save Ordinance'}
        </button>
      </div>
    </form>
  );
};