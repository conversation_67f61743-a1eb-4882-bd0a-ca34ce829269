'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { FileText, Download, Calendar, AlertCircle } from 'lucide-react';
import { toast } from 'sonner';

interface OrdinanceDetails {
  id: string;
  title: string;
  ordinanceTypeName: string;
  status: string;
  notes: string | null;
  completedDate: string | null;
  expirationDate: string | null;
  documentPath: string | null;
  createdAt: string;
  updatedAt: string;
}

interface OrdinanceViewModalProps {
  ordinanceId: string | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onViewDetails?: (id: string) => void;
}

export const OrdinanceViewModal: React.FC<OrdinanceViewModalProps> = ({ 
  ordinanceId, 
  open, 
  onOpenChange 
}) => {
  const [ordinance, setOrdinance] = useState<OrdinanceDetails | null>(null);
  const [loading, setLoading] = useState(false);

  const fetchOrdinanceDetails = useCallback(async () => {
    if (!ordinanceId) return;
    
    setLoading(true);
    try {
      const response = await fetch(`/api/ordinances?id=${ordinanceId}`);
      
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to fetch ordinance details');
      }
      
      const data = await response.json();
      setOrdinance(data);
    } catch (error: any) {
      toast.error(error.message || 'Failed to load ordinance details');
      console.error('Error fetching ordinance details:', error);
    } finally {
      setLoading(false);
    }
  }, [ordinanceId]);

  useEffect(() => {
    if (open && ordinanceId) {
      fetchOrdinanceDetails();
    }
  }, [open, ordinanceId, fetchOrdinanceDetails]);

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'pending':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200';
      case 'expired':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  if (loading) {
    return (
      <Dialog open={open} onOpenChange={onOpenChange}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Ordinance Details</DialogTitle>
          </DialogHeader>
          <div className="animate-pulse space-y-4">
            <div className="h-6 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
            <div className="h-20 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <FileText className="h-5 w-5 mr-2 text-blue-500" />
            Ordinance Details
          </DialogTitle>
        </DialogHeader>
        
        {ordinance && (
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                {ordinance.title}
              </h3>
              <div className="mt-2 flex items-center space-x-2">
                <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(ordinance.status)}`}>
                  {ordinance.status.replace('_', ' ')}
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400">
                  {ordinance.ordinanceTypeName}
                </span>
              </div>
            </div>
            
            {ordinance.notes && (
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1">Description</h4>
                <p className="text-gray-700 dark:text-gray-300">{ordinance.notes}</p>
              </div>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1 flex items-center">
                  <Calendar className="h-4 w-4 mr-1" />
                  Created
                </h4>
                <p className="text-gray-700 dark:text-gray-300">{formatDate(ordinance.createdAt)}</p>
              </div>
              
              {ordinance.completedDate && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1 flex items-center">
                    <Calendar className="h-4 w-4 mr-1" />
                    Completed
                  </h4>
                  <p className="text-gray-700 dark:text-gray-300">{formatDate(ordinance.completedDate)}</p>
                </div>
              )}
              
              {ordinance.expirationDate && (
                <div>
                  <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-1 flex items-center">
                    <AlertCircle className="h-4 w-4 mr-1" />
                    Expires
                  </h4>
                  <p className="text-gray-700 dark:text-gray-300">{formatDate(ordinance.expirationDate)}</p>
                </div>
              )}
            </div>
            
            {ordinance.documentPath && (
              <div className="pt-4">
                <Button 
                  onClick={() => window.open(ordinance.documentPath!, '_blank')}
                  className="w-full"
                >
                  <Download className="h-4 w-4 mr-2" />
                  Download Document
                </Button>
              </div>
            )}
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};