'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff, Lock, AlertCircle } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useSession } from 'next-auth/react';

interface ApiKeyDisplayProps {
    apiKey: string;
    onVerify?: (success: boolean) => void;
}

export function ApiKeyDisplay({ apiKey, onVerify }: ApiKeyDisplayProps) {
    const { data: session } = useSession();
    const [showApiKey, setShowApiKey] = useState(false);
    const [isVerifying, setIsVerifying] = useState(false);
    const [password, setPassword] = useState('');
    const [error, setError] = useState<string | null>(null);
    const [isVerified, setIsVerified] = useState(false);
    const [isAdmin, setIsAdmin] = useState(false);

    const handleVerifyPassword = async () => {
        if (!password.trim()) {
            setError('Please enter your password');
            return;
        }

        setIsVerifying(true);
        setError(null);

        try {
            // Verify password with the backend
            const response = await fetch('/api/user/verify-password', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ password }),
                credentials: 'include'
            });

            const result = await response.json();

            if (result.success) {
                setIsVerified(true);
                setShowApiKey(true);
                setPassword('');
                setError(null);
                onVerify?.(true);
            } else {
                setError(result.error || 'Invalid password');
                onVerify?.(false);
            }
        } catch (err) {
            setError('Failed to verify password. Please try again.');
            onVerify?.(false);
        } finally {
            setIsVerifying(false);
        }
    };

    const toggleApiKeyVisibility = async () => {
        // If already showing the API key, just toggle visibility
        if (isVerified && showApiKey) {
            setShowApiKey(false);
            return;
        }
        
        // If we've already verified, show the API key
        if (isVerified) {
            setShowApiKey(true);
            return;
        }
        
        // Check admin status when user tries to view the API key
        try {
            const response = await fetch('/api/user/check-admin', {
                credentials: 'include'
            });
            const result = await response.json();
            const userIsAdmin = result.isAdmin;
            setIsAdmin(userIsAdmin);
            
            if (!userIsAdmin) {
                // Show error but don't block the UI
                setError('You need admin privileges to view API keys.');
                return;
            }
            
            // User is admin, show password verification
            setIsVerifying(true);
            setError(null); // Clear any previous errors
        } catch (error) {
            console.error('Failed to check admin status:', error);
            setError('Failed to verify admin status. Please try again.');
        }
    };

    const handleKeyPress = (e: React.KeyboardEvent) => {
        if (e.key === 'Enter') {
            handleVerifyPassword();
        }
    };

    // Show masked API key by default
    if (!isVerifying && !isVerified) {
        return (
            <div className="space-y-2">
                <Label className="text-sm font-medium text-gray-700">API Key</Label>
                <div className="flex space-x-2">
                    <Input
                        type="password"
                        value="••••••••••••••••••••••••••••••••••••••••"
                        readOnly
                        className="flex-1 font-mono text-sm"
                    />
                    <Button
                        variant="outline"
                        size="icon"
                        onClick={() => {
                            setError(null); // Clear any previous errors
                            toggleApiKeyVisibility();
                        }}
                        className="flex-shrink-0"
                    >
                        <Eye className="h-4 w-4" />
                    </Button>
                </div>
                <p className="text-xs text-gray-500">
                    Click the eye icon to reveal the API key
                </p>
                {error && (
                    <Alert variant="destructive" className="alert-white">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{error}</AlertDescription>
                    </Alert>
                )}
            </div>
        );
    }

    // Show password verification if we're in verification mode
    if (isVerifying && !isVerified) {
        return (
            <div className="space-y-3">
                <Label htmlFor="password" className="text-sm font-medium">
                    Verify Your Password to View API Key
                </Label>
                <div className="flex space-x-2">
                    <Input
                        id="password"
                        type="password"
                        placeholder="Enter your password"
                        value={password}
                        onChange={(e) => setPassword(e.target.value)}
                        onKeyPress={handleKeyPress}
                        className="flex-1"
                    />
                    <Button
                        onClick={handleVerifyPassword}
                        disabled={isVerifying}
                        className="flex items-center space-x-2"
                    >
                        <Lock className="h-4 w-4" />
                        {isVerifying ? 'Verifying...' : 'Verify'}
                    </Button>
                </div>
                {error && (
                    <Alert variant="destructive" className="alert-white">
                        <AlertCircle className="h-4 w-4" />
                        <AlertDescription>{error}</AlertDescription>
                    </Alert>
                )}
            </div>
        );
    }
} 