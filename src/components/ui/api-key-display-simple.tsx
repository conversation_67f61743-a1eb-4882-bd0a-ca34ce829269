'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Eye, EyeOff } from 'lucide-react';

interface ApiKeyDisplayProps {
    apiKey: string;
}

export function ApiKeyDisplay({ apiKey }: ApiKeyDisplayProps) {
    const [showApiKey, setShowApiKey] = useState(false);

    const toggleApiKeyVisibility = () => {
        setShowApiKey(!showApiKey);
    };

    return (
        <div className="space-y-2">
            <div className="flex space-x-2">
                <Input
                    type={showApiKey ? "text" : "password"}
                    value={apiKey}
                    readOnly
                    className="flex-1 font-mono text-sm"
                />
                <Button
                    variant="outline"
                    size="icon"
                    onClick={toggleApiKeyVisibility}
                    className="flex-shrink-0"
                >
                    {showApiKey ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </Button>
            </div>
            <p className="text-xs text-gray-500">
                Click the eye icon to {showApiKey ? 'hide' : 'reveal'} the API key
            </p>
        </div>
    );
}