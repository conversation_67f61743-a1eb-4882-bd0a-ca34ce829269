import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"

import { cn } from "@/lib/utils"

const buttonVariants = cva(
  "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-emerald-500 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",
  {
    variants: {
      variant: {
        default:
          "bg-emerald-600 text-white shadow hover:bg-emerald-700 focus:ring-2 focus:ring-emerald-500",
        destructive:
          "bg-red-600 text-white shadow-sm hover:bg-red-700 focus:ring-2 focus:ring-red-500",
        outline:
          "border border-black bg-white text-black shadow-sm hover:bg-gray-100 focus:ring-2 focus:ring-emerald-500",
        secondary:
          "bg-gray-200 text-gray-900 shadow-sm hover:bg-gray-300 focus:ring-2 focus:ring-emerald-500",
        ghost: "hover:bg-gray-100 hover:text-gray-900 focus:ring-2 focus:ring-emerald-500",
        link: "text-emerald-600 underline-offset-4 hover:underline focus:ring-2 focus:ring-emerald-500",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
)

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
  VariantProps<typeof buttonVariants> {
  asChild?: boolean
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
Button.displayName = "Button"

export { Button, buttonVariants }