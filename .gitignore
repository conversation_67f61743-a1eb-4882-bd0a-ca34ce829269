# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz
.agent-os/
# testing
/coverage
/.nyc_output

# next.js
/.next/
/out/

# production
/build
/dist

# misc
.DS_Store
*.pem
.vscode/
.idea/

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
*.log

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# backup files
/backup

# temporary files
/tmp
/temp

# OS generated files
Thumbs.db
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# File watching
.watchmanconfig

.qwen/
test-minio.js
test-minio.mjs
.claude/settings.local.json
