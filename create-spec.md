# Remote Server Template Enhancement Specification

## Overview
This document outlines the improvements to be made to the remote server template to include client ID, callback URL, and secret key configurations. These enhancements will improve the OAuth2 integration and security of remote server connections.

## Current State
The current remote server implementation includes:
- Basic server registration with URL and API key
- OAuth2 client credentials (client_id and client_secret) generation
- Simple permission synchronization

## Proposed Enhancements

### 1. Enhanced Remote Server Model
Update the `remote_servers` table to include additional configuration fields:

```sql
ALTER TABLE remote_servers ADD COLUMN callback_url TEXT;
ALTER TABLE remote_servers ADD COLUMN allowed_origins TEXT[];
ALTER TABLE remote_servers ADD COLUMN token_endpoint_auth_method TEXT DEFAULT 'client_secret_basic';
```

### 2. Client Configuration Template
Create a standardized template for remote server configuration:

```json
{
  "client_id": "generated_client_id",
  "client_secret": "generated_client_secret",
  "redirect_uris": [
    "https://example.com/callback",
    "https://example.com/oauth/callback"
  ],
  "allowed_origins": [
    "https://example.com",
    "https://subdomain.example.com"
  ],
  "token_endpoint_auth_method": "client_secret_basic",
  "grant_types": [
    "authorization_code",
    "refresh_token"
  ],
  "response_types": [
    "code"
  ],
  "default_scopes": [
    "read:profile",
    "write:profile"
  ]
}
```

### 3. API Endpoints

#### a. Update Remote Server Configuration
`PUT /api/remote-servers/{id}/config`

Request Body:
```json
{
  "callback_url": "https://example.com/oauth/callback",
  "allowed_origins": [
    "https://example.com"
  ],
  "token_endpoint_auth_method": "client_secret_basic",
  "redirect_uris": [
    "https://example.com/oauth/callback"
  ]
}
```

#### b. Get Client Configuration
`GET /api/remote-servers/{id}/client-config`

Response:
```json
{
  "client_id": "generated_client_id",
  "redirect_uris": [
    "https://example.com/oauth/callback"
  ],
  "grant_types": [
    "authorization_code",
    "refresh_token"
  ],
  "response_types": [
    "code"
  ],
  "default_scopes": [
    "read:profile"
  ]
}
```

### 4. Security Improvements

#### a. Secret Key Rotation
Implement a mechanism for rotating client secrets:
- Generate new secrets without invalidating existing ones immediately
- Allow a grace period for clients to update their configurations
- Automatically invalidate old secrets after the grace period

#### b. Enhanced Validation
- Validate redirect URIs against allowed origins
- Implement stricter client authentication methods
- Add rate limiting for client configuration endpoints

### 5. UI/UX Improvements

#### a. Remote Server Management Interface
Enhance the existing remote server management UI to include:
- Client ID display (non-editable)
- Callback URL configuration
- Secret key regeneration button
- Redirect URI management
- Allowed origins configuration

#### b. Client Configuration Display
Create a dedicated view for displaying client configuration information:
- Client credentials (with option to reveal secret)
- Configuration template for easy copying
- Integration instructions

### 6. Implementation Steps

1. **Database Schema Updates**
   - Add new columns to `remote_servers` table
   - Create indexes for improved query performance

2. **API Endpoint Development**
   - Implement new configuration endpoints
   - Update existing registration endpoint to handle new fields
   - Add validation for new configuration options

3. **Service Layer Enhancements**
   - Update OAuth service to handle new configuration options
   - Implement secret rotation functionality
   - Add enhanced validation logic

4. **UI Implementation**
   - Update remote server management components
   - Create new client configuration display components
   - Add form validation for new fields

5. **Testing**
   - Unit tests for new API endpoints
   - Integration tests for OAuth flow with new configurations
   - UI component tests

6. **Documentation**
   - Update API documentation
   - Create integration guide for remote servers
   - Update user documentation

### 7. Security Considerations

- Client secrets should be properly encrypted at rest
- Implement proper access controls for configuration endpoints
- Add audit logging for configuration changes
- Validate all input to prevent injection attacks
- Implement rate limiting to prevent abuse

### 8. Backward Compatibility

- Ensure existing remote servers continue to function without changes
- Provide migration path for existing configurations
- Maintain support for simplified configuration options

## Conclusion
These enhancements will provide a more robust and secure foundation for remote server integrations, with better configuration management and improved security practices.