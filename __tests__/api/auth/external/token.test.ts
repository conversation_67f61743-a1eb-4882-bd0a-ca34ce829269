import { NextRequest } from 'next/server';
import { POST as tokenPOST } from '@/app/api/auth/external/token/route';
import { prisma } from '@/lib/prisma';
import { JwtService } from '@/lib/services/jwt';
import { ApiKeyService } from '@/lib/services/api-key';

// Mock Prisma client
jest.mock('@/lib/prisma', () => ({
  prisma: {
    project: {
      findUnique: jest.fn(),
    },
    userProjectScope: {
      findFirst: jest.fn(),
    },
  },
}));

// Mock JWT service
jest.mock('@/lib/services/jwt', () => ({
  JwtService: jest.fn().mockImplementation(() => ({
    signToken: jest.fn().mockResolvedValue('test_jwt_token'),
  })),
}));

// Mock API key service
jest.mock('@/lib/services/api-key', () => ({
  ApiKeyService: jest.fn().mockImplementation(() => ({
    isValidApiKeyFormat: jest.fn(),
    validateApiKey: jest.fn(),
    hashApiKey: jest.fn(),
  })),
}));

// Mock NextResponse
const mockJson = jest.fn();
const mockNext = jest.fn();
jest.mock('next/server', () => ({
  NextResponse: {
    json: mockJson,
    next: mockNext,
  },
}));

describe('POST /api/auth/external/token', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should generate a JWT token for a valid project', async () => {
    // Mock request
    const request = {
      json: jest.fn().mockResolvedValue({}),
      headers: {
        get: jest.fn((key) => {
          switch (key) {
            case 'x-project-id':
              return 'project_123';
            default:
              return null;
          }
        }),
      },
    } as unknown as NextRequest;

    // Mock Prisma responses
    (prisma.project.findUnique as jest.Mock).mockResolvedValue({
      id: 'project_123',
      name: 'Test Project',
    });

    // Call the endpoint
    await tokenPOST(request);

    // Verify response
    expect(mockJson).toHaveBeenCalledWith({
      token: 'test_jwt_token',
      expiresIn: 3600,
      tokenType: 'Bearer',
    });
  });

  it('should generate a JWT token for a user with access to the project', async () => {
    // Mock request
    const request = {
      json: jest.fn().mockResolvedValue({ userId: 'user_123' }),
      headers: {
        get: jest.fn((key) => {
          switch (key) {
            case 'x-project-id':
              return 'project_123';
            default:
              return null;
          }
        }),
      },
    } as unknown as NextRequest;

    // Mock Prisma responses
    (prisma.project.findUnique as jest.Mock).mockResolvedValue({
      id: 'project_123',
      name: 'Test Project',
    });

    (prisma.userProjectScope.findFirst as jest.Mock).mockResolvedValue({
      id: 'ups_123',
      userId: 'user_123',
      projectId: 'project_123',
      scopeId: 'scope_123',
    });

    // Call the endpoint
    await tokenPOST(request);

    // Verify response
    expect(mockJson).toHaveBeenCalledWith({
      token: 'test_jwt_token',
      expiresIn: 3600,
      tokenType: 'Bearer',
    });
  });

  it('should return 403 when user does not have access to the project', async () => {
    // Mock request
    const request = {
      json: jest.fn().mockResolvedValue({ userId: 'user_123' }),
      headers: {
        get: jest.fn((key) => {
          switch (key) {
            case 'x-project-id':
              return 'project_123';
            default:
              return null;
          }
        }),
      },
    } as unknown as NextRequest;

    // Mock Prisma responses
    (prisma.project.findUnique as jest.Mock).mockResolvedValue({
      id: 'project_123',
      name: 'Test Project',
    });

    (prisma.userProjectScope.findFirst as jest.Mock).mockResolvedValue(null);

    // Call the endpoint
    await tokenPOST(request);

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Forbidden',
        message: 'User does not have access to this project',
      }),
      { status: 403 }
    );
  });

  it('should return 400 for invalid request body', async () => {
    // Mock request with invalid body
    const request = {
      json: jest.fn().mockResolvedValue({ userId: 123 }), // Should be string
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    // Call the endpoint
    await tokenPOST(request);

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Bad Request',
      }),
      { status: 400 }
    );
  });
});