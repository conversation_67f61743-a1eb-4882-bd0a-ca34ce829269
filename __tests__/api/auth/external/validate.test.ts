import { NextRequest } from 'next/server';
import { POST as validatePOST } from '@/app/api/auth/external/validate/route';
import { prisma } from '@/lib/prisma';
import { JwtService } from '@/lib/services/jwt';
import { ApiKeyService } from '@/lib/services/api-key';

// Mock Prisma client
jest.mock('@/lib/prisma', () => ({
  prisma: {
    project: {
      findUnique: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
    },
  },
}));

// Mock JWT service
jest.mock('@/lib/services/jwt', () => ({
  JwtService: jest.fn().mockImplementation(() => ({
    verifyToken: jest.fn(),
  })),
}));

// Mock API key service
jest.mock('@/lib/services/api-key', () => ({
  ApiKeyService: jest.fn().mockImplementation(() => ({
    isValidApiKeyFormat: jest.fn(),
    validateApiKey: jest.fn(),
    hashApiKey: jest.fn(),
  })),
}));

// Mock NextResponse
const mockJson = jest.fn();
const mockNext = jest.fn();
jest.mock('next/server', () => ({
  NextResponse: {
    json: mockJson,
    next: mockNext,
  },
}));

describe('POST /api/auth/external/validate', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should validate a valid API key and JWT token', async () => {
    // Mock request
    const request = {
      json: jest.fn().mockResolvedValue({ requiredScopes: [] }),
      headers: {
        get: jest.fn((key) => {
          switch (key) {
            case 'x-project-id':
              return 'project_123';
            case 'x-user-id':
              return 'user_123';
            case 'x-scopes':
              return JSON.stringify(['read:users']);
            default:
              return null;
          }
        }),
      },
    } as unknown as NextRequest;

    // Mock Prisma responses
    (prisma.project.findUnique as jest.Mock).mockResolvedValue({
      id: 'project_123',
      name: 'Test Project',
    });

    (prisma.user.findUnique as jest.Mock).mockResolvedValue({
      id: 'user_123',
      name: 'Test User',
      email: '<EMAIL>',
    });

    // Call the endpoint
    await validatePOST(request);

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      {
        valid: true,
        project: {
          id: 'project_123',
          name: 'Test Project',
        },
        user: {
          id: 'user_123',
          name: 'Test User',
          email: '<EMAIL>',
        },
        scopes: ['read:users'],
      }
    );
  });

  it('should return 400 for invalid request body', async () => {
    // Mock request with invalid body
    const request = {
      json: jest.fn().mockResolvedValue({ requiredScopes: 'invalid' }), // Should be array
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    // Call the endpoint
    await validatePOST(request);

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Bad Request',
      }),
      { status: 400 }
    );
  });

  it('should return 403 for insufficient scopes', async () => {
    // Mock request
    const request = {
      json: jest.fn().mockResolvedValue({ requiredScopes: ['write:users'] }),
      headers: {
        get: jest.fn((key) => {
          switch (key) {
            case 'x-project-id':
              return 'project_123';
            case 'x-user-id':
              return 'user_123';
            case 'x-scopes':
              return JSON.stringify(['read:users']);
            default:
              return null;
          }
        }),
      },
    } as unknown as NextRequest;

    // Call the endpoint
    await validatePOST(request);

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Forbidden',
        message: 'Missing required permissions: write:users',
      }),
      { status: 403 }
    );
  });
});