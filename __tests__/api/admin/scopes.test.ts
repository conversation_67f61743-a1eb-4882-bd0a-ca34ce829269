import { NextRequest } from 'next/server';
import { GET as scopesGET, POST as scopesPOST } from '@/app/api/admin/scopes/route';
import { prisma } from '@/lib/prisma';

// Mock Prisma client
jest.mock('@/lib/prisma', () => ({
  prisma: {
    scope: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
      create: jest.fn(),
    },
  },
}));

// Mock NextResponse
const mockJson = jest.fn();
const mockNext = jest.fn();
jest.mock('next/server', () => ({
  NextResponse: {
    json: mockJson,
    next: mockNext,
  },
}));

describe('GET /api/admin/scopes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return a list of scopes', async () => {
    // Mock request
    const request = {
      url: 'http://localhost:3000/api/admin/scopes',
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    // Mock Prisma response
    (prisma.scope.findMany as jest.Mock).mockResolvedValue([
      {
        id: 'scope_123',
        name: 'read:users',
        description: 'Read user profile information',
        category: 'users',
        isActive: true,
        createdAt: new Date(),
      },
    ]);

    // Call the endpoint
    await scopesGET(request);

    // Verify response
    expect(mockJson).toHaveBeenCalledWith({
      scopes: [
        {
          id: 'scope_123',
          name: 'read:users',
          description: 'Read user profile information',
          category: 'users',
          isActive: true,
          createdAt: expect.any(Date),
        },
      ],
    });
  });

  it('should filter scopes by category', async () => {
    // Mock request with category filter
    const request = {
      url: 'http://localhost:3000/api/admin/scopes?category=users',
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    // Mock Prisma response
    (prisma.scope.findMany as jest.Mock).mockResolvedValue([
      {
        id: 'scope_123',
        name: 'read:users',
        description: 'Read user profile information',
        category: 'users',
        isActive: true,
        createdAt: new Date(),
      },
    ]);

    // Call the endpoint
    await scopesGET(request);

    // Verify Prisma was called with correct filter
    expect(prisma.scope.findMany).toHaveBeenCalledWith(
      expect.objectContaining({
        where: expect.objectContaining({
          category: 'users',
        }),
      })
    );
  });
});

describe('POST /api/admin/scopes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should create a new scope', async () => {
    // Mock request
    const request = {
      json: jest.fn().mockResolvedValue({
        name: 'write:users',
        description: 'Create and update user profiles',
        category: 'users',
      }),
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    // Mock Prisma responses
    (prisma.scope.findUnique as jest.Mock).mockResolvedValue(null);

    (prisma.scope.create as jest.Mock).mockResolvedValue({
      id: 'scope_456',
      name: 'write:users',
      description: 'Create and update user profiles',
      category: 'users',
      isActive: true,
      createdAt: new Date(),
    });

    // Call the endpoint
    await scopesPOST(request);

    // Verify response
    expect(mockJson).toHaveBeenCalledWith({
      scope: {
        id: 'scope_456',
        name: 'write:users',
        description: 'Create and update user profiles',
        category: 'users',
        isActive: true,
        createdAt: expect.any(Date),
      },
    });
  });

  it('should return 409 when scope already exists', async () => {
    // Mock request
    const request = {
      json: jest.fn().mockResolvedValue({
        name: 'read:users', // Already exists
        description: 'Read user profile information',
        category: 'users',
      }),
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    // Mock Prisma response
    (prisma.scope.findUnique as jest.Mock).mockResolvedValue({
      id: 'scope_123',
      name: 'read:users',
    });

    // Call the endpoint
    await scopesPOST(request);

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Conflict',
        message: 'Scope with this name already exists',
      }),
      { status: 409 }
    );
  });

  it('should return 400 for invalid request body', async () => {
    // Mock request with invalid body
    const request = {
      json: jest.fn().mockResolvedValue({
        name: '', // Should not be empty
      }),
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    // Call the endpoint
    await scopesPOST(request);

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Bad Request',
      }),
      { status: 400 }
    );
  });
});