import { NextRequest } from 'next/server';
import { GET as projectsGET, POST as projectsPOST } from '@/app/api/admin/projects/route';
import { prisma } from '@/lib/prisma';
import { ApiKeyService } from '@/lib/services/api-key';

// Mock Prisma client
jest.mock('@/lib/prisma', () => ({
  prisma: {
    project: {
      findMany: jest.fn(),
      count: jest.fn(),
      create: jest.fn(),
    },
  },
}));

// Mock API key service
jest.mock('@/lib/services/api-key', () => ({
  ApiKeyService: jest.fn().mockImplementation(() => ({
    generateApiKey: jest.fn().mockReturnValue('nwa_test_api_key'),
    hashApiKey: jest.fn().mockReturnValue('hashed_api_key'),
  })),
}));

// Mock NextResponse
const mockJson = jest.fn();
const mockNext = jest.fn();
jest.mock('next/server', () => ({
  NextResponse: {
    json: mockJson,
    next: mockNext,
  },
}));

describe('GET /api/admin/projects', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return a list of projects with pagination', async () => {
    // Mock request
    const request = {
      url: 'http://localhost:3000/api/admin/projects?page=1&limit=25',
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    // Mock Prisma responses
    (prisma.project.findMany as jest.Mock).mockResolvedValue([
      {
        id: 'project_123',
        name: 'Test Project',
        description: 'A test project',
        allowedOrigins: ['https://example.com'],
        isActive: true,
        createdAt: new Date(),
      },
    ]);

    (prisma.project.count as jest.Mock).mockResolvedValue(1);

    // Call the endpoint
    await projectsGET(request);

    // Verify response
    expect(mockJson).toHaveBeenCalledWith({
      projects: [
        {
          id: 'project_123',
          name: 'Test Project',
          description: 'A test project',
          allowedOrigins: ['https://example.com'],
          isActive: true,
          createdAt: expect.any(Date),
        },
      ],
      pagination: {
        page: 1,
        limit: 25,
        total: 1,
      },
    });
  });

  it('should return 400 for invalid pagination parameters', async () => {
    // Mock request with invalid pagination
    const request = {
      url: 'http://localhost:3000/api/admin/projects?page=0&limit=0',
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    // Call the endpoint
    await projectsGET(request);

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Bad Request',
        message: 'Invalid pagination parameters',
      }),
      { status: 400 }
    );
  });
});

describe('POST /api/admin/projects', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should create a new project with an API key', async () => {
    // Mock request
    const request = {
      json: jest.fn().mockResolvedValue({
        name: 'New Project',
        description: 'A new test project',
        allowedOrigins: ['https://newproject.com'],
      }),
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    // Mock Prisma response
    (prisma.project.create as jest.Mock).mockResolvedValue({
      id: 'project_456',
      name: 'New Project',
      description: 'A new test project',
      allowedOrigins: ['https://newproject.com'],
      isActive: true,
      createdAt: new Date(),
    });

    // Call the endpoint
    await projectsPOST(request);

    // Verify response
    expect(mockJson).toHaveBeenCalledWith({
      project: {
        id: 'project_456',
        name: 'New Project',
        description: 'A new test project',
        allowedOrigins: ['https://newproject.com'],
        isActive: true,
        createdAt: expect.any(Date),
        apiKey: 'nwa_test_api_key',
      },
    });
  });

  it('should return 400 for invalid request body', async () => {
    // Mock request with invalid body
    const request = {
      json: jest.fn().mockResolvedValue({
        name: '', // Should not be empty
      }),
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    // Call the endpoint
    await projectsPOST(request);

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Bad Request',
      }),
      { status: 400 }
    );
  });
});