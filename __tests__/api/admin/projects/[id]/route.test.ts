import { NextRequest } from 'next/server';
import { GET as projectGET, PUT as projectPUT, DELETE as projectDELETE } from '@/app/api/admin/projects/[id]/route';
import { prisma } from '@/lib/prisma';

// Mock Prisma client
jest.mock('@/lib/prisma', () => ({
  prisma: {
    project: {
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
  },
}));

// Mock NextResponse
const mockJson = jest.fn();
const mockNext = jest.fn();
jest.mock('next/server', () => ({
  NextResponse: {
    json: mockJson,
    next: mockNext,
  },
}));

describe('GET /api/admin/projects/[id]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return project details', async () => {
    // Mock request
    const request = {
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { id: 'project_123' };

    // Mock Prisma response
    (prisma.project.findUnique as jest.Mock).mockResolvedValue({
      id: 'project_123',
      name: 'Test Project',
      description: 'A test project',
      allowedOrigins: ['https://example.com'],
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Call the endpoint
    await projectGET(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith({
      project: {
        id: 'project_123',
        name: 'Test Project',
        description: 'A test project',
        allowedOrigins: ['https://example.com'],
        isActive: true,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      },
    });
  });

  it('should return 404 when project is not found', async () => {
    // Mock request
    const request = {
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { id: 'nonexistent_project' };

    // Mock Prisma response
    (prisma.project.findUnique as jest.Mock).mockResolvedValue(null);

    // Call the endpoint
    await projectGET(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Not Found',
        message: 'Project not found',
      }),
      { status: 404 }
    );
  });
});

describe('PUT /api/admin/projects/[id]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should update project details', async () => {
    // Mock request
    const request = {
      json: jest.fn().mockResolvedValue({
        name: 'Updated Project Name',
        description: 'An updated description',
      }),
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { id: 'project_123' };

    // Mock Prisma responses
    (prisma.project.findUnique as jest.Mock).mockResolvedValue({
      id: 'project_123',
      name: 'Test Project',
      description: 'A test project',
      allowedOrigins: ['https://example.com'],
      isActive: true,
    });

    (prisma.project.update as jest.Mock).mockResolvedValue({
      id: 'project_123',
      name: 'Updated Project Name',
      description: 'An updated description',
      allowedOrigins: ['https://example.com'],
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    // Call the endpoint
    await projectPUT(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith({
      project: {
        id: 'project_123',
        name: 'Updated Project Name',
        description: 'An updated description',
        allowedOrigins: ['https://example.com'],
        isActive: true,
        createdAt: expect.any(Date),
        updatedAt: expect.any(Date),
      },
    });
  });

  it('should return 404 when project is not found', async () => {
    // Mock request
    const request = {
      json: jest.fn().mockResolvedValue({
        name: 'Updated Project Name',
      }),
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { id: 'nonexistent_project' };

    // Mock Prisma response
    (prisma.project.findUnique as jest.Mock).mockResolvedValue(null);

    // Call the endpoint
    await projectPUT(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Not Found',
        message: 'Project not found',
      }),
      { status: 404 }
    );
  });

  it('should return 400 for invalid request body', async () => {
    // Mock request with invalid body
    const request = {
      json: jest.fn().mockResolvedValue({
        name: '', // Should not be empty
      }),
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { id: 'project_123' };

    // Mock Prisma response
    (prisma.project.findUnique as jest.Mock).mockResolvedValue({
      id: 'project_123',
      name: 'Test Project',
      description: 'A test project',
      allowedOrigins: ['https://example.com'],
      isActive: true,
    });

    // Call the endpoint
    await projectPUT(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Bad Request',
      }),
      { status: 400 }
    );
  });
});

describe('DELETE /api/admin/projects/[id]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should deactivate a project', async () => {
    // Mock request
    const request = {
      url: 'http://localhost:3000/api/admin/projects/project_123',
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { id: 'project_123' };

    // Mock Prisma responses
    (prisma.project.findUnique as jest.Mock).mockResolvedValue({
      id: 'project_123',
      name: 'Test Project',
      isActive: true,
    });

    (prisma.project.update as jest.Mock).mockResolvedValue({
      id: 'project_123',
      name: 'Test Project',
      isActive: false,
    });

    // Call the endpoint
    await projectDELETE(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith({
      success: true,
      message: 'Project deactivated successfully',
      project: {
        id: 'project_123',
        name: 'Test Project',
        isActive: false,
      },
    });
  });

  it('should permanently delete a project', async () => {
    // Mock request with permanent=true
    const request = {
      url: 'http://localhost:3000/api/admin/projects/project_123?permanent=true',
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { id: 'project_123' };

    // Mock Prisma responses
    (prisma.project.findUnique as jest.Mock).mockResolvedValue({
      id: 'project_123',
      name: 'Test Project',
    });

    (prisma.project.delete as jest.Mock).mockResolvedValue({});

    // Call the endpoint
    await projectDELETE(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith({
      success: true,
      message: 'Project deleted permanently',
    });
  });

  it('should return 404 when project is not found', async () => {
    // Mock request
    const request = {
      url: 'http://localhost:3000/api/admin/projects/nonexistent_project',
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { id: 'nonexistent_project' };

    // Mock Prisma response
    (prisma.project.findUnique as jest.Mock).mockResolvedValue(null);

    // Call the endpoint
    await projectDELETE(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Not Found',
        message: 'Project not found',
      }),
      { status: 404 }
    );
  });
});