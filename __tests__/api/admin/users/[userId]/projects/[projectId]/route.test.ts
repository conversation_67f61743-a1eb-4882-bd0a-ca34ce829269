import { NextRequest } from 'next/server';
import { GET as userProjectGET, POST as userProjectPOST, DELETE as userProjectDELETE } from '@/app/api/admin/users/[userId]/projects/[projectId]/route';
import { prisma } from '@/lib/prisma';

// Mock Prisma client
jest.mock('@/lib/prisma', () => ({
  prisma: {
    user: {
      findUnique: jest.fn(),
    },
    project: {
      findUnique: jest.fn(),
    },
    scope: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
    userProjectScope: {
      findMany: jest.fn(),
      upsert: jest.fn(),
      findUnique: jest.fn(),
      delete: jest.fn(),
    },
  },
}));

// Mock NextResponse
const mockJson = jest.fn();
const mockNext = jest.fn();
jest.mock('next/server', () => ({
  NextResponse: {
    json: mockJson,
    next: mockNext,
  },
}));

describe('GET /api/admin/users/[userId]/projects/[projectId]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should return user project scopes', async () => {
    // Mock request
    const request = {
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { userId: 'user_123', projectId: 'project_123' };

    // Mock Prisma responses
    (prisma.user.findUnique as jest.Mock).mockResolvedValue({
      id: 'user_123',
      name: 'Test User',
      email: '<EMAIL>',
    });

    (prisma.project.findUnique as jest.Mock).mockResolvedValue({
      id: 'project_123',
      name: 'Test Project',
    });

    (prisma.userProjectScope.findMany as jest.Mock).mockResolvedValue([
      {
        scope: {
          id: 'scope_123',
          name: 'read:users',
          description: 'Read user profile information',
        },
      },
    ]);

    // Call the endpoint
    await userProjectGET(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith({
      user: {
        id: 'user_123',
        name: 'Test User',
        email: '<EMAIL>',
      },
      project: {
        id: 'project_123',
        name: 'Test Project',
      },
      scopes: [
        {
          id: 'scope_123',
          name: 'read:users',
          description: 'Read user profile information',
        },
      ],
    });
  });

  it('should return 404 when user is not found', async () => {
    // Mock request
    const request = {
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { userId: 'nonexistent_user', projectId: 'project_123' };

    // Mock Prisma response
    (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

    // Call the endpoint
    await userProjectGET(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Not Found',
        message: 'User not found',
      }),
      { status: 404 }
    );
  });

  it('should return 404 when project is not found', async () => {
    // Mock request
    const request = {
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { userId: 'user_123', projectId: 'nonexistent_project' };

    // Mock Prisma responses
    (prisma.user.findUnique as jest.Mock).mockResolvedValue({
      id: 'user_123',
      name: 'Test User',
      email: '<EMAIL>',
    });

    (prisma.project.findUnique as jest.Mock).mockResolvedValue(null);

    // Call the endpoint
    await userProjectGET(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Not Found',
        message: 'Project not found',
      }),
      { status: 404 }
    );
  });
});

describe('POST /api/admin/users/[userId]/projects/[projectId]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should assign scopes to user for project', async () => {
    // Mock request
    const request = {
      json: jest.fn().mockResolvedValue({
        scopeIds: ['scope_123', 'scope_456'],
      }),
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { userId: 'user_123', projectId: 'project_123' };

    // Mock Prisma responses
    (prisma.user.findUnique as jest.Mock).mockResolvedValue({
      id: 'user_123',
    });

    (prisma.project.findUnique as jest.Mock).mockResolvedValue({
      id: 'project_123',
    });

    (prisma.scope.findMany as jest.Mock).mockResolvedValue([
      { id: 'scope_123', name: 'read:users' },
      { id: 'scope_456', name: 'write:users' },
    ]);

    (prisma.userProjectScope.upsert as jest.Mock).mockResolvedValue({
      scope: {
        name: 'read:users',
      },
    });

    // Call the endpoint
    await userProjectPOST(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith({
      success: true,
      grantedScopes: ['read:users'],
      message: 'Scopes granted successfully',
    });
  });

  it('should return 404 when user is not found', async () => {
    // Mock request
    const request = {
      json: jest.fn().mockResolvedValue({
        scopeIds: ['scope_123'],
      }),
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { userId: 'nonexistent_user', projectId: 'project_123' };

    // Mock Prisma response
    (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

    // Call the endpoint
    await userProjectPOST(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Not Found',
        message: 'User not found',
      }),
      { status: 404 }
    );
  });

  it('should return 404 when some scopes are not found', async () => {
    // Mock request
    const request = {
      json: jest.fn().mockResolvedValue({
        scopeIds: ['scope_123', 'nonexistent_scope'],
      }),
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { userId: 'user_123', projectId: 'project_123' };

    // Mock Prisma responses
    (prisma.user.findUnique as jest.Mock).mockResolvedValue({
      id: 'user_123',
    });

    (prisma.project.findUnique as jest.Mock).mockResolvedValue({
      id: 'project_123',
    });

    (prisma.scope.findMany as jest.Mock).mockResolvedValue([
      { id: 'scope_123', name: 'read:users' },
      // 'nonexistent_scope' is missing
    ]);

    // Call the endpoint
    await userProjectPOST(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Not Found',
        message: 'Some scopes not found',
      }),
      { status: 404 }
    );
  });

  it('should return 400 for invalid request body', async () => {
    // Mock request with invalid body
    const request = {
      json: jest.fn().mockResolvedValue({
        scopeIds: 'invalid', // Should be array
      }),
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { userId: 'user_123', projectId: 'project_123' };

    // Call the endpoint
    await userProjectPOST(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Bad Request',
      }),
      { status: 400 }
    );
  });
});

describe('DELETE /api/admin/users/[userId]/projects/[projectId]/scopes/[scopeId]', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should revoke a scope from user for project', async () => {
    // Mock request
    const request = {
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { userId: 'user_123', projectId: 'project_123', scopeId: 'scope_123' };

    // Mock Prisma responses
    (prisma.user.findUnique as jest.Mock).mockResolvedValue({
      id: 'user_123',
    });

    (prisma.project.findUnique as jest.Mock).mockResolvedValue({
      id: 'project_123',
    });

    (prisma.scope.findUnique as jest.Mock).mockResolvedValue({
      id: 'scope_123',
    });

    (prisma.userProjectScope.findUnique as jest.Mock).mockResolvedValue({
      id: 'ups_123',
      userId: 'user_123',
      projectId: 'project_123',
      scopeId: 'scope_123',
    });

    (prisma.userProjectScope.delete as jest.Mock).mockResolvedValue({});

    // Call the endpoint
    await userProjectDELETE(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith({
      success: true,
      message: 'Scope revoked successfully',
    });
  });

  it('should return 404 when user-project-scope relationship does not exist', async () => {
    // Mock request
    const request = {
      headers: {
        get: jest.fn(),
      },
    } as unknown as NextRequest;

    const params = { userId: 'user_123', projectId: 'project_123', scopeId: 'scope_123' };

    // Mock Prisma responses
    (prisma.user.findUnique as jest.Mock).mockResolvedValue({
      id: 'user_123',
    });

    (prisma.project.findUnique as jest.Mock).mockResolvedValue({
      id: 'project_123',
    });

    (prisma.scope.findUnique as jest.Mock).mockResolvedValue({
      id: 'scope_123',
    });

    (prisma.userProjectScope.findUnique as jest.Mock).mockResolvedValue(null);

    // Call the endpoint
    await userProjectDELETE(request, { params });

    // Verify response
    expect(mockJson).toHaveBeenCalledWith(
      expect.objectContaining({
        error: 'Not Found',
        message: 'User does not have this scope for this project',
      }),
      { status: 404 }
    );
  });
});