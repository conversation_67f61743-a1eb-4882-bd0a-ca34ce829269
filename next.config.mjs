/** @type {import('next').NextConfig} */
const nextConfig = {
  experimental: {
    optimizePackageImports: ['lucide-react', '@radix-ui/react-icons'],
  },

  webpack: (config, { dev, isServer }) => {
    if (dev) {
      config.watchOptions = {
        ignored: [
          '**/node_modules/**',
          '**/.git/**',
          '**/.next/**',
          '**/backup/**',
          '**/dist/**',
          '**/build/**',
          '**/.env*',
          '**/README.md',
          '**/*.log',
          '**/coverage/**',
          '**/.nyc_output/**',
          '**/tmp/**',
          '**/temp/**',
          '**/.agent-os/**',
          '**/package-lock.json',
          '**/yarn.lock',
          '**/pnpm-lock.yaml',
        ],
        aggregateTimeout: 600,
        poll: false,
        followSymlinks: false,
      };
    }
    return config;
  },

  images: {
    domains: ['images.unsplash.com'],
    formats: ['image/webp', 'image/avif'],
  },

  productionBrowserSourceMaps: false,
  output: 'standalone',

  serverRuntimeConfig: {
    port: process.env.PORT || 3005,
  },
  publicRuntimeConfig: {
    port: process.env.PORT || 3005,
  },
};

export default nextConfig; // ✅ Use ES6 export for .mjs file
