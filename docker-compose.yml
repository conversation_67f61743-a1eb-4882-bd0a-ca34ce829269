services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: nwaalliance-postgres
    environment:
      POSTGRES_DB: nwa_portal
      POSTGRES_USER: nwa_user
      POSTGRES_PASSWORD: nwa_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5435:5432"
    networks:
      - nwaalliance-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U nwa_user -d nwa_portal"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis Cache
  redis:
    build:
      context: .
      dockerfile: Dockerfile.redis
    container_name: nwaalliance-redis
    
    volumes:
      - redis_data:/data
    networks:
      - nwaalliance-network
    ports:
      - "6370:6379"
    healthcheck:
      test: ["CMD", "redis-cli", "--user", "newuser", "-a", "redispassword123", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
    
    

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: nwaalliance-minio
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    ports:
      - "9002:9000"
      - "9003:9001"
    volumes:
      - minio_data:/data
    networks:
      - nwaalliance-network
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Next.js Application
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nwaalliance-app
    environment:
      NODE_ENV: production
      DATABASE_URL: ************************************************/nwa_portal
      REDIS_URL: ${REDIS_URL}
      MINIO_ENDPOINT: minio
      MINIO_PORT: 9000
      MINIO_ACCESS_KEY: minioadmin
      MINIO_SECRET_KEY: minioadmin123
      MINIO_BUCKET_NAME: nwa-uploads
      MINIO_USE_SSL: false
      NEXTAUTH_URL: http://localhost:3001
      NEXTAUTH_SECRET: your-super-secret-nextauth-key-change-this-in-production
      PORT: 3000
    command: node server.js
    ports:
      - "3001:3000"
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      minio:
        condition: service_healthy
    networks:
      - nwaalliance-network
    volumes:
      - ./prisma:/app/prisma

volumes:
  postgres_data:
  redis_data:
  minio_data:

networks:
  nwaalliance-network:
    driver: bridge