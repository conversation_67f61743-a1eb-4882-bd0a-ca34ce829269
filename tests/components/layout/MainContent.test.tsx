import React from 'react';
import { render, screen } from '@testing-library/react';
import { MainContent } from '@/components/layout/MainContent';

describe('MainContent', () => {
  it('renders children content correctly', () => {
    const testContent = <div data-testid="test-content">Test Content</div>;
    
    render(<MainContent>{testContent}</MainContent>);

    expect(screen.getByTestId('main-content')).toBeInTheDocument();
    expect(screen.getByTestId('test-content')).toBeInTheDocument();
  });

  it('applies correct CSS classes for responsive padding', () => {
    render(<MainContent><div>Test</div></MainContent>);

    const mainContent = screen.getByTestId('main-content');
    expect(mainContent).toHaveClass('p-4');
    expect(mainContent).toHaveClass('sm:p-5');
    expect(mainContent).toHaveClass('md:p-6');
    expect(mainContent).toHaveClass('lg:p-8');
    expect(mainContent).toHaveClass('xl:p-10');
  });

  it('has max-width container', () => {
    render(<MainContent><div>Test</div></MainContent>);

    const mainContent = screen.getByTestId('main-content');
    const childDiv = mainContent.firstChild as HTMLElement;
    
    expect(childDiv).toHaveClass('max-w-7xl');
    expect(childDiv).toHaveClass('mx-auto');
  });
});