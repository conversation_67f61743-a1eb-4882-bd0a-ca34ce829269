import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { Sidebar } from '@/components/layout/Sidebar';

// Mock the Button component from shadcn/ui
jest.mock('@/components/ui/button', () => {
  return {
    Button: ({ children, onClick, ...props }: any) => (
      <button onClick={onClick} {...props}>
        {children}
      </button>
    ),
  };
});

describe('Sidebar', () => {
  const mockToggle = jest.fn();

  beforeEach(() => {
    mockToggle.mockClear();
  });

  it('renders correctly when expanded', () => {
    render(<Sidebar isCollapsed={false} onToggle={mockToggle} />);

    expect(screen.getByTestId('sidebar')).toBeInTheDocument();
    expect(screen.getByText('NWA Portal')).toBeInTheDocument();
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('Ordinances')).toBeInTheDocument();
    expect(screen.getByText('Treaties')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
    expect(screen.getByText('Logout')).toBeInTheDocument();
  });

  it('renders correctly when collapsed', () => {
    render(<Sidebar isCollapsed={true} onToggle={mockToggle} />);

    expect(screen.getByTestId('sidebar')).toBeInTheDocument();
    expect(screen.queryByText('NWA Portal')).not.toBeInTheDocument();
    expect(screen.getByRole('button', { name: /expand sidebar/i })).toBeInTheDocument();
  });

  it('calls onToggle when toggle button is clicked', () => {
    render(<Sidebar isCollapsed={false} onToggle={mockToggle} />);

    const toggleButton = screen.getByRole('button', { name: /collapse sidebar/i });
    fireEvent.click(toggleButton);

    expect(mockToggle).toHaveBeenCalledTimes(1);
  });

  it('shows all navigation items with correct icons', () => {
    render(<Sidebar isCollapsed={false} onToggle={mockToggle} />);

    // Check that all navigation items are present
    expect(screen.getByText('Dashboard')).toBeInTheDocument();
    expect(screen.getByText('Profile')).toBeInTheDocument();
    expect(screen.getByText('Ordinances')).toBeInTheDocument();
    expect(screen.getByText('Treaties')).toBeInTheDocument();
    expect(screen.getByText('Settings')).toBeInTheDocument();
    
    // Check that icons are present (by checking for the icon components)
    const icons = screen.getAllByRole('img', { hidden: true });
    expect(icons.length).toBeGreaterThan(0);
  });

  it('shows logout button in footer', () => {
    render(<Sidebar isCollapsed={false} onToggle={mockToggle} />);

    expect(screen.getByText('Logout')).toBeInTheDocument();
  });
});