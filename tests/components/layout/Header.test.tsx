import React from 'react';
import { render, screen } from '@testing-library/react';
import { Header } from '@/components/layout/Header';

// Mock the Button component from shadcn/ui
jest.mock('@/components/ui/button', () => {
  return {
    Button: ({ children, onClick, ...props }: any) => (
      <button onClick={onClick} {...props}>
        {children}
      </button>
    ),
  };
});

describe('Header', () => {
  it('renders correctly with all components', () => {
    render(
      <Header 
        isSidebarCollapsed={false}
        onToggleSidebar={jest.fn()}
      />
    );

    expect(screen.getByTestId('header')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /expand sidebar/i })).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Search...')).toBeInTheDocument();
  });

  it('shows notification badge with correct count', () => {
    render(
      <Header 
        isSidebarCollapsed={false}
        onToggleSidebar={jest.fn()}
      />
    );

    const notificationBadge = screen.getByText('3');
    expect(notificationBadge).toBeInTheDocument();
    expect(notificationBadge).toHaveClass('rounded-full');
  });

  it('renders user profile icon', () => {
    render(
      <Header 
        isSidebarCollapsed={false}
        onToggleSidebar={jest.fn()}
      />
    );

    expect(screen.getByRole('button', { name: /user profile/i })).toBeInTheDocument();
  });

  it('shows mobile search bar on smaller screens', () => {
    render(
      <Header 
        isSidebarCollapsed={false}
        onToggleSidebar={jest.fn()}
      />
    );

    // The mobile search bar should be in the document
    const mobileSearchBars = screen.getAllByPlaceholderText('Search...');
    expect(mobileSearchBars).toHaveLength(2); // One for desktop, one for mobile
  });
});