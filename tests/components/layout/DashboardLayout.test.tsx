import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { DashboardLayout } from '@/components/layout/DashboardLayout';

// Mock child components
jest.mock('@/components/layout/Header', () => {
  return {
    Header: () => <div data-testid="header">Header Component</div>,
  };
});

jest.mock('@/components/layout/Sidebar', () => {
  return {
    Sidebar: ({ isCollapsed }: { isCollapsed: boolean }) => (
      <div data-testid="sidebar">
        Sidebar Component
        <span data-testid="sidebar-state">{isCollapsed ? 'collapsed' : 'expanded'}</span>
      </div>
    ),
  };
});

describe('DashboardLayout', () => {
  const mockChildren = <div data-testid="main-content">Main Content</div>;

  it('renders correctly with all components', () => {
    render(<DashboardLayout>{mockChildren}</DashboardLayout>);

    expect(screen.getByTestId('header')).toBeInTheDocument();
    expect(screen.getByTestId('sidebar')).toBeInTheDocument();
    expect(screen.getByTestId('main-content')).toBeInTheDocument();
  });

  it('toggles sidebar collapse state when collapse button is clicked', () => {
    render(<DashboardLayout>{mockChildren}</DashboardLayout>);

    // Initially sidebar should be expanded
    expect(screen.getByTestId('sidebar-state')).toHaveTextContent('expanded');

    // Toggle sidebar
    const toggleButton = screen.getByRole('button', { name: /toggle sidebar/i });
    fireEvent.click(toggleButton);

    // Sidebar should now be collapsed
    expect(screen.getByTestId('sidebar-state')).toHaveTextContent('collapsed');
  });

  it('maintains sidebar state across re-renders', () => {
    const { rerender } = render(<DashboardLayout>{mockChildren}</DashboardLayout>);

    // Initially sidebar should be expanded
    expect(screen.getByTestId('sidebar-state')).toHaveTextContent('expanded');

    // Toggle sidebar
    const toggleButton = screen.getByRole('button', { name: /toggle sidebar/i });
    fireEvent.click(toggleButton);

    // Sidebar should now be collapsed
    expect(screen.getByTestId('sidebar-state')).toHaveTextContent('collapsed');

    // Re-render the component
    rerender(<DashboardLayout>{mockChildren}</DashboardLayout>);

    // Sidebar should still be collapsed
    expect(screen.getByTestId('sidebar-state')).toHaveTextContent('collapsed');
  });

  it('applies correct CSS classes for layout structure', () => {
    render(<DashboardLayout>{mockChildren}</DashboardLayout>);

    const layoutContainer = screen.getByTestId('dashboard-layout');
    expect(layoutContainer).toHaveClass('flex', 'h-screen', 'bg-gray-50');
  });

  it('applies correct CSS classes for collapsed sidebar', () => {
    render(<DashboardLayout>{mockChildren}</DashboardLayout>);

    // Toggle sidebar to collapse it
    const toggleButton = screen.getByRole('button', { name: /toggle sidebar/i });
    fireEvent.click(toggleButton);

    const sidebar = screen.getByTestId('sidebar');
    expect(sidebar).toHaveClass('collapsed');
  });
});