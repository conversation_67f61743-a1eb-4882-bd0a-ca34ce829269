import { PrismaClient } from '@prisma/client'

const prisma = new PrismaClient()

describe('Database Schema Tests', () => {
  beforeAll(async () => {
    // Connect to test database
    await prisma.$connect()
  })

  afterAll(async () => {
    // Clean up and disconnect
    await prisma.$disconnect()
  })

  describe('NextAuth.js Models', () => {
    test('should create and retrieve user account', async () => {
      const testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
        },
      })

      expect(testUser.id).toBeDefined()
      expect(testUser.email).toBe('<EMAIL>')
      expect(testUser.name).toBe('Test User')

      // Clean up
      await prisma.user.delete({ where: { id: testUser.id } })
    })

    test('should create account with provider relationship', async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'OAuth User',
        },
      })

      const account = await prisma.account.create({
        data: {
          userId: user.id,
          type: 'oauth',
          provider: 'google',
          providerAccountId: '12345',
        },
      })

      expect(account.provider).toBe('google')
      expect(account.userId).toBe(user.id)

      // Clean up
      await prisma.account.delete({ where: { id: account.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })

    test('should create session with user relationship', async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Session User',
        },
      })

      const session = await prisma.session.create({
        data: {
          userId: user.id,
          sessionToken: 'test-session-token',
          expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
        },
      })

      expect(session.sessionToken).toBe('test-session-token')
      expect(session.userId).toBe(user.id)

      // Clean up
      await prisma.session.delete({ where: { id: session.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })
  })

  describe('Role and Permission Models', () => {
    test('should create role with permissions', async () => {
      const role = await prisma.role.create({
        data: {
          name: 'test-role',
          description: 'Test role for validation',
          isSystem: false,
        },
      })

      const permission = await prisma.permission.create({
        data: {
          name: 'test:read',
          resource: 'test',
          action: 'read',
          description: 'Test permission',
        },
      })

      const rolePermission = await prisma.rolePermission.create({
        data: {
          roleId: role.id,
          permissionId: permission.id,
        },
      })

      expect(rolePermission.roleId).toBe(role.id)
      expect(rolePermission.permissionId).toBe(permission.id)

      // Clean up
      await prisma.rolePermission.delete({ where: { id: rolePermission.id } })
      await prisma.permission.delete({ where: { id: permission.id } })
      await prisma.role.delete({ where: { id: role.id } })
    })
  })

  describe('User Profile and Position Models', () => {
    test('should create user with profile and positions', async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Profile User',
        },
      })

      const profile = await prisma.userProfile.create({
        data: {
          userId: user.id,
          nwaEmail: '<EMAIL>',
          country: 'USA',
          city: 'Test City',
          mobile: '555-1234',
          bio: 'Test bio',
        },
      })

      const position = await prisma.position.create({
        data: {
          title: 'Test Position',
          description: 'Test position for validation',
          level: 1,
        },
      })

      const userPosition = await prisma.userPosition.create({
        data: {
          userId: user.id,
          positionId: position.id,
          startDate: new Date(),
        },
      })

      expect(profile.userId).toBe(user.id)
      expect(userPosition.userId).toBe(user.id)
      expect(userPosition.positionId).toBe(position.id)

      // Clean up
      await prisma.userPosition.delete({ where: { id: userPosition.id } })
      await prisma.position.delete({ where: { id: position.id } })
      await prisma.userProfile.delete({ where: { id: profile.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })
  })

  describe('Ordinance and Treaty Models', () => {
    test('should create ordinance with type and user relationship', async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Ordinance User',
        },
      })

      const ordinanceType = await prisma.ordinanceType.create({
        data: {
          name: 'Test Ordinance',
          description: 'Test ordinance type',
          category: 'test',
        },
      })

      const ordinance = await prisma.ordinance.create({
        data: {
          userId: user.id,
          ordinanceTypeId: ordinanceType.id,
          completedDate: new Date(),
          status: 'COMPLETED',
        },
      })

      expect(ordinance.userId).toBe(user.id)
      expect(ordinance.ordinanceTypeId).toBe(ordinanceType.id)
      expect(ordinance.status).toBe('completed')

      // Clean up
      await prisma.ordinance.delete({ where: { id: ordinance.id } })
      await prisma.ordinanceType.delete({ where: { id: ordinanceType.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })
  })

  describe('Audit Log Model', () => {
    test('should create audit log entry', async () => {
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Audit User',
        },
      })

      const auditLog = await prisma.auditLog.create({
        data: {
          userId: user.id,
          action: 'CREATE',
          resource: 'user',
          resourceId: user.id,
          oldValues: {},
          newValues: { email: '<EMAIL>', name: 'Audit User' },
          ipAddress: '127.0.0.1',
          userAgent: 'test-agent',
          success: true,
          apiEndpoint: '/test',
          requestMethod: 'POST',
        },
      })

      expect(auditLog.userId).toBe(user.id)
      expect(auditLog.action).toBe('CREATE')
      expect(auditLog.resource).toBe('user')

      // Clean up
      await prisma.auditLog.delete({ where: { id: auditLog.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })
  })

  describe('External API Authentication Models', () => {
    test('should create and retrieve project with API key hash', async () => {
      const project = await prisma.project.create({
        data: {
          name: 'Test Project',
          description: 'Test project for API authentication',
          apiKeyHash: 'test_api_key_hash_12345',
          allowedOrigins: ['https://example.com', 'https://test.com'],
          isActive: true,
        },
      })

      expect(project.id).toBeDefined()
      expect(project.name).toBe('Test Project')
      expect(project.apiKeyHash).toBe('test_api_key_hash_12345')
      expect(project.allowedOrigins).toEqual(['https://example.com', 'https://test.com'])
      expect(project.isActive).toBe(true)

      // Clean up
      await prisma.project.delete({ where: { id: project.id } })
    })

    test('should create and retrieve scope', async () => {
      const scope = await prisma.scope.create({
        data: {
          name: 'read:users',
          description: 'Read user profile information',
          category: 'users',
          isActive: true,
        },
      })

      expect(scope.id).toBeDefined()
      expect(scope.name).toBe('read:users')
      expect(scope.description).toBe('Read user profile information')
      expect(scope.category).toBe('users')
      expect(scope.isActive).toBe(true)

      // Clean up
      await prisma.scope.delete({ where: { id: scope.id } })
    })

    test('should create user-project-scope relationship', async () => {
      // Create test user
      const user = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Scope User',
        },
      })

      // Create test project
      const project = await prisma.project.create({
        data: {
          name: 'Scope Test Project',
          description: 'Project for scope testing',
          apiKeyHash: 'scope_test_api_key_hash_12345',
          allowedOrigins: ['https://example.com'],
          isActive: true,
        },
      })

      // Create test scope
      const scope = await prisma.scope.create({
        data: {
          name: 'write:profiles',
          description: 'Update user profile information',
          category: 'profiles',
          isActive: true,
        },
      })

      // Create user-project-scope relationship
      const userProjectScope = await prisma.userProjectScope.create({
        data: {
          userId: user.id,
          projectId: project.id,
          scopeId: scope.id,
          grantedBy: 'test-admin',
        },
      })

      expect(userProjectScope.id).toBeDefined()
      expect(userProjectScope.userId).toBe(user.id)
      expect(userProjectScope.projectId).toBe(project.id)
      expect(userProjectScope.scopeId).toBe(scope.id)
      expect(userProjectScope.grantedBy).toBe('test-admin')

      // Verify relationships
      const retrievedUserProjectScope = await prisma.userProjectScope.findUnique({
        where: { id: userProjectScope.id },
        include: {
          user: true,
          project: true,
          scope: true,
        },
      })

      expect(retrievedUserProjectScope?.user.email).toBe('<EMAIL>')
      expect(retrievedUserProjectScope?.project.name).toBe('Scope Test Project')
      expect(retrievedUserProjectScope?.scope.name).toBe('write:profiles')

      // Clean up
      await prisma.userProjectScope.delete({ where: { id: userProjectScope.id } })
      await prisma.scope.delete({ where: { id: scope.id } })
      await prisma.project.delete({ where: { id: project.id } })
      await prisma.user.delete({ where: { id: user.id } })
    })

    test('should handle project deactivation', async () => {
      const project = await prisma.project.create({
        data: {
          name: 'Deactivation Test Project',
          description: 'Project for deactivation testing',
          apiKeyHash: 'deactivation_test_api_key_hash_12345',
          allowedOrigins: ['https://example.com'],
          isActive: true,
        },
      })

      // Verify project is active
      expect(project.isActive).toBe(true)

      // Deactivate project
      const updatedProject = await prisma.project.update({
        where: { id: project.id },
        data: { isActive: false },
      })

      expect(updatedProject.isActive).toBe(false)

      // Clean up
      await prisma.project.delete({ where: { id: project.id } })
    })

    test('should handle scope deactivation', async () => {
      const scope = await prisma.scope.create({
        data: {
          name: 'inactive:scope',
          description: 'Inactive scope for testing',
          category: 'test',
          isActive: true,
        },
      })

      // Verify scope is active
      expect(scope.isActive).toBe(true)

      // Deactivate scope
      const updatedScope = await prisma.scope.update({
        where: { id: scope.id },
        data: { isActive: false },
      })

      expect(updatedScope.isActive).toBe(false)

      // Clean up
      await prisma.scope.delete({ where: { id: scope.id } })
    })
  })
})