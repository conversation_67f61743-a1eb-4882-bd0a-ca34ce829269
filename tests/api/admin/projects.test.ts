import { NextRequest } from 'next/server';

// Mock NextResponse before importing the modules that use it
const mockJson = jest.fn();
jest.mock('next/server', () => ({
  NextResponse: {
    json: mockJson,
  },
}));

// Now import the modules
import * as projectRoutes from '@/app/api/admin/projects/route';
import * as projectDetailRoutes from '@/app/api/admin/projects/[id]/route';
import * as userProjectRoutes from '@/app/api/admin/users/[userId]/projects/[projectId]/route';
import { prisma } from '@/lib/prisma';
import { ApiKeyService } from '@/lib/services/api-key';

// Mock Prisma client
jest.mock('@/lib/prisma', () => ({
  prisma: {
    project: {
      findMany: jest.fn(),
      count: jest.fn(),
      create: jest.fn(),
      findUnique: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
    },
    user: {
      findUnique: jest.fn(),
    },
    scope: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
    userProjectScope: {
      findMany: jest.fn(),
      upsert: jest.fn(),
      findUnique: jest.fn(),
      delete: jest.fn(),
    },
  },
}));

// Mock API key service
jest.mock('@/lib/services/api-key', () => ({
  ApiKeyService: jest.fn().mockImplementation(() => ({
    generateApiKey: jest.fn().mockReturnValue('nwa_test_api_key'),
    hashApiKey: jest.fn().mockReturnValue('hashed_api_key'),
  })),
}));

describe('Project Management API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/admin/projects', () => {
    it('should return a list of projects with pagination', async () => {
      // Mock request
      const request = {
        url: 'http://localhost:3000/api/admin/projects?page=1&limit=25',
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      // Mock Prisma responses
      (prisma.project.findMany as jest.Mock).mockResolvedValue([
        {
          id: 'project_123',
          name: 'Test Project',
          description: 'A test project',
          allowedOrigins: ['https://example.com'],
          isActive: true,
          createdAt: new Date(),
        },
      ]);

      (prisma.project.count as jest.Mock).mockResolvedValue(1);

      // Call the endpoint
      await projectRoutes.GET(request);

      // Verify response
      expect(mockJson).toHaveBeenCalledWith({
        projects: [
          {
            id: 'project_123',
            name: 'Test Project',
            description: 'A test project',
            allowedOrigins: ['https://example.com'],
            isActive: true,
            createdAt: expect.any(Date),
          },
        ],
        pagination: {
          page: 1,
          limit: 25,
          total: 1,
        },
      });
    });

    it('should return 400 for invalid pagination parameters', async () => {
      // Mock request with invalid pagination
      const request = {
        url: 'http://localhost:3000/api/admin/projects?page=0&limit=0',
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      // Call the endpoint
      await projectRoutes.GET(request);

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Bad Request',
          message: 'Invalid pagination parameters',
        }),
        { status: 400 }
      );
    });
  });

  describe('POST /api/admin/projects', () => {
    it('should create a new project with an API key', async () => {
      // Mock request
      const request = {
        json: jest.fn().mockResolvedValue({
          name: 'New Project',
          description: 'A new test project',
          allowedOrigins: ['https://newproject.com'],
        }),
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      // Mock Prisma response
      (prisma.project.create as jest.Mock).mockResolvedValue({
        id: 'project_456',
        name: 'New Project',
        description: 'A new test project',
        allowedOrigins: ['https://newproject.com'],
        isActive: true,
        createdAt: new Date(),
      });

      // Call the endpoint
      await projectRoutes.POST(request);

      // Verify response
      expect(mockJson).toHaveBeenCalledWith({
        project: {
          id: 'project_456',
          name: 'New Project',
          description: 'A new test project',
          allowedOrigins: ['https://newproject.com'],
          isActive: true,
          createdAt: expect.any(Date),
          apiKey: 'nwa_test_api_key',
        },
      });
    });

    it('should return 400 for invalid request body', async () => {
      // Mock request with invalid body
      const request = {
        json: jest.fn().mockResolvedValue({
          name: '', // Should not be empty
        }),
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      // Call the endpoint
      await projectRoutes.POST(request);

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Bad Request',
        }),
        { status: 400 }
      );
    });
  });

  describe('GET /api/admin/projects/[id]', () => {
    it('should return project details for valid project ID', async () => {
      // Mock request
      const request = {
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'project_123' });

      // Mock Prisma response
      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_123',
        name: 'Test Project',
        description: 'A test project',
        allowedOrigins: ['https://example.com'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Call the endpoint
      await projectDetailRoutes.GET(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith({
        project: {
          id: 'project_123',
          name: 'Test Project',
          description: 'A test project',
          allowedOrigins: ['https://example.com'],
          isActive: true,
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date),
        },
      });
    });

    it('should return 404 for non-existent project', async () => {
      // Mock request
      const request = {
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'nonexistent' });

      // Mock Prisma response
      (prisma.project.findUnique as jest.Mock).mockResolvedValue(null);

      // Call the endpoint
      await projectDetailRoutes.GET(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Not Found',
          message: 'Project not found',
        }),
        { status: 404 }
      );
    });
  });

  describe('PUT /api/admin/projects/[id]', () => {
    it('should update project details', async () => {
      // Mock request
      const request = {
        json: jest.fn().mockResolvedValue({
          name: 'Updated Project',
          description: 'An updated test project',
        }),
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'project_123' });

      // Mock Prisma responses
      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_123',
      });

      (prisma.project.update as jest.Mock).mockResolvedValue({
        id: 'project_123',
        name: 'Updated Project',
        description: 'An updated test project',
        allowedOrigins: ['https://example.com'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Call the endpoint
      await projectDetailRoutes.PUT(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith({
        project: {
          id: 'project_123',
          name: 'Updated Project',
          description: 'An updated test project',
          allowedOrigins: ['https://example.com'],
          isActive: true,
          createdAt: expect.any(Date),
          updatedAt: expect.any(Date),
        },
      });
    });

    it('should return 404 for updating non-existent project', async () => {
      // Mock request
      const request = {
        json: jest.fn().mockResolvedValue({
          name: 'Updated Project',
        }),
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'nonexistent' });

      // Mock Prisma response
      (prisma.project.findUnique as jest.Mock).mockResolvedValue(null);

      // Call the endpoint
      await projectDetailRoutes.PUT(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Not Found',
          message: 'Project not found',
        }),
        { status: 404 }
      );
    });

    it('should return 400 for invalid request body', async () => {
      // Mock request with invalid body
      const request = {
        json: jest.fn().mockResolvedValue({
          name: '', // Should not be empty
        }),
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'project_123' });

      // Mock Prisma response
      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_123',
      });

      // Call the endpoint
      await projectDetailRoutes.PUT(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Bad Request',
        }),
        { status: 400 }
      );
    });
  });

  describe('DELETE /api/admin/projects/[id]', () => {
    it('should deactivate project by default', async () => {
      // Mock request
      const request = {
        url: 'http://localhost:3000/api/admin/projects/project_123',
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'project_123' });

      // Mock Prisma responses
      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_123',
      });

      (prisma.project.update as jest.Mock).mockResolvedValue({
        id: 'project_123',
        name: 'Test Project',
        isActive: false,
      });

      // Call the endpoint
      await projectDetailRoutes.DELETE(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        message: 'Project deactivated successfully',
        project: {
          id: 'project_123',
          name: 'Test Project',
          isActive: false,
        },
      });
    });

    it('should permanently delete project when permanent=true', async () => {
      // Mock request
      const request = {
        url: 'http://localhost:3000/api/admin/projects/project_123?permanent=true',
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'project_123' });

      // Mock Prisma responses
      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_123',
      });

      (prisma.project.delete as jest.Mock).mockResolvedValue({});

      // Call the endpoint
      await projectDetailRoutes.DELETE(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        message: 'Project deleted permanently',
      });
    });

    it('should return 404 for deleting non-existent project', async () => {
      // Mock request
      const request = {
        url: 'http://localhost:3000/api/admin/projects/nonexistent',
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ id: 'nonexistent' });

      // Mock Prisma response
      (prisma.project.findUnique as jest.Mock).mockResolvedValue(null);

      // Call the endpoint
      await projectDetailRoutes.DELETE(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Not Found',
          message: 'Project not found',
        }),
        { status: 404 }
      );
    });
  });
});

describe('User Project Scopes API', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/admin/users/[userId]/projects/[projectId]', () => {
    it('should return user, project, and scopes information', async () => {
      // Mock request
      const request = {
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ userId: 'user_123', projectId: 'project_456' });

      // Mock Prisma responses
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'user_123',
        name: 'Test User',
        email: '<EMAIL>',
      });

      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_456',
        name: 'Test Project',
      });

      (prisma.userProjectScope.findMany as jest.Mock).mockResolvedValue([
        {
          scope: {
            id: 'scope_789',
            name: 'read:users',
            description: 'Read user information',
          },
        },
        {
          scope: {
            id: 'scope_790',
            name: 'write:users',
            description: 'Write user information',
          },
        },
      ]);

      // Call the endpoint
      await userProjectRoutes.GET(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith({
        user: {
          id: 'user_123',
          name: 'Test User',
          email: '<EMAIL>',
        },
        project: {
          id: 'project_456',
          name: 'Test Project',
        },
        scopes: [
          {
            id: 'scope_789',
            name: 'read:users',
            description: 'Read user information',
          },
          {
            id: 'scope_790',
            name: 'write:users',
            description: 'Write user information',
          },
        ],
      });
    });

    it('should return 404 for non-existent user', async () => {
      // Mock request
      const request = {
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ userId: 'nonexistent', projectId: 'project_456' });

      // Mock Prisma response
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

      // Call the endpoint
      await userProjectRoutes.GET(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Not Found',
          message: 'User not found',
        }),
        { status: 404 }
      );
    });

    it('should return 404 for non-existent project', async () => {
      // Mock request
      const request = {
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ userId: 'user_123', projectId: 'nonexistent' });

      // Mock Prisma responses
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'user_123',
      });

      (prisma.project.findUnique as jest.Mock).mockResolvedValue(null);

      // Call the endpoint
      await userProjectRoutes.GET(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Not Found',
          message: 'Project not found',
        }),
        { status: 404 }
      );
    });
  });

  describe('POST /api/admin/users/[userId]/projects/[projectId]', () => {
    it('should assign scopes to user for project', async () => {
      // Mock request
      const request = {
        json: jest.fn().mockResolvedValue({
          scopeIds: ['scope_789', 'scope_790'],
        }),
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ userId: 'user_123', projectId: 'project_456' });

      // Mock Prisma responses
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'user_123',
      });

      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_456',
      });

      (prisma.scope.findMany as jest.Mock).mockResolvedValue([
        { id: 'scope_789' },
        { id: 'scope_790' },
      ]);

      (prisma.userProjectScope.upsert as jest.Mock).mockImplementation(({ include }) => ({
        scope: {
          name: include.scope.select.name ? 'test_scope' : undefined,
        },
      }));

      // Call the endpoint
      await userProjectRoutes.POST(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        grantedScopes: ['test_scope', 'test_scope'],
        message: 'Scopes granted successfully',
      });
    });

    it('should return 404 for non-existent user', async () => {
      // Mock request
      const request = {
        json: jest.fn().mockResolvedValue({
          scopeIds: ['scope_789'],
        }),
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ userId: 'nonexistent', projectId: 'project_456' });

      // Mock Prisma response
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

      // Call the endpoint
      await userProjectRoutes.POST(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Not Found',
          message: 'User not found',
        }),
        { status: 404 }
      );
    });

    it('should return 404 for non-existent project', async () => {
      // Mock request
      const request = {
        json: jest.fn().mockResolvedValue({
          scopeIds: ['scope_789'],
        }),
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ userId: 'user_123', projectId: 'nonexistent' });

      // Mock Prisma responses
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'user_123',
      });

      (prisma.project.findUnique as jest.Mock).mockResolvedValue(null);

      // Call the endpoint
      await userProjectRoutes.POST(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Not Found',
          message: 'Project not found',
        }),
        { status: 404 }
      );
    });

    it('should return 404 for non-existent scope', async () => {
      // Mock request
      const request = {
        json: jest.fn().mockResolvedValue({
          scopeIds: ['nonexistent'],
        }),
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ userId: 'user_123', projectId: 'project_456' });

      // Mock Prisma responses
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'user_123',
      });

      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_456',
      });

      (prisma.scope.findMany as jest.Mock).mockResolvedValue([]);

      // Call the endpoint
      await userProjectRoutes.POST(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Not Found',
          message: 'Some scopes not found',
        }),
        { status: 404 }
      );
    });

    it('should return 400 for invalid request body', async () => {
      // Mock request with invalid body
      const request = {
        json: jest.fn().mockResolvedValue({
          scopeIds: 'invalid', // Should be an array
        }),
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ userId: 'user_123', projectId: 'project_456' });

      // Call the endpoint
      await userProjectRoutes.POST(request, { params });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Bad Request',
        }),
        { status: 400 }
      );
    });
  });

  describe('DELETE /api/admin/users/[userId]/projects/[projectId]/[scopeId]', () => {
    it('should revoke scope from user for project', async () => {
      // Mock request
      const request = {
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      const params = Promise.resolve({ 
        userId: 'user_123', 
        projectId: 'project_456',
        scopeId: 'scope_789'
      });

      // Mock Prisma responses
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'user_123',
      });

      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_456',
      });

      (prisma.scope.findUnique as jest.Mock).mockResolvedValue({
        id: 'scope_789',
      });

      (prisma.userProjectScope.findUnique as jest.Mock).mockResolvedValue({
        userId: 'user_123',
        projectId: 'project_456',
        scopeId: 'scope_789',
      });

      (prisma.userProjectScope.delete as jest.Mock).mockResolvedValue({});

      // Call the endpoint
      await userProjectRoutes.DELETE(request, { params: Promise.resolve({ 
        userId: 'user_123', 
        projectId: 'project_456',
        scopeId: 'scope_789'
      }) });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith({
        success: true,
        message: 'Scope revoked successfully',
      });
    });

    it('should return 404 for non-existent user', async () => {
      // Mock request
      const request = {
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      // Mock Prisma response
      (prisma.user.findUnique as jest.Mock).mockResolvedValue(null);

      // Call the endpoint
      await userProjectRoutes.DELETE(request, { params: Promise.resolve({ 
        userId: 'nonexistent', 
        projectId: 'project_456',
        scopeId: 'scope_789'
      }) });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Not Found',
          message: 'User not found',
        }),
        { status: 404 }
      );
    });

    it('should return 404 for non-existent project', async () => {
      // Mock request
      const request = {
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      // Mock Prisma responses
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'user_123',
      });

      (prisma.project.findUnique as jest.Mock).mockResolvedValue(null);

      // Call the endpoint
      await userProjectRoutes.DELETE(request, { params: Promise.resolve({ 
        userId: 'user_123', 
        projectId: 'nonexistent',
        scopeId: 'scope_789'
      }) });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Not Found',
          message: 'Project not found',
        }),
        { status: 404 }
      );
    });

    it('should return 404 for non-existent scope', async () => {
      // Mock request
      const request = {
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      // Mock Prisma responses
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'user_123',
      });

      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_456',
      });

      (prisma.scope.findUnique as jest.Mock).mockResolvedValue(null);

      // Call the endpoint
      await userProjectRoutes.DELETE(request, { params: Promise.resolve({ 
        userId: 'user_123', 
        projectId: 'project_456',
        scopeId: 'nonexistent'
      }) });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Not Found',
          message: 'Scope not found',
        }),
        { status: 404 }
      );
    });

    it('should return 404 for non-existent user-project-scope relationship', async () => {
      // Mock request
      const request = {
        headers: {
          get: jest.fn(),
        },
      } as unknown as NextRequest;

      // Mock Prisma responses
      (prisma.user.findUnique as jest.Mock).mockResolvedValue({
        id: 'user_123',
      });

      (prisma.project.findUnique as jest.Mock).mockResolvedValue({
        id: 'project_456',
      });

      (prisma.scope.findUnique as jest.Mock).mockResolvedValue({
        id: 'scope_789',
      });

      (prisma.userProjectScope.findUnique as jest.Mock).mockResolvedValue(null);

      // Call the endpoint
      await userProjectRoutes.DELETE(request, { params: Promise.resolve({ 
        userId: 'user_123', 
        projectId: 'project_456',
        scopeId: 'scope_789'
      }) });

      // Verify response
      expect(mockJson).toHaveBeenCalledWith(
        expect.objectContaining({
          error: 'Not Found',
          message: 'User does not have this scope for this project',
        }),
        { status: 404 }
      );
    });
  });
});