import { jest } from '@jest/globals';

// Import the services
import { ApiKeyService } from '../../src/lib/services/api-key';
import { JwtService } from '../../src/lib/services/jwt';
import { CorsValidationService } from '../../src/lib/services/cors-validation';
import { ScopeValidationService } from '../../src/lib/services/scope-validation';

describe('Middleware Unit Tests', () => {
  describe('ApiKeyService', () => {
    let apiKeyService: ApiKeyService;

    beforeEach(() => {
      apiKeyService = new ApiKeyService();
    });

    it('should generate API key with correct format', () => {
      const apiKey = apiKeyService.generateApiKey();
      
      expect(apiKey).toMatch(/^nwa_[a-zA-Z0-9_-]+$/);
      expect(apiKey.length).toBeGreaterThan(10);
    });

    it('should validate API key format correctly', () => {
      expect(apiKeyService.isValidApiKeyFormat('nwa_validkey123')).toBe(true);
      expect(apiKeyService.isValidApiKeyFormat('invalid-key')).toBe(false);
      expect(apiKeyService.isValidApiKeyFormat('')).toBe(false);
      expect(apiKeyService.isValidApiKeyFormat('nwa_')).toBe(false);
    });

    it('should hash API keys consistently', () => {
      const apiKey = 'nwa_testkey123';
      const hash1 = apiKeyService.hashApiKey(apiKey);
      const hash2 = apiKeyService.hashApiKey(apiKey);
      
      expect(hash1).toBe(hash2);
      expect(hash1).toMatch(/^[a-f0-9]{64}$/); // SHA-256 hex string
    });
  });

  describe('JwtService', () => {
    let jwtService: JwtService;

    beforeEach(() => {
      // Set up environment variables for JWT service
      process.env.JWT_PRIVATE_KEY = `***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;

      process.env.JWT_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqSYjHLSfmXU91GE5Zi7A
hi76eNQclmCUQohqA2duP5SXAWnkT0iK1qWoZGqGw8yGQT/llGwWr40Kby1dYTAL
qiGWRG/OV+xy0/YGcelbxEyU0xTMW1Zxar02O6Nd+Qq7RQ6N5ik1wDTv5RRHKQN0
CAwdlSRswdbtJZFSyZUVs4zBPUTvEIQhIPKumkj70uEjdvW3w7wx0ZMq5Lv9JtKs
otQS4nNewatpORyIgAAe3TUyalBsp4A0IlE7lytYWsL4y5lX3AS7I55UDFm/mr3V
e+bFwZU+36s8LS5Iiwr2XHtzaaQdvPBuhi5oDL1llAx5KB16N1gSjOqp1vEyGhhg
/wIDAQAB
-----END PUBLIC KEY-----`;

      process.env.JWT_ISSUER = 'nwa-api';
      process.env.JWT_AUDIENCE = 'nwa-external';

      jwtService = new JwtService();
    });

    it('should sign and verify JWT tokens', async () => {
      const payload = {
        projectId: 'test-project',
        scopes: ['users:read'],
      };

      const token = await jwtService.signToken(payload);
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');

      const decoded = await jwtService.verifyToken(token);
      expect(decoded.projectId).toBe(payload.projectId);
      expect(decoded.scopes).toEqual(payload.scopes);
      expect(decoded.iss).toBe('nwa-api');
      expect(decoded.aud).toBe('nwa-external');
    });

    it('should extract payload without verification', () => {
      const payload = {
        projectId: 'test-project',
        scopes: ['users:read'],
      };

      // Create a token manually for testing
      const token = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.eyJwcm9qZWN0SWQiOiJ0ZXN0LXByb2plY3QiLCJzY29wZXMiOlsidXNlcnM6cmVhZCJdLCJpc3MiOiJud2EtYXBpIiwiYXVkIjoibndhLWV4dGVybmFsIiwiaWF0IjoxNjkwMDAwMDAwLCJleHAiOjE2OTAwMDM2MDB9.invalid-signature';
      
      const extracted = jwtService.extractPayload(token);
      expect(extracted.projectId).toBe('test-project');
      expect(extracted.scopes).toEqual(['users:read']);
    });
  });

  describe('CorsValidationService', () => {
    let corsService: CorsValidationService;

    beforeEach(() => {
      corsService = new CorsValidationService();
    });

    it('should validate origin format correctly', () => {
      expect(corsService.isValidOrigin('https://example.com')).toBe(true);
      expect(corsService.isValidOrigin('http://localhost:3000')).toBe(true);
      expect(corsService.isValidOrigin('invalid-origin')).toBe(false);
      expect(corsService.isValidOrigin('https://example.com/path')).toBe(false);
    });

    it('should match wildcard patterns correctly', () => {
      expect(corsService.matchesWildcard('https://*.example.com', 'https://app.example.com')).toBe(true);
      expect(corsService.matchesWildcard('https://*.example.com', 'https://api.example.com')).toBe(true);
      expect(corsService.matchesWildcard('https://*.example.com', 'https://other.com')).toBe(false);
      expect(corsService.matchesWildcard('https://*.example.com', 'https://example.com')).toBe(false);
    });

    it('should normalize origins consistently', () => {
      expect(corsService.normalizeOrigin('https://Example.COM')).toBe('https://example.com');
      expect(corsService.normalizeOrigin('https://example.com:443')).toBe('https://example.com');
      expect(corsService.normalizeOrigin('http://example.com:80')).toBe('http://example.com');
    });
  });

  describe('ScopeValidationService', () => {
    let scopeService: ScopeValidationService;

    beforeEach(() => {
      scopeService = new ScopeValidationService();
    });

    it('should parse scope format correctly', () => {
      const scope = scopeService.parseScope('users:read');
      expect(scope.action).toBe('users');
      expect(scope.resource).toBe('read');
      expect(scope.isWildcard).toBe(false);

      const wildcardScope = scopeService.parseScope('admin:*');
      expect(wildcardScope.action).toBe('admin');
      expect(wildcardScope.resource).toBe('*');
      expect(wildcardScope.isWildcard).toBe(true);
    });

    it('should validate scope format', () => {
      expect(scopeService.validateScopeFormat('users:read')).toBe(true);
      expect(scopeService.validateScopeFormat('admin:*')).toBe(true);
      expect(scopeService.validateScopeFormat('invalid-scope')).toBe(false);
      expect(scopeService.validateScopeFormat('users:')).toBe(false);
      expect(scopeService.validateScopeFormat(':read')).toBe(false);
    });

    it('should check permissions with wildcards', () => {
      const grantedScopes = ['admin:*', 'users:read'];
      
      expect(scopeService.hasPermission(grantedScopes, 'admin:users')).toBe(true);
      expect(scopeService.hasPermission(grantedScopes, 'users:read')).toBe(true);
      expect(scopeService.hasPermission(grantedScopes, 'users:write')).toBe(false);
      expect(scopeService.hasPermission(grantedScopes, 'projects:read')).toBe(false);
    });
  });

  describe('Middleware Integration', () => {
    it('should have all required middleware components', () => {
      expect(ApiKeyService).toBeDefined();
      expect(JwtService).toBeDefined();
      expect(CorsValidationService).toBeDefined();
      expect(ScopeValidationService).toBeDefined();
    });

    it('should create service instances without errors', () => {
      expect(() => new ApiKeyService()).not.toThrow();
      expect(() => new CorsValidationService()).not.toThrow();
      expect(() => new ScopeValidationService()).not.toThrow();
      
      // JWT service requires environment variables
      expect(() => new JwtService()).not.toThrow();
    });
  });
});