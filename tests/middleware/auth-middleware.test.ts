import { jest } from '@jest/globals';
import { NextRequest, NextResponse } from 'next/server';
import { ApiKeyService } from '../../src/lib/services/api-key';
import { JwtService } from '../../src/lib/services/jwt';
import { CorsValidationService } from '../../src/lib/services/cors-validation';
import { ScopeValidationService } from '../../src/lib/services/scope-validation';

// Mock the services
jest.mock('../../src/lib/services/api-key');
jest.mock('../../src/lib/services/jwt');
jest.mock('../../src/lib/services/cors-validation');
jest.mock('../../src/lib/services/scope-validation');
jest.mock('../../src/lib/prisma');

// Mock Next.js response methods
const mockJson = jest.fn();
const mockStatus = jest.fn(() => ({ json: mockJson }));
NextResponse.json = jest.fn().mockImplementation((data, init) => ({
  json: () => Promise.resolve(data),
  status: init?.status || 200,
}));

describe('Authentication Middleware', () => {
  let apiKeyService: jest.Mocked<ApiKeyService>;
  let jwtService: jest.Mocked<JwtService>;
  let corsService: jest.Mocked<CorsValidationService>;
  let scopeService: jest.Mocked<ScopeValidationService>;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Setup service mocks
    apiKeyService = new ApiKeyService() as jest.Mocked<ApiKeyService>;
    jwtService = new JwtService() as jest.Mocked<JwtService>;
    corsService = new CorsValidationService() as jest.Mocked<CorsValidationService>;
    scopeService = new ScopeValidationService() as jest.Mocked<ScopeValidationService>;
  });

  describe('API Key Validation Middleware', () => {
    it('should validate API key successfully', async () => {
      const mockRequest = new NextRequest('https://api.example.com/test', {
        headers: {
          'x-api-key': 'nwa_validkey123',
          'origin': 'https://app.example.com',
        },
      });

      apiKeyService.validateApiKey = jest.fn().mockResolvedValue(true);
      apiKeyService.isValidApiKeyFormat = jest.fn().mockReturnValue(true);

      // Mock database response for project lookup
      const mockProject = {
        id: 'project-1',
        name: 'Test Project',
        apiKeyHash: 'hashed-key',
        allowedOrigins: ['https://app.example.com'],
        isActive: true,
      };

      // Test will be implemented with actual middleware
      expect(apiKeyService.isValidApiKeyFormat).toBeDefined();
    });

    it('should reject invalid API key format', async () => {
      const mockRequest = new NextRequest('https://api.example.com/test', {
        headers: {
          'x-api-key': 'invalid-key',
        },
      });

      apiKeyService.isValidApiKeyFormat = jest.fn().mockReturnValue(false);

      // Test will verify rejection
      expect(apiKeyService.isValidApiKeyFormat('invalid-key')).toBe(false);
    });

    it('should reject missing API key', async () => {
      const mockRequest = new NextRequest('https://api.example.com/test', {
        headers: {},
      });

      // Test will verify missing key rejection
      expect(mockRequest.headers.get('x-api-key')).toBeNull();
    });

    it('should handle API key validation errors', async () => {
      const mockRequest = new NextRequest('https://api.example.com/test', {
        headers: {
          'x-api-key': 'nwa_validkey123',
        },
      });

      apiKeyService.validateApiKey = jest.fn().mockRejectedValue(new Error('Validation failed'));

      // Test will verify error handling
      await expect(apiKeyService.validateApiKey('nwa_validkey123', 'hash')).rejects.toThrow('Validation failed');
    });
  });

  describe('JWT Token Verification Middleware', () => {
    it('should verify JWT token successfully', async () => {
      const mockToken = 'eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9...';
      const mockPayload = {
        userId: 'user-1',
        projectId: 'project-1',
        scopes: ['users:read', 'users:write'],
        iss: 'nwa-api',
        aud: 'nwa-external',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600,
      };

      jwtService.verifyToken = jest.fn().mockResolvedValue(mockPayload);

      const result = await jwtService.verifyToken(mockToken);
      expect(result).toEqual(mockPayload);
      expect(jwtService.verifyToken).toHaveBeenCalledWith(mockToken);
    });

    it('should reject expired JWT token', async () => {
      const mockToken = 'expired.jwt.token';
      
      jwtService.verifyToken = jest.fn().mockRejectedValue(new Error('JWT token has expired'));

      await expect(jwtService.verifyToken(mockToken)).rejects.toThrow('JWT token has expired');
    });

    it('should reject invalid JWT token', async () => {
      const mockToken = 'invalid.jwt.token';
      
      jwtService.verifyToken = jest.fn().mockRejectedValue(new Error('Invalid JWT token'));

      await expect(jwtService.verifyToken(mockToken)).rejects.toThrow('Invalid JWT token');
    });

    it('should handle missing JWT token', async () => {
      const mockRequest = new NextRequest('https://api.example.com/test', {
        headers: {},
      });

      expect(mockRequest.headers.get('authorization')).toBeNull();
    });

    it('should handle malformed authorization header', async () => {
      const mockRequest = new NextRequest('https://api.example.com/test', {
        headers: {
          'authorization': 'InvalidFormat token',
        },
      });

      const authHeader = mockRequest.headers.get('authorization');
      expect(authHeader).toBe('InvalidFormat token');
      expect(authHeader?.startsWith('Bearer ')).toBe(false);
    });
  });

  describe('CORS Origin Validation Middleware', () => {
    it('should validate allowed origin successfully', async () => {
      const projectId = 'project-1';
      const origin = 'https://app.example.com';

      corsService.validateOrigin = jest.fn().mockResolvedValue({
        isValid: true,
        matchedOrigin: origin,
      });

      const result = await corsService.validateOrigin(projectId, origin);
      expect(result.isValid).toBe(true);
      expect(result.matchedOrigin).toBe(origin);
    });

    it('should reject disallowed origin', async () => {
      const projectId = 'project-1';
      const origin = 'https://malicious.com';

      corsService.validateOrigin = jest.fn().mockResolvedValue({
        isValid: false,
        matchedOrigin: null,
        reason: 'Origin not in allowed list',
      });

      const result = await corsService.validateOrigin(projectId, origin);
      expect(result.isValid).toBe(false);
      expect(result.reason).toBe('Origin not in allowed list');
    });

    it('should handle wildcard origin matching', async () => {
      const projectId = 'project-1';
      const origin = 'https://subdomain.example.com';

      corsService.validateOrigin = jest.fn().mockResolvedValue({
        isValid: true,
        matchedOrigin: 'https://*.example.com',
      });

      const result = await corsService.validateOrigin(projectId, origin);
      expect(result.isValid).toBe(true);
      expect(result.matchedOrigin).toBe('https://*.example.com');
    });

    it('should reject invalid origin format', async () => {
      const projectId = 'project-1';
      const origin = 'not-a-valid-origin';

      corsService.validateOrigin = jest.fn().mockRejectedValue(new Error('Invalid origin format'));

      await expect(corsService.validateOrigin(projectId, origin)).rejects.toThrow('Invalid origin format');
    });
  });

  describe('Scope Authorization Middleware', () => {
    it('should validate user scopes successfully', async () => {
      const userId = 'user-1';
      const projectId = 'project-1';
      const requiredScopes = ['users:read'];

      scopeService.validateUserScopes = jest.fn().mockResolvedValue({
        isValid: true,
        grantedScopes: ['users:read'],
        missingScopes: [],
      });

      const result = await scopeService.validateUserScopes(userId, projectId, requiredScopes);
      expect(result.isValid).toBe(true);
      expect(result.grantedScopes).toEqual(['users:read']);
      expect(result.missingScopes).toEqual([]);
    });

    it('should reject insufficient scopes', async () => {
      const userId = 'user-1';
      const projectId = 'project-1';
      const requiredScopes = ['users:read', 'users:write'];

      scopeService.validateUserScopes = jest.fn().mockResolvedValue({
        isValid: false,
        grantedScopes: ['users:read'],
        missingScopes: ['users:write'],
      });

      const result = await scopeService.validateUserScopes(userId, projectId, requiredScopes);
      expect(result.isValid).toBe(false);
      expect(result.missingScopes).toEqual(['users:write']);
    });

    it('should validate service scopes', async () => {
      const projectId = 'project-1';
      const requiredScopes = ['service:access'];

      scopeService.validateServiceScopes = jest.fn().mockResolvedValue({
        isValid: true,
        grantedScopes: ['service:access'],
        missingScopes: [],
      });

      const result = await scopeService.validateServiceScopes(projectId, requiredScopes);
      expect(result.isValid).toBe(true);
    });

    it('should handle wildcard scope permissions', async () => {
      const grantedScopes = ['admin:*'];
      const requiredPermission = 'admin:users';

      scopeService.hasPermission = jest.fn().mockReturnValue(true);

      const result = scopeService.hasPermission(grantedScopes, requiredPermission);
      expect(result).toBe(true);
    });
  });

  describe('Rate Limiting Middleware', () => {
    it('should allow requests within rate limit', async () => {
      // Mock rate limiting logic
      const projectId = 'project-1';
      const currentRequests = 10;
      const rateLimit = 100;

      expect(currentRequests).toBeLessThan(rateLimit);
    });

    it('should reject requests exceeding rate limit', async () => {
      // Mock rate limiting logic
      const projectId = 'project-1';
      const currentRequests = 150;
      const rateLimit = 100;

      expect(currentRequests).toBeGreaterThan(rateLimit);
    });

    it('should reset rate limit after time window', async () => {
      // Mock time-based rate limit reset
      const timeWindow = 60000; // 1 minute
      const lastReset = Date.now() - timeWindow - 1000; // Over 1 minute ago
      const currentTime = Date.now();

      expect(currentTime - lastReset).toBeGreaterThan(timeWindow);
    });
  });

  describe('Audit Logging Middleware', () => {
    it('should log successful authentication', async () => {
      const logData = {
        projectId: 'project-1',
        userId: 'user-1',
        action: 'authenticate',
        success: true,
        timestamp: new Date(),
        ipAddress: '***********',
        userAgent: 'Test Agent',
      };

      // Mock audit logging
      expect(logData.success).toBe(true);
      expect(logData.action).toBe('authenticate');
    });

    it('should log failed authentication attempts', async () => {
      const logData = {
        projectId: 'project-1',
        action: 'authenticate',
        success: false,
        reason: 'Invalid API key',
        timestamp: new Date(),
        ipAddress: '***********',
        userAgent: 'Test Agent',
      };

      // Mock audit logging
      expect(logData.success).toBe(false);
      expect(logData.reason).toBe('Invalid API key');
    });

    it('should log scope validation failures', async () => {
      const logData = {
        projectId: 'project-1',
        userId: 'user-1',
        action: 'scope_validation',
        success: false,
        reason: 'Insufficient permissions',
        requiredScopes: ['users:write'],
        grantedScopes: ['users:read'],
        timestamp: new Date(),
      };

      // Mock audit logging
      expect(logData.success).toBe(false);
      expect(logData.reason).toBe('Insufficient permissions');
    });
  });

  describe('Middleware Integration Flow', () => {
    it('should process complete authentication flow successfully', async () => {
      const mockRequest = new NextRequest('https://api.example.com/users', {
        method: 'GET',
        headers: {
          'x-api-key': 'nwa_validkey123',
          'authorization': 'Bearer valid.jwt.token',
          'origin': 'https://app.example.com',
        },
      });

      // Mock successful validation chain
      apiKeyService.validateApiKey = jest.fn().mockResolvedValue(true);
      jwtService.verifyToken = jest.fn().mockResolvedValue({
        userId: 'user-1',
        projectId: 'project-1',
        scopes: ['users:read'],
        iss: 'nwa-api',
        aud: 'nwa-external',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600,
      });
      corsService.validateOrigin = jest.fn().mockResolvedValue({
        isValid: true,
        matchedOrigin: 'https://app.example.com',
      });
      scopeService.validateUserScopes = jest.fn().mockResolvedValue({
        isValid: true,
        grantedScopes: ['users:read'],
        missingScopes: [],
      });

      // Verify all validations would pass
      expect(await apiKeyService.validateApiKey('nwa_validkey123', 'hash')).toBe(true);
      expect(await jwtService.verifyToken('valid.jwt.token')).toBeDefined();
      expect(await corsService.validateOrigin('project-1', 'https://app.example.com')).toMatchObject({
        isValid: true,
      });
      expect(await scopeService.validateUserScopes('user-1', 'project-1', ['users:read'])).toMatchObject({
        isValid: true,
      });
    });

    it('should fail authentication flow on any validation failure', async () => {
      const mockRequest = new NextRequest('https://api.example.com/users', {
        method: 'GET',
        headers: {
          'x-api-key': 'nwa_invalidkey',
          'authorization': 'Bearer valid.jwt.token',
          'origin': 'https://app.example.com',
        },
      });

      // Mock API key validation failure
      apiKeyService.validateApiKey = jest.fn().mockResolvedValue(false);

      // Even if other validations would pass, the flow should fail
      expect(await apiKeyService.validateApiKey('nwa_invalidkey', 'hash')).toBe(false);
    });

    it('should handle service-to-service authentication without user context', async () => {
      const mockRequest = new NextRequest('https://api.example.com/service/status', {
        method: 'GET',
        headers: {
          'x-api-key': 'nwa_servicekey123',
          'origin': 'https://service.example.com',
        },
      });

      // Mock service authentication (no JWT token required)
      apiKeyService.validateApiKey = jest.fn().mockResolvedValue(true);
      corsService.validateOrigin = jest.fn().mockResolvedValue({
        isValid: true,
        matchedOrigin: 'https://service.example.com',
      });
      scopeService.validateServiceScopes = jest.fn().mockResolvedValue({
        isValid: true,
        grantedScopes: ['service:access'],
        missingScopes: [],
      });

      // Verify service authentication flow
      expect(await apiKeyService.validateApiKey('nwa_servicekey123', 'hash')).toBe(true);
      expect(await corsService.validateOrigin('project-1', 'https://service.example.com')).toMatchObject({
        isValid: true,
      });
      expect(await scopeService.validateServiceScopes('project-1', ['service:access'])).toMatchObject({
        isValid: true,
      });
    });
  });
});