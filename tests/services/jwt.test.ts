import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { JwtService, JwtPayload } from '../../src/lib/services/jwt';

describe('JwtService', () => {
  let jwtService: JwtService;

  beforeEach(() => {
    jwtService = new JwtService();
  });

  describe('signToken', () => {
    it('should sign a JWT token with valid payload', async () => {
      const payload: JwtPayload = {
        userId: 'user123',
        projectId: 'project456',
        scopes: ['read:users', 'write:users']
      };

      const token = await jwtService.signToken(payload);
      
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token.split('.')).toHaveLength(3); // JWT has 3 parts
    });

    it('should include standard JWT claims', async () => {
      const payload: JwtPayload = {
        userId: 'user123',
        projectId: 'project456',
        scopes: ['read:users']
      };

      const token = await jwtService.signToken(payload);
      const decoded = await jwtService.verifyToken(token);
      
      expect(decoded.iss).toBe('nwa-api');
      expect(decoded.aud).toBe('nwa-external');
      expect(decoded.iat).toBeDefined();
      expect(decoded.exp).toBeDefined();
      expect(decoded.exp).toBeGreaterThan(decoded.iat);
    });

    it('should set custom expiration time', async () => {
      const payload: JwtPayload = {
        userId: 'user123',
        projectId: 'project456',
        scopes: ['read:users']
      };

      const customExpiresIn = '2h';
      const token = await jwtService.signToken(payload, customExpiresIn);
      const decoded = await jwtService.verifyToken(token);
      
      const expectedExp = decoded.iat + (2 * 60 * 60); // 2 hours in seconds
      expect(decoded.exp).toBeCloseTo(expectedExp, -1); // Allow 10 second tolerance
    });

    it('should handle different payload types', async () => {
      const servicePayload: JwtPayload = {
        projectId: 'project456',
        scopes: ['service:access']
      };

      const userPayload: JwtPayload = {
        userId: 'user123',
        projectId: 'project456',
        scopes: ['read:profile']
      };

      const serviceToken = await jwtService.signToken(servicePayload);
      const userToken = await jwtService.signToken(userPayload);
      
      const decodedService = await jwtService.verifyToken(serviceToken);
      const decodedUser = await jwtService.verifyToken(userToken);
      
      expect(decodedService.userId).toBeUndefined();
      expect(decodedService.projectId).toBe('project456');
      
      expect(decodedUser.userId).toBe('user123');
      expect(decodedUser.projectId).toBe('project456');
    });
  });

  describe('verifyToken', () => {
    it('should verify a valid token', async () => {
      const payload: JwtPayload = {
        userId: 'user123',
        projectId: 'project456',
        scopes: ['read:users', 'write:users']
      };

      const token = await jwtService.signToken(payload);
      const decoded = await jwtService.verifyToken(token);
      
      expect(decoded.userId).toBe(payload.userId);
      expect(decoded.projectId).toBe(payload.projectId);
      expect(decoded.scopes).toEqual(payload.scopes);
    });

    it('should reject an invalid token', async () => {
      const invalidToken = 'invalid.jwt.token';
      
      await expect(jwtService.verifyToken(invalidToken)).rejects.toThrow();
    });

    it('should reject a token with wrong signature', async () => {
      const payload: JwtPayload = {
        userId: 'user123',
        projectId: 'project456',
        scopes: ['read:users']
      };

      const token = await jwtService.signToken(payload);
      const tamperedToken = token.slice(0, -10) + 'tampered123';
      
      await expect(jwtService.verifyToken(tamperedToken)).rejects.toThrow();
    });

    it('should reject an expired token', async () => {
      const payload: JwtPayload = {
        userId: 'user123',
        projectId: 'project456',
        scopes: ['read:users']
      };

      // Create token that expires in 1 second
      const token = await jwtService.signToken(payload, '1s');
      
      // Wait for token to expire
      await new Promise(resolve => setTimeout(resolve, 1100));
      
      await expect(jwtService.verifyToken(token)).rejects.toThrow();
    });

    it('should validate token structure and required fields', async () => {
      const payload: JwtPayload = {
        userId: 'user123',
        projectId: 'project456',
        scopes: ['read:users']
      };

      const token = await jwtService.signToken(payload);
      const decoded = await jwtService.verifyToken(token);
      
      expect(decoded).toHaveProperty('iss');
      expect(decoded).toHaveProperty('aud');
      expect(decoded).toHaveProperty('iat');
      expect(decoded).toHaveProperty('exp');
      expect(decoded).toHaveProperty('projectId');
      expect(decoded).toHaveProperty('scopes');
      expect(Array.isArray(decoded.scopes)).toBe(true);
    });
  });

  describe('isTokenExpired', () => {
    it('should return false for valid token', async () => {
      const payload: JwtPayload = {
        userId: 'user123',
        projectId: 'project456',
        scopes: ['read:users']
      };

      const token = await jwtService.signToken(payload);
      const isExpired = await jwtService.isTokenExpired(token);
      
      expect(isExpired).toBe(false);
    });

    it('should return true for expired token', async () => {
      const payload: JwtPayload = {
        userId: 'user123',
        projectId: 'project456',
        scopes: ['read:users']
      };

      const token = await jwtService.signToken(payload, '1s');
      
      // Wait for token to expire
      await new Promise(resolve => setTimeout(resolve, 1100));
      
      const isExpired = await jwtService.isTokenExpired(token);
      expect(isExpired).toBe(true);
    });

    it('should handle invalid tokens gracefully', async () => {
      const invalidToken = 'invalid.jwt.token';
      
      await expect(jwtService.isTokenExpired(invalidToken)).rejects.toThrow();
    });
  });

  describe('extractPayload', () => {
    it('should extract payload without verification', async () => {
      const payload: JwtPayload = {
        userId: 'user123',
        projectId: 'project456',
        scopes: ['read:users', 'write:users']
      };

      const token = await jwtService.signToken(payload);
      const extracted = jwtService.extractPayload(token);
      
      expect(extracted.userId).toBe(payload.userId);
      expect(extracted.projectId).toBe(payload.projectId);
      expect(extracted.scopes).toEqual(payload.scopes);
    });

    it('should extract payload from expired token', async () => {
      const payload: JwtPayload = {
        userId: 'user123',
        projectId: 'project456',
        scopes: ['read:users']
      };

      const token = await jwtService.signToken(payload, '1s');
      
      // Wait for token to expire
      await new Promise(resolve => setTimeout(resolve, 1100));
      
      // Should still be able to extract payload
      const extracted = jwtService.extractPayload(token);
      expect(extracted.userId).toBe(payload.userId);
    });

    it('should throw for malformed tokens', () => {
      const invalidToken = 'invalid.jwt.token';
      
      expect(() => jwtService.extractPayload(invalidToken)).toThrow();
    });
  });

  describe('error handling', () => {
    it('should handle missing environment variables', () => {
      const originalPrivateKey = process.env.JWT_PRIVATE_KEY;
      delete process.env.JWT_PRIVATE_KEY;
      
      expect(() => new JwtService()).toThrow('JWT_PRIVATE_KEY environment variable is required');
      
      // Restore the environment variable
      process.env.JWT_PRIVATE_KEY = originalPrivateKey;
    });

    it('should handle invalid private key format', () => {
      const originalPrivateKey = process.env.JWT_PRIVATE_KEY;
      process.env.JWT_PRIVATE_KEY = 'invalid-key';
      
      expect(() => new JwtService()).toThrow();
      
      // Restore the environment variable
      process.env.JWT_PRIVATE_KEY = originalPrivateKey;
    });
  });
});