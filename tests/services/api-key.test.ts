import { describe, it, expect, beforeEach } from '@jest/globals';
import { ApiKeyService } from '../../src/lib/services/api-key';

describe('ApiKeyService', () => {
  let apiKeyService: ApiKeyService;

  beforeEach(() => {
    apiKeyService = new ApiKeyService();
  });

  describe('generateApiKey', () => {
    it('should generate a unique API key with correct format', () => {
      const apiKey = apiKeyService.generateApiKey();
      
      expect(apiKey).toBeDefined();
      expect(typeof apiKey).toBe('string');
      expect(apiKey.length).toBeGreaterThan(32);
      expect(apiKey).toMatch(/^nwa_[a-zA-Z0-9_-]+$/);
    });

    it('should generate different API keys on multiple calls', () => {
      const apiKey1 = apiKeyService.generateApiKey();
      const apiKey2 = apiKeyService.generateApiKey();
      
      expect(apiKey1).not.toBe(apiKey2);
    });

    it('should generate API keys with sufficient entropy', () => {
      const keys = new Set();
      for (let i = 0; i < 100; i++) {
        keys.add(apiKeyService.generateApiKey());
      }
      
      expect(keys.size).toBe(100);
    });
  });

  describe('hashApiKey', () => {
    it('should hash an API key consistently', () => {
      const apiKey = 'nwa_test_key_123';
      const hash1 = apiKeyService.hashApiKey(apiKey);
      const hash2 = apiKeyService.hashApiKey(apiKey);
      
      expect(hash1).toBe(hash2);
      expect(hash1).toBeDefined();
      expect(typeof hash1).toBe('string');
      expect(hash1.length).toBeGreaterThan(0);
    });

    it('should produce different hashes for different API keys', () => {
      const apiKey1 = 'nwa_test_key_123';
      const apiKey2 = 'nwa_test_key_456';
      
      const hash1 = apiKeyService.hashApiKey(apiKey1);
      const hash2 = apiKeyService.hashApiKey(apiKey2);
      
      expect(hash1).not.toBe(hash2);
    });

    it('should handle empty or invalid input gracefully', () => {
      expect(() => apiKeyService.hashApiKey('')).toThrow();
      expect(() => apiKeyService.hashApiKey(null as any)).toThrow();
      expect(() => apiKeyService.hashApiKey(undefined as any)).toThrow();
    });
  });

  describe('validateApiKey', () => {
    it('should validate a correct API key against its hash', async () => {
      const apiKey = apiKeyService.generateApiKey();
      const hash = apiKeyService.hashApiKey(apiKey);
      
      const isValid = await apiKeyService.validateApiKey(apiKey, hash);
      expect(isValid).toBe(true);
    });

    it('should reject an incorrect API key', async () => {
      const apiKey = apiKeyService.generateApiKey();
      const wrongKey = apiKeyService.generateApiKey();
      const hash = apiKeyService.hashApiKey(apiKey);
      
      const isValid = await apiKeyService.validateApiKey(wrongKey, hash);
      expect(isValid).toBe(false);
    });

    it('should reject malformed API keys', async () => {
      const hash = apiKeyService.hashApiKey('nwa_valid_key');
      
      const invalidKeys = [
        'invalid_key',
        'nwa_',
        '',
        'not_nwa_prefixed',
        'nwa_key with spaces',
        'nwa_key@invalid'
      ];

      for (const invalidKey of invalidKeys) {
        const isValid = await apiKeyService.validateApiKey(invalidKey, hash);
        expect(isValid).toBe(false);
      }
    });

    it('should handle timing attacks by using constant-time comparison', async () => {
      const apiKey = apiKeyService.generateApiKey();
      const hash = apiKeyService.hashApiKey(apiKey);
      
      // Test with keys of different lengths
      const shortWrongKey = 'nwa_short';
      const longWrongKey = 'nwa_' + 'a'.repeat(100);
      
      const start1 = Date.now();
      await apiKeyService.validateApiKey(shortWrongKey, hash);
      const time1 = Date.now() - start1;
      
      const start2 = Date.now();
      await apiKeyService.validateApiKey(longWrongKey, hash);
      const time2 = Date.now() - start2;
      
      // Times should be relatively similar (within reasonable bounds)
      // This is a basic timing attack protection test
      expect(Math.abs(time1 - time2)).toBeLessThan(50);
    });
  });

  describe('isValidApiKeyFormat', () => {
    it('should validate correct API key format', () => {
      const validKeys = [
        'nwa_abc123',
        'nwa_test_key_with_underscores',
        'nwa_key-with-dashes',
        'nwa_MixedCase123',
        'nwa_' + 'a'.repeat(50)
      ];

      validKeys.forEach(key => {
        expect(apiKeyService.isValidApiKeyFormat(key)).toBe(true);
      });
    });

    it('should reject invalid API key formats', () => {
      const invalidKeys = [
        'invalid_key',
        'nwa_',
        '',
        'not_nwa_prefixed',
        'nwa_key with spaces',
        'nwa_key@invalid',
        'nwa_key#invalid',
        'nwa_key$invalid',
        'NWA_uppercase_prefix'
      ];

      invalidKeys.forEach(key => {
        expect(apiKeyService.isValidApiKeyFormat(key)).toBe(false);
      });
    });
  });
});