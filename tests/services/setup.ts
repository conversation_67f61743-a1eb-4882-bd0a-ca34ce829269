// Test setup for services that don't require database connection
import { jest } from '@jest/globals';

// Mock Prisma client for services tests
jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    $connect: jest.fn(),
    $disconnect: jest.fn(),
    userProjectScope: {
      findMany: jest.fn(),
    },
    scope: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
    project: {
      findUnique: jest.fn(),
    },
  },
}));

// Mock environment variables for JWT tests
process.env.JWT_PRIVATE_KEY = `***************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************`;

process.env.JWT_PUBLIC_KEY = `-----BEGIN PUBLIC KEY-----
MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAqSYjHLSfmXU91GE5Zi7A
hi76eNQclmCUQohqA2duP5SXAWnkT0iK1qWoZGqGw8yGQT/llGwWr40Kby1dYTAL
qiGWRG/OV+xy0/YGcelbxEyU0xTMW1Zxar02O6Nd+Qq7RQ6N5ik1wDTv5RRHKQN0
CAwdlSRswdbtJZFSyZUVs4zBPUTvEIQhIPKumkj70uEjdvW3w7wx0ZMq5Lv9JtKs
otQS4nNewatpORyIgAAe3TUyalBsp4A0IlE7lytYWsL4y5lX3AS7I55UDFm/mr3V
e+bFwZU+36s8LS5Iiwr2XHtzaaQdvPBuhi5oDL1llAx5KB16N1gSjOqp1vEyGhhg
/wIDAQAB
-----END PUBLIC KEY-----`;

process.env.JWT_ISSUER = 'nwa-api';
process.env.JWT_AUDIENCE = 'nwa-external';