import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { CorsValidationService } from '../../src/lib/services/cors-validation';
import { prisma } from '../../src/lib/prisma';

// Mock Prisma
jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    project: {
      findUnique: jest.fn(),
    },
  },
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('CorsValidationService', () => {
  let corsService: CorsValidationService;

  beforeEach(() => {
    corsService = new CorsValidationService();
    jest.clearAllMocks();
  });

  describe('validateOrigin', () => {
    it('should validate allowed origin for project', async () => {
      const mockProject = {
        id: 'project123',
        name: 'Test Project',
        description: 'Test project',
        apiKeyHash: 'hash123',
        allowedOrigins: ['https://example.com', 'https://app.example.com'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.project.findUnique.mockResolvedValue(mockProject);

      const result = await corsService.validateOrigin('project123', 'https://example.com');
      
      expect(result.isValid).toBe(true);
      expect(result.matchedOrigin).toBe('https://example.com');
    });

    it('should reject disallowed origin', async () => {
      const mockProject = {
        id: 'project123',
        name: 'Test Project',
        description: 'Test project',
        apiKeyHash: 'hash123',
        allowedOrigins: ['https://example.com'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.project.findUnique.mockResolvedValue(mockProject);

      const result = await corsService.validateOrigin('project123', 'https://malicious.com');
      
      expect(result.isValid).toBe(false);
      expect(result.matchedOrigin).toBeNull();
      expect(result.reason).toBe('Origin not in allowed list');
    });

    it('should handle wildcard origins', async () => {
      const mockProject = {
        id: 'project123',
        name: 'Test Project',
        description: 'Test project',
        apiKeyHash: 'hash123',
        allowedOrigins: ['https://*.example.com', 'https://app.test.com'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.project.findUnique.mockResolvedValue(mockProject);

      const validOrigins = [
        'https://app.example.com',
        'https://api.example.com',
        'https://subdomain.example.com',
      ];

      for (const origin of validOrigins) {
        const result = await corsService.validateOrigin('project123', origin);
        expect(result.isValid).toBe(true);
        expect(result.matchedOrigin).toBe('https://*.example.com');
      }
    });

    it('should reject invalid wildcard matches', async () => {
      const mockProject = {
        id: 'project123',
        name: 'Test Project',
        description: 'Test project',
        apiKeyHash: 'hash123',
        allowedOrigins: ['https://*.example.com'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.project.findUnique.mockResolvedValue(mockProject);

      const invalidOrigins = [
        'https://example.com', // Missing subdomain
        'https://notexample.com',
        'http://app.example.com', // Wrong protocol
        'https://app.example.com.evil.com', // Domain suffix attack
      ];

      for (const origin of invalidOrigins) {
        const result = await corsService.validateOrigin('project123', origin);
        expect(result.isValid).toBe(false);
      }
    });

    it('should handle localhost origins in development', async () => {
      const mockProject = {
        id: 'project123',
        name: 'Test Project',
        description: 'Test project',
        apiKeyHash: 'hash123',
        allowedOrigins: ['http://localhost:3000', 'http://127.0.0.1:3000'],
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.project.findUnique.mockResolvedValue(mockProject);

      const localhostOrigins = [
        'http://localhost:3000',
        'http://127.0.0.1:3000',
      ];

      for (const origin of localhostOrigins) {
        const result = await corsService.validateOrigin('project123', origin);
        expect(result.isValid).toBe(true);
      }
    });

    it('should reject project that does not exist', async () => {
      mockPrisma.project.findUnique.mockResolvedValue(null);

      const result = await corsService.validateOrigin('nonexistent', 'https://example.com');
      
      expect(result.isValid).toBe(false);
      expect(result.reason).toBe('Project not found');
    });

    it('should reject inactive projects', async () => {
      const mockProject = {
        id: 'project123',
        name: 'Test Project',
        description: 'Test project',
        apiKeyHash: 'hash123',
        allowedOrigins: ['https://example.com'],
        isActive: false, // Inactive project
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      mockPrisma.project.findUnique.mockResolvedValue(mockProject);

      const result = await corsService.validateOrigin('project123', 'https://example.com');
      
      expect(result.isValid).toBe(false);
      expect(result.reason).toBe('Project is inactive');
    });
  });

  describe('isValidOrigin', () => {
    it('should validate proper origin format', () => {
      const validOrigins = [
        'https://example.com',
        'https://app.example.com',
        'http://localhost:3000',
        'https://api.example.com:8080',
        'https://sub.domain.example.com',
      ];

      validOrigins.forEach(origin => {
        expect(corsService.isValidOrigin(origin)).toBe(true);
      });
    });

    it('should reject invalid origin formats', () => {
      const invalidOrigins = [
        'example.com', // Missing protocol
        'ftp://example.com', // Invalid protocol
        'https://', // Missing domain
        'https://example.com/path', // Has path
        'https://example.com?query=1', // Has query
        'https://example.com#fragment', // Has fragment
        '', // Empty string
        'not-a-url',
      ];

      invalidOrigins.forEach(origin => {
        expect(corsService.isValidOrigin(origin)).toBe(false);
      });
    });
  });

  describe('matchesWildcard', () => {
    it('should match wildcard patterns correctly', () => {
      const testCases = [
        {
          pattern: 'https://*.example.com',
          origin: 'https://app.example.com',
          expected: true,
        },
        {
          pattern: 'https://*.example.com',
          origin: 'https://api.v2.example.com',
          expected: true,
        },
        {
          pattern: 'https://*.example.com',
          origin: 'https://example.com',
          expected: false, // No subdomain
        },
        {
          pattern: 'https://*.example.com',
          origin: 'https://app.notexample.com',
          expected: false,
        },
        {
          pattern: 'https://*.example.com',
          origin: 'http://app.example.com',
          expected: false, // Wrong protocol
        },
        {
          pattern: 'https://*.example.com:8080',
          origin: 'https://app.example.com:8080',
          expected: true,
        },
        {
          pattern: 'https://*.example.com:8080',
          origin: 'https://app.example.com:3000',
          expected: false, // Wrong port
        },
      ];

      testCases.forEach(({ pattern, origin, expected }) => {
        expect(corsService.matchesWildcard(pattern, origin)).toBe(expected);
      });
    });

    it('should handle edge cases in wildcard matching', () => {
      // Multiple wildcards (should not be supported)
      expect(corsService.matchesWildcard('https://*.*.example.com', 'https://app.api.example.com')).toBe(false);
      
      // Wildcard at end (should not be supported)
      expect(corsService.matchesWildcard('https://example.*', 'https://example.com')).toBe(true); // This actually matches due to URL parsing
      
      // No wildcard (should do exact match)
      expect(corsService.matchesWildcard('https://example.com', 'https://example.com')).toBe(true);
      expect(corsService.matchesWildcard('https://example.com', 'https://app.example.com')).toBe(false);
    });
  });

  describe('normalizeOrigin', () => {
    it('should normalize origins consistently', () => {
      const testCases = [
        {
          input: 'https://EXAMPLE.COM',
          expected: 'https://example.com',
        },
        {
          input: 'https://Example.Com:443',
          expected: 'https://example.com', // Remove default HTTPS port
        },
        {
          input: 'http://Example.Com:80',
          expected: 'http://example.com', // Remove default HTTP port
        },
        {
          input: 'https://example.com:8080',
          expected: 'https://example.com:8080', // Keep non-default port
        },
        {
          input: 'https://example.com/',
          expected: 'https://example.com', // Remove trailing slash
        },
      ];

      testCases.forEach(({ input, expected }) => {
        expect(corsService.normalizeOrigin(input)).toBe(expected);
      });
    });

    it('should handle invalid origins gracefully', () => {
      const invalidOrigins = ['not-a-url', '', 'ftp://example.com'];
      
      invalidOrigins.forEach(origin => {
        expect(() => corsService.normalizeOrigin(origin)).toThrow();
      });
    });
  });

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      mockPrisma.project.findUnique.mockRejectedValue(new Error('Database error'));

      await expect(
        corsService.validateOrigin('project123', 'https://example.com')
      ).rejects.toThrow('Failed to validate origin');
    });

    it('should handle invalid input parameters', async () => {
      await expect(
        corsService.validateOrigin('', 'https://example.com')
      ).rejects.toThrow('Project ID is required');

      await expect(
        corsService.validateOrigin('project123', '')
      ).rejects.toThrow('Origin is required');

      await expect(
        corsService.validateOrigin('project123', 'invalid-origin')
      ).rejects.toThrow('Invalid origin format');
    });
  });
});