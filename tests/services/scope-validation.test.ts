import { describe, it, expect, beforeEach, jest } from '@jest/globals';
import { ScopeValidationService, ScopePermission } from '../../src/lib/services/scope-validation';
import { prisma } from '../../src/lib/prisma';

// Mock Prisma
jest.mock('../../src/lib/prisma', () => ({
  prisma: {
    userProjectScope: {
      findMany: jest.fn(),
    },
    scope: {
      findMany: jest.fn(),
      findUnique: jest.fn(),
    },
  },
}));

const mockPrisma = prisma as jest.Mocked<typeof prisma>;

describe('ScopeValidationService', () => {
  let scopeService: ScopeValidationService;

  beforeEach(() => {
    scopeService = new ScopeValidationService();
    jest.clearAllMocks();
  });

  describe('validateUserScopes', () => {
    it('should validate user has required scopes for project', async () => {
      const mockUserProjectScopes = [
        {
          id: '1',
          userId: 'user123',
          projectId: 'project456',
          scopeId: 'scope1',
          grantedAt: new Date(),
          grantedBy: 'admin',
          scope: {
            id: 'scope1',
            name: 'read:users',
            description: 'Read user data',
            category: 'users',
            isActive: true,
            createdAt: new Date(),
          },
        },
        {
          id: '2',
          userId: 'user123',
          projectId: 'project456',
          scopeId: 'scope2',
          grantedAt: new Date(),
          grantedBy: 'admin',
          scope: {
            id: 'scope2',
            name: 'write:users',
            description: 'Write user data',
            category: 'users',
            isActive: true,
            createdAt: new Date(),
          },
        },
      ];

      mockPrisma.userProjectScope.findMany.mockResolvedValue(mockUserProjectScopes);

      const result = await scopeService.validateUserScopes(
        'user123',
        'project456',
        ['read:users', 'write:users']
      );

      expect(result.isValid).toBe(true);
      expect(result.grantedScopes).toEqual(['read:users', 'write:users']);
      expect(result.missingScopes).toEqual([]);
    });

    it('should return false when user lacks required scopes', async () => {
      const mockUserProjectScopes = [
        {
          id: '1',
          userId: 'user123',
          projectId: 'project456',
          scopeId: 'scope1',
          grantedAt: new Date(),
          grantedBy: 'admin',
          scope: {
            id: 'scope1',
            name: 'read:users',
            description: 'Read user data',
            category: 'users',
            isActive: true,
            createdAt: new Date(),
          },
        },
      ];

      mockPrisma.userProjectScope.findMany.mockResolvedValue(mockUserProjectScopes);

      const result = await scopeService.validateUserScopes(
        'user123',
        'project456',
        ['read:users', 'write:users', 'delete:users']
      );

      expect(result.isValid).toBe(false);
      expect(result.grantedScopes).toEqual(['read:users']);
      expect(result.missingScopes).toEqual(['write:users', 'delete:users']);
    });

    it('should handle inactive scopes', async () => {
      // The service filters by isActive: true in the query, so inactive scopes won't be returned
      mockPrisma.userProjectScope.findMany.mockResolvedValue([]);

      const result = await scopeService.validateUserScopes(
        'user123',
        'project456',
        ['read:users']
      );

      expect(result.isValid).toBe(false);
      expect(result.grantedScopes).toEqual([]);
      expect(result.missingScopes).toEqual(['read:users']);
    });

    it('should return true for empty required scopes', async () => {
      const result = await scopeService.validateUserScopes(
        'user123',
        'project456',
        []
      );

      expect(result.isValid).toBe(true);
      expect(result.grantedScopes).toEqual([]);
      expect(result.missingScopes).toEqual([]);
    });
  });

  describe('validateServiceScopes', () => {
    it('should validate service-level scopes for project', async () => {
      // Service scopes are typically broader and don't require user-specific validation
      const result = await scopeService.validateServiceScopes(
        'project456',
        ['service:access', 'api:read']
      );

      expect(result.isValid).toBe(true);
      expect(result.grantedScopes).toEqual(['service:access', 'api:read']);
      expect(result.missingScopes).toEqual([]);
    });

    it('should reject invalid service scope patterns', async () => {
      const result = await scopeService.validateServiceScopes(
        'project456',
        ['invalid-scope', 'user:delete'] // user scopes not allowed for service
      );

      expect(result.isValid).toBe(false);
      expect(result.grantedScopes).toEqual([]);
      expect(result.missingScopes).toEqual(['invalid-scope', 'user:delete']);
    });
  });

  describe('hasPermission', () => {
    it('should check if scopes include specific permission', () => {
      const scopes = ['read:users', 'write:users', 'read:projects'];

      expect(scopeService.hasPermission(scopes, 'read:users')).toBe(true);
      expect(scopeService.hasPermission(scopes, 'write:users')).toBe(true);
      expect(scopeService.hasPermission(scopes, 'delete:users')).toBe(false);
    });

    it('should handle wildcard permissions', () => {
      const scopes = ['admin:*', 'read:users'];

      expect(scopeService.hasPermission(scopes, 'admin:users')).toBe(true);
      expect(scopeService.hasPermission(scopes, 'admin:projects')).toBe(true);
      expect(scopeService.hasPermission(scopes, 'write:users')).toBe(false);
    });

    it('should handle hierarchical permissions', () => {
      const scopes = ['users:*', 'read:projects'];

      expect(scopeService.hasPermission(scopes, 'users:read')).toBe(true);
      expect(scopeService.hasPermission(scopes, 'users:write')).toBe(true);
      expect(scopeService.hasPermission(scopes, 'users:delete')).toBe(true);
      expect(scopeService.hasPermission(scopes, 'projects:write')).toBe(false);
    });
  });

  describe('parseScope', () => {
    it('should parse valid scope format', () => {
      const parsed = scopeService.parseScope('read:users');
      
      expect(parsed.action).toBe('read');
      expect(parsed.resource).toBe('users');
      expect(parsed.isWildcard).toBe(false);
    });

    it('should parse wildcard scopes', () => {
      const parsed = scopeService.parseScope('admin:*');
      
      expect(parsed.action).toBe('admin');
      expect(parsed.resource).toBe('*');
      expect(parsed.isWildcard).toBe(true);
    });

    it('should handle resource wildcards', () => {
      const parsed = scopeService.parseScope('users:*');
      
      expect(parsed.action).toBe('users');
      expect(parsed.resource).toBe('*');
      expect(parsed.isWildcard).toBe(true);
    });

    it('should throw for invalid scope format', () => {
      expect(() => scopeService.parseScope('invalid-scope')).toThrow();
      expect(() => scopeService.parseScope('too:many:colons')).toThrow();
      expect(() => scopeService.parseScope('')).toThrow();
    });
  });

  describe('getScopeHierarchy', () => {
    it('should return scope hierarchy for resource', async () => {
      const mockScopes = [
        {
          id: '1',
          name: 'read:users',
          description: 'Read user data',
          category: 'users',
          isActive: true,
          createdAt: new Date(),
        },
        {
          id: '2',
          name: 'write:users',
          description: 'Write user data',
          category: 'users',
          isActive: true,
          createdAt: new Date(),
        },
        {
          id: '3',
          name: 'delete:users',
          description: 'Delete user data',
          category: 'users',
          isActive: true,
          createdAt: new Date(),
        },
      ];

      mockPrisma.scope.findMany.mockResolvedValue(mockScopes);

      const hierarchy = await scopeService.getScopeHierarchy('users');
      
      expect(hierarchy).toHaveLength(3);
      expect(hierarchy.map(s => s.name)).toEqual(['read:users', 'write:users', 'delete:users']);
    });
  });

  describe('validateScopeFormat', () => {
    it('should validate correct scope formats', () => {
      const validScopes = [
        'read:users',
        'write:projects',
        'admin:*',
        'users:*',
        'service:access',
        'api:read'
      ];

      validScopes.forEach(scope => {
        expect(scopeService.validateScopeFormat(scope)).toBe(true);
      });
    });

    it('should reject invalid scope formats', () => {
      const invalidScopes = [
        'invalid-scope',
        'too:many:colons:here',
        '',
        'read:',
        ':users',
        'read users',
        'read@users'
      ];

      invalidScopes.forEach(scope => {
        expect(scopeService.validateScopeFormat(scope)).toBe(false);
      });
    });
  });

  describe('error handling', () => {
    it('should handle database errors gracefully', async () => {
      mockPrisma.userProjectScope.findMany.mockRejectedValue(new Error('Database error'));

      await expect(
        scopeService.validateUserScopes('user123', 'project456', ['read:users'])
      ).rejects.toThrow('Failed to validate user scopes');
    });

    it('should handle invalid input parameters', async () => {
      await expect(
        scopeService.validateUserScopes('', 'project456', ['read:users'])
      ).rejects.toThrow('User ID is required');

      await expect(
        scopeService.validateUserScopes('user123', '', ['read:users'])
      ).rejects.toThrow('Project ID is required');
    });
  });
});