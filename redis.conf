# Redis configuration file for production security

# Bind Redis to all interfaces to allow container communication
bind 0.0.0.0

# Enable protected mode (enabled by default in Redis 3.2+)
protected-mode yes

# Require clients to authenticate using a password
# Note: Password is supplied via command line (--requirepass) from docker-compose using REDIS_PASSWORD

# Rename dangerous commands to prevent accidental misuse
rename-command FL<PERSON><PERSON>B ""
rename-command <PERSON><PERSON><PERSON><PERSON> ""
rename-command <PERSON><PERSON>Y<PERSON> ""
rename-command <PERSON><PERSON><PERSON> ""
rename-command SHUTDOWN ""
rename-command <PERSON><PERSON><PERSON>G ""

# Disable inline commands for security
rename-command EVAL ""

# Set memory limits to prevent DoS attacks
maxmemory 256mb
maxmemory-policy allkeys-lru

# Enable append-only file for data persistence
appendonly yes
appendfilename "appendonly.aof"

# Set append-only file sync policy
appendfsync everysec

# Disable RDB snapshots (using AO<PERSON> instead)
save ""

# Set timeout for client connections
timeout 300

# TCP keepalive
tcp-keepalive 300

# Log level
loglevel notice

# Log file
# logfile /var/log/redis/redis-server.log