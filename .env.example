# Database Configuration
DATABASE_URL="postgresql://username:password@localhost:5432/database_name"

# Redis Configuration
REDIS_URL="redis://:your-redis-password@localhost:6379"

# MinIO Configuration
MINIO_ENDPOINT="localhost"
MINIO_PORT="9000"
MINIO_ACCESS_KEY="your-minio-access-key"
MINIO_SECRET_KEY="your-minio-secret-key"
MINIO_BUCKET_NAME="your-bucket-name"
MINIO_USE_SSL="false"

# NextAuth.js Configuration
NEXTAUTH_URL="http://localhost:3001"
NEXTAUTH_SECRET="your-nextauth-secret-key"

# Application Configuration
NODE_ENV="development"
PORT=3001