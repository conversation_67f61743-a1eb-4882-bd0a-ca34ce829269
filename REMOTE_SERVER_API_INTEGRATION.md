# Remote Server API Integration Documentation

This document outlines the API endpoints, requirements, and specifications for integrating external systems with the NWA Alliance member portal through its OAuth2-based remote server authentication system.

## Overview

The NWA Alliance member portal implements an OAuth2 authorization server that allows external systems to authenticate users and access protected resources. External systems (referred to as "remote servers") must implement specific API endpoints to integrate with this system.

## Required Endpoints for Remote Servers

Remote servers must implement the following endpoint to be compatible with the NWA Alliance member portal:

### 1. Information Endpoint (`/api/info`)

**Method**: GET  
**URL**: `/api/info`  
**Description**: Provides metadata about the remote server including available permissions and roles.

#### Response Format

```json
{
  "name": "string",
  "description": "string",
  "permissions": [
    {
      "name": "string",
      "description": "string"
    }
  ],
  "roles": [
    {
      "name": "string",
      "description": "string",
      "permissions": ["string"]
    }
  ]
}
```

#### Example Response

```json
{
  "name": "Document Management System",
  "description": "A system for managing organizational documents",
  "permissions": [
    {
      "name": "read:documents",
      "description": "Read documents and files"
    },
    {
      "name": "write:documents",
      "description": "Create and edit documents"
    },
    {
      "name": "delete:documents",
      "description": "Delete documents"
    }
  ],
  "roles": [
    {
      "name": "admin",
      "description": "Administrator with full access",
      "permissions": ["read:documents", "write:documents", "delete:documents"]
    },
    {
      "name": "editor",
      "description": "Content editor with document management access",
      "permissions": ["read:documents", "write:documents"]
    },
    {
      "name": "viewer",
      "description": "Read-only access to documents",
      "permissions": ["read:documents"]
    }
  ]
}
```

## OAuth2 Endpoints (Implemented by NWA Alliance Portal)

The NWA Alliance member portal implements the following OAuth2 endpoints for remote server integration:

### 1. Authorization Endpoint

**URL**: `/api/oauth/authorize`  
**Method**: GET  
**Description**: Initiates the OAuth2 authorization code flow.

#### Parameters

| Parameter | Required | Description |
|-----------|----------|-------------|
| client_id | Yes | The client identifier issued to the remote server |
| redirect_uri | Yes | The URI to redirect to after authorization |
| response_type | Yes | Must be "code" for authorization code flow |
| scope | No | Space-separated list of permissions to request |
| state | Recommended | Random string to prevent CSRF attacks |

#### Example Request

```
GET /api/oauth/authorize?
  client_id=abc123&
  redirect_uri=https://example.com/callback&
  response_type=code&
  scope=read:documents%20write:documents&
  state=xyz789
```

#### Response

Redirects to the `redirect_uri` with either:
- Success: `?code=AUTHORIZATION_CODE&state=xyz789`
- Error: `?error=ERROR_CODE&error_description=DESCRIPTION&state=xyz789`

### 2. Token Endpoint

**URL**: `/api/oauth/token`  
**Method**: POST  
**Description**: Exchanges authorization codes for access tokens.

#### Parameters (form-encoded)

| Parameter | Required | Description |
|-----------|----------|-------------|
| grant_type | Yes | Either "authorization_code" or "refresh_token" |
| client_id | Yes | The client identifier |
| client_secret | Yes | The client secret |
| code | Conditional | Authorization code (required for authorization_code grant) |
| redirect_uri | Conditional | Must match the redirect_uri from authorization (required for authorization_code grant) |
| refresh_token | Conditional | Refresh token (required for refresh_token grant) |

#### Example Request (Authorization Code)

```
POST /api/oauth/token
Content-Type: application/x-www-form-urlencoded

grant_type=authorization_code&
client_id=abc123&
client_secret=secret123&
code=AUTH_CODE_FROM_REDIRECT&
redirect_uri=https://example.com/callback
```

#### Example Response

```json
{
  "access_token": "ACCESS_TOKEN",
  "token_type": "Bearer",
  "expires_in": 3600,
  "refresh_token": "REFRESH_TOKEN",
  "scope": "read:documents write:documents"
}
```

### 3. User Info Endpoint

**URL**: `/api/oauth/userinfo`  
**Method**: GET  
**Description**: Retrieves information about the authenticated user.

#### Headers

| Header | Value |
|--------|-------|
| Authorization | Bearer ACCESS_TOKEN |

#### Example Request

```
GET /api/oauth/userinfo
Authorization: Bearer ACCESS_TOKEN
```

#### Example Response

```json
{
  "sub": "user123",
  "name": "John Doe",
  "email": "<EMAIL>",
  "roles": ["admin"],
  "permissions": ["read:documents", "write:documents", "delete:documents"]
}
```

## Authentication Requirements

### 1. Client Registration

Remote servers must first be registered with the NWA Alliance portal through the remote server registration API:

**URL**: `/api/remote-servers`  
**Method**: POST  
**Body**:

```json
{
  "url": "https://your-remote-server.com"
}
```

Upon registration, the system will:
1. Verify the remote server by calling its `/api/info` endpoint
2. Generate client credentials (client_id and client_secret)
3. Return registration details

### 2. OAuth2 Flow Implementation

Remote servers must implement the OAuth2 Authorization Code flow:
1. Redirect users to the authorization endpoint
2. Receive authorization code at the redirect URI
3. Exchange the code for an access token at the token endpoint
4. Use the access token to access protected resources

### 3. Token Usage

All API requests to protected endpoints must include an Authorization header with the Bearer token:

```
Authorization: Bearer ACCESS_TOKEN
```

## Data Structures

### Permission Object

```json
{
  "name": "string",
  "description": "string"
}
```

### Role Object

```json
{
  "name": "string",
  "description": "string",
  "permissions": ["string"]
}
```

### User Object

```json
{
  "sub": "string",          // User ID
  "name": "string",         // User's name
  "email": "string",        // User's email
  "roles": ["string"],      // User's roles
  "permissions": ["string"] // User's permissions
}
```

## Integration Requirements

### 1. Remote Server Implementation

Remote servers must:
1. Implement the `/api/info` endpoint as specified
2. Handle OAuth2 redirects properly
3. Securely store client credentials
4. Validate tokens before processing requests
5. Implement proper error handling

### 2. Security Considerations

- All communication should use HTTPS
- Client secrets must be stored securely
- Authorization codes should be single-use and time-limited
- Access tokens should be short-lived (1 hour)
- Refresh tokens should be long-lived (30 days) but revocable
- Implement proper CSRF protection using the state parameter

### 3. Error Handling

OAuth2 endpoints may return the following errors:

| Error Code | Description |
|------------|-------------|
| invalid_request | Missing or invalid parameters |
| unauthorized_client | Invalid client credentials |
| unsupported_response_type | Unsupported response type |
| invalid_scope | Invalid scope requested |
| server_error | Internal server error |

### 4. Rate Limiting

The API implements rate limiting to prevent abuse. Remote servers should implement exponential backoff when encountering rate limit errors.

## Example Integration Flow

1. **Registration**: Remote server registers with the NWA portal
2. **Authorization**: User is redirected to the portal for authentication
3. **Token Exchange**: Remote server exchanges authorization code for access token
4. **User Info**: Remote server retrieves user information
5. **Resource Access**: Remote server accesses protected resources using the access token

## Supported Scopes

The system supports the following standard scopes:
- `read:profile` - Read user profile information
- `write:profile` - Update user profile information
- `read:documents` - Read documents and files
- `write:documents` - Create and edit documents
- `delete:documents` - Delete documents
- `manage:users` - Manage user accounts
- `view:reports` - View system reports

Additional scopes can be defined by remote servers in their `/api/info` endpoint.

## API Rate Limits

- 100 requests per hour per client
- 10 requests per minute per client (burst limit)

Exceeding these limits will result in a 429 (Too Many Requests) response.