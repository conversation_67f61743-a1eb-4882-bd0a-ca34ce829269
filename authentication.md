Sure! Here's everything neatly packed into **one markdown box** as requested:

```md
# 🔐 Secure External Integration Strategy

This document outlines how to securely connect external projects to a centralized Admin/Member API using:

- ✅ API Keys
- ✅ JWT authentication
- ✅ Trusted Origins (CORS)
- ✅ Scoped access control
- ✅ Environment-based configuration

---

## 1. 🌐 Trusted Origins (CORS)

Each external project defines the Admin API base URL in its `.env`:

```env
ADMIN_API_BASE_URL=https://admin.example.com
```

Your Admin API only allows requests from trusted domains:

```ts
// Express + CORS
const allowedOrigins = [
  'https://project1.com',
  'https://project2.com'
];

app.use(cors({
  origin: (origin, cb) => {
    if (!origin || allowedOrigins.includes(origin)) {
      return cb(null, true);
    }
    return cb(new Error('Not allowed by CORS'));
  },
  credentials: true
}));
```

---

## 2. 🔑 API Keys (Per Project)

Each project gets a unique API key stored in its `.env`:

```env
ADMIN_API_KEY=prj_abc123xyz
```

On your backend, store **hashed API keys** and project metadata:

```ts
{
  id: "project1",
  apiKeyHash: "sha256:...",
  scopes: ["read:users", "read:roles"],
  allowedOrigins: ["https://project1.com"],
  active: true
}
```

### API Call Example:

```http
GET /api/users/123
Authorization: Bearer <JWT>
x-api-key: prj_abc123xyz
```

---

## 3. 🪪 JWT (Authentication)

Used to authenticate either:
- External **users** (who have logged in)
- Or external **projects** (service-to-service communication)

Projects must trust your public key:

```env
ADMIN_JWT_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----...
```

Your service issues JWTs like:

```json
{
  "sub": "user-123",
  "email": "<EMAIL>",
  "permissions": ["view:profile", "edit:profile"],
  "iss": "admin.example.com",
  "aud": "project1.com",
  "exp": 1692128400
}
```

JWT verification:

```ts
const payload = jwt.verify(token, PUBLIC_KEY, { algorithms: ['RS256'] });
```

---

## 4. 🔐 Middleware Flow

1. Validate `x-api-key`
2. Lookup project in DB:
   - Is key valid?
   - Is origin allowed?
   - Are scopes permitted?
3. Validate JWT (`Authorization: Bearer`)
4. Check:
   - Token signature
   - Token expiry
   - Required permissions
5. Proceed or reject
6. Log request for auditing

---

## 5. 📦 External Project `.env` Summary

```env
# External Project Environment Variables
ADMIN_API_BASE_URL=https://admin.example.com
ADMIN_API_KEY=prj_abc123xyz
ADMIN_JWT_PUBLIC_KEY=-----BEGIN PUBLIC KEY-----
```

---

## ✅ Security Tips

- 🔐 Store API keys as hashed values (e.g., SHA-256)
- ⏱️ Use short-lived JWTs (15–60 min) with refresh tokens if needed
- 🔁 Rotate API keys and JWT signing keys periodically
- 🪵 Audit all external API requests (origin, key, user, endpoint)
- 🧾 Apply rate limiting and IP whitelisting per project
- ❌ Do not expose internal errors or stack traces in responses
- ✅ Allow per-project key revocation and permission scoping

---

## 🧩 Optional Add-ons

- 🔄 **Webhook sync** for real-time updates (project registers a webhook URL)
- 📥 **Callback-based integration** for projects needing automatic sync
- 🛂 **Dashboard for managing projects**, keys, and scopes

---

```

Let me know if you'd like this exported as a `.md` file or included in a starter repo!


---

## 🧱 Key Entities

### 🧩 `projects` Table

| Field     | Type   | Description          |
|-----------|--------|----------------------|
| id        | UUID   | Unique project ID    |
| name      | string | Friendly name        |
| api_key   | string | Hashed API key       |
| origin    | string | Trusted origin       |

---

### 👤 `users` Table

| Field     | Type   | Description         |
|-----------|--------|---------------------|
| id        | UUID   | User ID             |
| name      | string | User name           |
| email     | string | User email          |

---

### 🔐 `scopes` Table

| Field       | Type   | Description               |
|-------------|--------|---------------------------|
| id          | UUID   | Scope ID                  |
| name        | string | e.g., `read:users`        |
| description | string | Human-readable label      |

---

### 🔗 `user_project_scopes` Table

| Field        | Type   | Description                        |
|--------------|--------|------------------------------------|
| user_id      | UUID   | References `users.id`              |
| project_id   | UUID   | References `projects.id`           |
| scope_id     | UUID   | References `scopes.id`             |

This table allows you to assign **different scopes for a user per project**.

---

## 📡 API Endpoint

### `GET /api/admin/project/:projectId/user/:userId`

### 🔁 Response Example

```json
{
  "user": {
    "id": "user-xyz789",
    "name": "Jane Peace",
    "email": "<EMAIL>"
  },
  "project": {
    "id": "abc123",
    "name": "Peace Dashboard"
  },
  "scopes": [
    "read:users",
    "edit:profile",
    "view:treaties"
  ]
}
