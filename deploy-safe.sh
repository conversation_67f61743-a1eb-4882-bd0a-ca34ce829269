#!/bin/bash

# NWA Alliance Deployment Script
# Safe deployment with clear local/remote separation

set -e  # Exit on any error

# Configuration
CLIENT_DEPLOY_WEBHOOK="http://***************:3000/api/deploy/NpQsKdeioNH5tYZXbHsAq"
SERVER_DEPLOY_WEBHOOK="http://***************:3000/api/deploy/-wmx4sXLEiIaqyJNZoF2x"
REGISTRY="ghcr.io/darrenedward"
PROJECT_NAME="nwaalliance"
IMAGE_NAME="nwa-member-portal"

# Check deployment mode
DEPLOY_MODE="local"  # default to local for safety
if [ "$1" == "--remote" ]; then
    DEPLOY_MODE="remote"
fi

echo "🚀 NWA Alliance Deployment"
echo "========================"
echo "MODE: $DEPLOY_MODE deployment"
echo ""

# Function to check if a command succeeded
check_command() {
    if [ $? -ne 0 ]; then
        echo "❌ Command failed: $1"
        exit 1
    else
        echo "✅ $1 completed successfully"
    fi
}

# Clean build artifacts
echo "🧹 Cleaning build artifacts..."
echo "Removing .next directory..."
rm -rf .next
check_command ".next removal"

echo "Removing dist directory..."
rm -rf dist
check_command "dist removal"

echo "Removing build directory..."
rm -rf build
check_command "build removal"

echo "Removing standalone directory..."
rm -rf .next/standalone
check_command "standalone removal"

echo "Removing static directory..."
rm -rf .next/static
check_command "static removal"

echo "✅ Build artifacts cleaned successfully"
echo ""

# Rebuild the project
echo "🔨 Rebuilding the project..."
npm run build
check_command "Next.js build"

echo "✅ Project rebuilt successfully"
echo ""

# Build Docker images with no-cache option
echo "🏗️  Building Docker images with no-cache..."
docker build --no-cache \
    -t $REGISTRY/$PROJECT_NAME/$IMAGE_NAME:latest \
    .
check_command "Docker build"

echo "✅ Docker images built successfully"
echo ""

# Local deployment mode
if [ "$DEPLOY_MODE" = "local" ]; then
    echo "🏠 Starting containers locally..."
    docker-compose down
    check_command "Stop existing containers"
    
    docker-compose up -d
    check_command "Start containers"
    
    echo ""
    echo "✅ Local deployment completed successfully!"
    echo "========================================"
    echo "Application available at: http://localhost:3001"
    echo "PostgreSQL database: localhost:5434"
    echo "MinIO dashboard: http://localhost:9003"
    echo ""
    echo "💡 To deploy to remote server, use: ./deploy.sh --remote"
    exit 0
fi

# Remote deployment mode (only if explicitly requested)
if [ "$DEPLOY_MODE" = "remote" ]; then
    echo "🌍 Deploying to remote server..."
    echo "⚠️  WARNING: This will push to production!"
    echo ""
    
    # Confirm deployment
    echo "Are you sure you want to deploy to remote? (yes/no)"
    read -r CONFIRM
    if [ "$CONFIRM" != "yes" ]; then
        echo "Deployment cancelled."
        exit 0
    fi
    
    # Push Docker images to registry
    echo "🚀 Pushing Docker images to registry..."
    echo "Logging in to GitHub Container Registry..."
    echo $GITHUB_TOKEN | docker login ghcr.io -u $GITHUB_USERNAME --password-stdin
    check_command "Docker registry login"

    echo "Pushing image..."
    docker push $REGISTRY/$PROJECT_NAME/$IMAGE_NAME:latest
    check_command "Docker image push"

    echo "✅ Docker images pushed successfully"
    echo ""

    # Show image hashes for comparison
    echo "🔍 Image hashes for verification:"
    echo "Image hash:"
    docker inspect --format='{{index .RepoDigests 0}}' $REGISTRY/$PROJECT_NAME/$IMAGE_NAME:latest 2>/dev/null || echo "Not yet available - will be generated after first push"
    echo ""

    echo "📦 Local image IDs:"
    docker images $REGISTRY/$PROJECT_NAME/$IMAGE_NAME:latest --format "table {{.ID}}\t{{.CreatedAt}}" 2>/dev/null || echo "No local image found"
    echo ""

    # Trigger redeployment on Dokploy server
    echo "🔁 Triggering redeployment on Dokploy server..."
    echo "Triggering client redeploy..."
    curl -X POST $CLIENT_DEPLOY_WEBHOOK
    check_command "Client redeploy trigger"

    echo "Triggering server redeploy..."
    curl -X POST $SERVER_DEPLOY_WEBHOOK
    check_command "Server redeploy trigger"

    echo ""
    echo "✅ Redeployment triggered successfully"
    echo ""

    # Summary
    echo "🎉 Remote deployment finished successfully!"
    echo "========================================="
    echo "1. Build artifacts cleaned"
    echo "2. Project rebuilt"
    echo "3. Docker images built and pushed"
    echo "4. Redeployment triggered on Dokploy server"
    echo ""
    echo "🌐 Application should be available at: http://nwa.cloudns.ch"
    echo "📊 Check container logs on the server to verify deployment"
    exit 0
fi